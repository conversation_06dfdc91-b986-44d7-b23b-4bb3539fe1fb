# API测试接口

## 📝 说明
这个目录包含测试人员用于系统测试和性能评估的HTTP API接口。

## 📁 目录结构

### `test_server.py` (v1.0 兼容接口)
- **用途**: 原版测试服务器，提供HTTP API接口
- **端口**: 5000（默认）
- **认证**: X-API-Key: test_key_123
- **状态**: 保持兼容，建议使用v2.0

### `v2/` (推荐使用)
- **用途**: API v2.0 测试接口
- **端口**: 5001
- **功能**: 离线测试、增强监控、配置管理
- **文档**: ../docs/api_v2.md

### `device_binding/`
- **用途**: 设备绑定专项测试
- **功能**: 绑定流程、签名验证、并发测试

### `stress_testing/`
- **用途**: 压力测试套件
- **功能**: 数据注入、并发API、内存压力测试

### `static/monitor.html`
- **用途**: 实时监控界面
- **访问**: http://localhost:5000/monitor (v1.0) 或 http://localhost:5001/monitor (v2.0)

## 🚀 快速开始

### 启动测试服务器
```bash
# 推荐：启动完整测试套件（v1.0 + v2.0）
python main.py --test
# v1.0: http://localhost:5000
# v2.0: http://localhost:5001

# 或者使用测试启动脚本
python test/start_test_suite.py

# 或者单独启动v1.0测试服务器
python test/api/test_server.py
```

### 运行测试
```bash
# 运行完整测试套件
python test/run_all_tests.py

# 运行特定测试
python test/run_all_tests.py --function-only
python test/run_all_tests.py --performance-only

# 使用curl测试v1.0接口
curl -H "X-API-Key: test_key_123" http://localhost:5000/api/v1/monitor/resources

# 使用curl测试v2.0接口
curl -H "X-API-Key: test_key_123" http://localhost:5001/api/v2/monitor/threads/detail
```

## 📊 主要API接口

### 数据注入接口
- `POST /api/v1/inject/sensor/single` - 单条传感器数据注入
- `POST /api/v1/inject/sensor/batch` - 批量传感器数据注入
- `POST /api/v1/inject/massive` - 大量数据注入

### 监控接口
- `GET /api/v1/monitor/resources` - 系统资源监控
- `GET /api/v1/monitor/queues` - 队列状态监控
- `GET /api/v1/monitor/components` - 组件状态监控

### 故障模拟接口
- `POST /api/v1/simulate/hardware_failure` - 硬件故障模拟
- `POST /api/v1/simulate/mqtt_message` - MQTT消息模拟

## 🔧 配置说明

### API认证
默认API密钥: `test_key_123`
可通过环境变量 `TEST_API_KEY` 修改

### 端口配置
默认端口: 5000
启动时可指定: `python main.py --test --port 8080`

## 📈 性能测试

### 压力测试
```bash
# 运行压力测试套件
python test/api/stress_testing/stress_test_runner.py

# 运行设备绑定测试
python test/api/device_binding/binding_test_suite.py

# 运行性能基准测试
python test/run_all_tests.py --performance-only
```

### 监控测试
```bash
# v2.0增强监控
curl -H "X-API-Key: test_key_123" http://localhost:5001/api/v2/monitor/memory/analysis
curl -H "X-API-Key: test_key_123" http://localhost:5001/api/v2/monitor/queues/health
```

## ⚠️ 注意事项

1. **生产环境**: 测试接口仅用于测试环境，不要在生产环境启用
2. **API密钥**: 生产环境请修改默认API密钥
3. **资源监控**: 长时间测试请注意系统资源使用
4. **数据清理**: 测试完成后注意清理测试数据

## 📞 问题反馈

如果API测试遇到问题：
1. 检查主程序是否正常启动
2. 确认测试接口已启用（--test参数）
3. 验证API密钥是否正确
4. 查看日志文件中的错误信息
