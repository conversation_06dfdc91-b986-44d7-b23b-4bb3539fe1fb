# 蚊子检测系统测试工具

这个工具用于生成蚊子检测测试数据，生成的JSON文件符合蚊子检测数据集成规范，可用于测试蚊子检测文件监控系统。

## 功能特点

- 生成符合规范的蚊子检测JSON数据
- 支持批量生成多个检测文件
- 可控制生成文件的时间间隔
- 自动创建所需目录结构
- 支持Windows和Linux环境

## 使用方法

### 命令行参数

```
python test_generate_detection_data.py [选项]

选项:
  --count COUNT       生成的文件数量 (默认: 1)
  --interval INTERVAL 文件生成间隔(秒) (默认: 2.0)
  --path PATH         共享数据路径 (默认自动判断)
```

### 基本用法

1. 生成单个测试文件:

```bash
python test_generate_detection_data.py
```

2. 生成多个测试文件:

```bash
python test_generate_detection_data.py --count 5
```

3. 指定生成间隔和路径:

```bash
python test_generate_detection_data.py --count 10 --interval 1.0 --path /custom/path/shared_data
```

## 输出示例

生成的JSON文件格式如下:

```json
{
  "devid": "",
  "ver": "2.0",
  "data": {
    "time": 1753349098,
    "picurl": "https://imgdev.wlwise.com/Mosquito-borne/20250724172457_b4c4ebc5.jpg,...",
    "lr": [
      {
        "count": 1,
        "type": "Aedes albopictus",
        "order": 1,
        "idnt": 95,
        "coords": "765,823,1235,1325",
        "track_id": "det_0_20250724_172458"
      },
      {
        "count": 1,
        "type": "Culex pipiens",
        "order": 2,
        "idnt": 87,
        "coords": "1641,1933,2384,2869",
        "track_id": "det_1_20250724_172458"
      }
    ]
  }
}
```

## 故障排查

1. 如果遇到权限问题:
   - 确保当前用户对共享目录有读写权限
   - 在Linux中可能需要调整目录权限: `chmod 755 shared_data shared_data/incoming`

2. 文件未被监控系统处理:
   - 检查JSON文件格式是否正确
   - 检查文件名格式是否正确
   - 检查监控系统是否正在运行

## 注意事项

- 该工具生成的是测试数据，`devid`字段留空，将由监控系统填充
- 在Windows环境下，默认会在当前目录创建shared_data文件夹
- 在Linux环境下，默认路径为`/home/<USER>/shared_data` 