#!/usr/bin/env python3
import time
import sys
import signal
import threading
import logging
import traceback
import Jetson.GPIO as GPIO
from utils.config_loader import load_config
from utils.logger import get_logger
from sensors.sensor_collector import SensorDataCollector
from communication.mqtt_client import MQTTClient, ConnectionState
from devices.co2_controller import CO2Controller
from utils.device_health_check import DeviceHealthCheck
import os
import argparse  # 添加argparse导入

class SensorSystem:
    """传感器系统主类"""
    
    def __init__(self):
        # 初始化日志
        self.logger = get_logger("main", logging.INFO)
        self.logger.info("初始化传感器系统...")
        
        self.config = None
        self.sensor_collector = None
        self.mqtt_client = None
        self.co2_controller = None
        self.device_health = None
        self.running = False
        self.power_status = 1  # 1: 开机, 2: 关机
        
        # 组件状态
        self.component_status = {
            "co2": 1,      # 二氧化碳浓度传感器
            "gps": 1,      # GPS定位
            "hum": 1,      # 湿度传感器
            "temp": 1,     # 温度传感器
            "ws": 1,       # 风速传感器
            "camera": 1,   # 摄像头
            "fan": 1,      # 风扇
            "co2device": 1 # 二氧化碳设备
        }

        # 异步I/O和线程管理器
        self.async_file_io = None
        self.thread_manager = None

    def initialize(self):
        """初始化系统组件"""
        try:
            # 加载配置
            self.logger.info("加载配置文件...")
            self.config = load_config()
            if not self.config:
                self.logger.error("无法加载配置文件，程序退出")
                return False
            
            self.logger.info("配置文件加载成功")
            
            # 集中初始化GPIO
            self.logger.info("初始化GPIO设置...")
            # 不再尝试清理GPIO，因为此时还没有设置GPIO模式
            # 直接设置GPIO模式
            GPIO.setmode(GPIO.BOARD)  # 使用物理引脚编号
            GPIO.setwarnings(False)
            self.logger.info("GPIO设置完成")
            
            # 初始化MQTT客户端
            self.logger.info("初始化MQTT客户端...")
            self.mqtt_client = MQTTClient()
            
            # 将MQTT客户端实例注册为全局变量，以便其他组件可以访问
            import builtins
            builtins.mqtt_client_instance = self.mqtt_client
            
            # 初始化传感器收集器
            self.logger.info("初始化传感器收集器...")
            self.sensor_collector = SensorDataCollector()

            # 将传感器收集器实例注册为全局变量，以便MQTT客户端可以访问
            builtins.sensor_collector_instance = self.sensor_collector

            # 初始化CO2控制器
            self.logger.info("初始化CO2控制器...")
            self.co2_controller = CO2Controller()
            
            # 将CO2控制器实例注册为全局变量，以便设备自检系统可以访问
            builtins.co2_controller_instance = self.co2_controller
            
            # 初始化设备自检系统
            self.logger.info("初始化设备自检系统...")
            try:
                self.device_health = DeviceHealthCheck()
                # 设置传感器收集器引用
                self.device_health.set_sensor_collector(self.sensor_collector)
            except Exception as e:
                self.logger.error(f"初始化设备自检系统失败: {e}", exc_info=True)
                return False
            
            # 将设备自检系统实例注册为全局变量，以便MQTT客户端可以访问
            builtins.device_health_instance = self.device_health
            
            # 初始化蚊子检测文件监控
            if 'mosquito_detection' in self.config.get('mqtt', {}) and self.config['mqtt']['mosquito_detection'].get('enabled', False):
                self.logger.info("初始化蚊子检测文件监控...")
                try:
                    from mosquito.mosquito_file_monitor import MosquitoFileMonitor
                    self.mosquito_monitor = MosquitoFileMonitor()
                    
                    # 注册为全局变量，供MQTT客户端回调使用
                    import builtins
                    builtins.mosquito_monitor_instance = self.mosquito_monitor
                    self.logger.info("蚊子检测文件监控初始化完成")
                except Exception as e:
                    self.logger.error(f"初始化蚊子检测文件监控失败: {e}", exc_info=True)
                    # 不影响系统其他功能，继续初始化
            
            # 注册设备操作回调
            self.mqtt_client.register_device_callbacks(
                power_on_cb=self.power_on,
                power_off_cb=self.power_off,
                restart_cb=self.restart,
                component_switch_cb=self.handle_component_switch
            )
            
            # 不再需要这一行，因为已经在上面的register_device_callbacks中注册了
            # self.mqtt_client._handle_component_switch = self.handle_component_switch
            
            # 启动内存监控
            self._start_memory_monitoring()

            # 启动线程管理器（必须在其他组件启动之前）
            self._start_thread_manager()

            # 启动异步文件I/O
            self._start_async_file_io()

            self.logger.info("系统初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"系统初始化失败: {e}", exc_info=True)
            return False

    def _start_memory_monitoring(self):
        """启动内存监控"""
        try:
            from utils.memory_monitor import start_memory_monitoring

            # 从配置中获取内存监控配置
            memory_config = self.config.get('cache', {}).get('memory_monitor', {})

            # 启动内存监控
            self.memory_monitor = start_memory_monitoring(memory_config)
            self.logger.info("内存监控已启动")

        except Exception as e:
            self.logger.error(f"启动内存监控失败: {e}")
            self.memory_monitor = None

    def _start_async_file_io(self):
        """启动异步文件I/O"""
        try:
            from utils.async_file_io import start_async_file_io

            # 从配置中获取异步I/O配置
            async_io_config = self.config.get('async_file_io', {})

            # 启动异步文件I/O
            self.async_file_io = start_async_file_io(async_io_config)
            self.logger.info("异步文件I/O已启动")

        except Exception as e:
            self.logger.error(f"启动异步文件I/O失败: {e}")
            self.async_file_io = None

    def _start_thread_manager(self):
        """启动线程管理器"""
        try:
            from utils.thread_manager import start_thread_manager

            # 从配置中获取线程管理器配置
            thread_config = self.config.get('thread_manager', {})

            # 启动线程管理器
            self.thread_manager = start_thread_manager(thread_config)
            self.logger.info("线程管理器已启动")

        except Exception as e:
            self.logger.error(f"启动线程管理器失败: {e}")
            self.thread_manager = None

    def start(self):
        """启动系统"""
        if self.running:
            self.logger.warning("系统已在运行中")
            return False
            
        try:
            self.logger.info("\n=== 启动传感器数据采集与上传系统 ===\n")
            
            # 启动MQTT上传线程（使用更健壮的错误处理）
            self.logger.info("启动MQTT上传线程...")
            try:
                self.mqtt_client.start()
                
                # 读取配置的连接超时时间
                mqtt_connection_timeout = self.config.get('mqtt', {}).get('connection_timeout', 30)
                self.logger.info(f"MQTT上传线程已启动，等待连接建立(超时:{mqtt_connection_timeout}秒)...")

                # 等待MQTT连接建立，但不超过配置的超时时间
                wait_start = time.time()
                while time.time() - wait_start < mqtt_connection_timeout:
                    if self.mqtt_client.connection_state == ConnectionState.CONNECTED:
                        elapsed_time = time.time() - wait_start
                        self.logger.info(f"MQTT连接已成功建立，耗时: {elapsed_time:.2f}秒")
                        # MQTT连接成功，开机状态已在连接回调中上报
                        break
                    time.sleep(0.5)
                else:
                    # 超时后的处理
                    elapsed_time = time.time() - wait_start
                    self.logger.warning(f"MQTT连接等待超时: {elapsed_time:.1f}秒，但系统将继续启动，MQTT将在后台重试连接")
                
                if self.mqtt_client.connection_state != ConnectionState.CONNECTED:
                    self.logger.warning(f"MQTT连接未在{mqtt_connection_timeout}秒内建立，将继续启动其他组件")
            except Exception as e:
                self.logger.error(f"启动MQTT上传线程失败: {e}", exc_info=True)
                self.logger.warning("MQTT上传线程启动失败，但将继续启动其他组件")
                # 不因为MQTT启动失败而退出
            
            # 更新电源状态
            self.power_status = 1
            
            # 启动传感器收集线程
            self.logger.info("启动传感器收集线程...")
            self.sensor_collector.start()
            self.logger.info("传感器收集线程已启动，数据采集间隔为60秒")
            
            # 等待一段时间，确保传感器初始化完成
            time.sleep(2)
            
            # 启动CO2控制器
            self.logger.info("初始化CO2控制器...")
            self.co2_controller.start()
            self.logger.info("CO2控制器初始化完成，控制循环已启动")
            
            # 等待一段时间，确保传感器数据已经收集
            self.logger.info("等待传感器数据采集完成第一个周期...")
            time.sleep(5)
            
            # 启动设备自检系统
            self.logger.info("启动设备自检系统...")
            self.device_health.start()
            
            # 启动蚊子检测文件监控
            if hasattr(self, 'mosquito_monitor'):
                self.logger.info("启动蚊子检测文件监控...")
                self.mosquito_monitor.start()
            
            # 开机状态已经在MQTT连接建立时自动上报
            self.logger.info("设备开机状态已在MQTT连接建立时自动上报，无需重复发送")
            
            self.running = True
            self.logger.info("\n系统已成功启动，按 Ctrl+C 停止")
            return True
            
        except Exception as e:
            self.logger.error(f"系统启动失败: {e}", exc_info=True)
            
            # 尝试停止已启动的组件
            self.stop()
            return False
    
    def stop(self):
        """停止系统"""
        self.logger.info("\n正在停止系统...")
        
        # 首先标记系统为停止状态，让其他线程能够响应
        self.running = False
        
        # 如果是正常停机（非关机指令导致的停止），则发送关机消息
        if self.power_status == 1 and self.mqtt_client:
            try:
                self.logger.info("上报设备关机状态...")
                # 即使MQTT未连接，也尝试发送关机消息，消息会进入队列
                success = self.mqtt_client.report_power_off_status()
                if not success:
                    self.logger.warning("设备关机状态上报未收到确认，但将继续关闭系统")
                # 无需等待，已经在report_power_off_status中等待了确认超时时间
            except Exception as e:
                self.logger.error(f"上报设备关机状态失败: {e}")
        
        # 创建字典跟踪组件停止状态
        components_stopped = {
            'device_health': False,
            'co2_controller': False,
            'sensor_collector': False,
            'mqtt_client': False
        }
        
        # 停止设备自检系统
        if self.device_health:
            try:
                self.logger.info("停止设备自检系统...")
                self.device_health.stop()
                components_stopped['device_health'] = True
            except Exception as e:
                self.logger.error(f"停止设备自检系统失败: {e}")
        else:
            components_stopped['device_health'] = True
            
        # 短暂等待，确保设备自检系统已停止
        time.sleep(0.5)
        
        # 停止CO2控制器（给予额外的时间确保它能够完成设备关闭）
        if self.co2_controller:
            try:
                self.logger.info("停止CO2控制器...")
                self.co2_controller.stop()
                
                # 等待CO2控制器完成停止过程（最多3秒）
                wait_time = 3
                start_time = time.time()
                while self.co2_controller.control_thread and self.co2_controller.control_thread.is_alive():
                    if time.time() - start_time > wait_time:
                        self.logger.warning(f"等待CO2控制器停止超时（{wait_time}秒），将继续其他组件的停止")
                        break
                    time.sleep(0.2)
                
                self.logger.info("CO2控制器已停止，所有CO2相关设备已设置为安全状态")
                components_stopped['co2_controller'] = True
            except Exception as e:
                self.logger.error(f"停止CO2控制器失败: {e}")
        else:
            components_stopped['co2_controller'] = True
        
        # 停止传感器收集线程
        if self.sensor_collector:
            try:
                self.logger.info("停止传感器收集线程...")
                self.sensor_collector.stop()
                components_stopped['sensor_collector'] = True
            except Exception as e:
                self.logger.error(f"停止传感器收集线程失败: {e}")
        else:
            components_stopped['sensor_collector'] = True
        
        # 在关闭MQTT客户端前执行最终归档
        if self.mqtt_client and self.mqtt_client.connected:
            try:
                self.logger.info("执行最终数据归档...")
                # 注意：CO2状态数据现在等平台回复后再归档，不在这里最终归档
                self.mqtt_client._archive_uploaded_sensor_data()
                self.mqtt_client._archive_uploaded_errors()
                self.mqtt_client._archive_uploaded_co2_errors()
                self.logger.info("最终数据归档完成")
            except Exception as e:
                self.logger.error(f"执行最终数据归档失败: {e}", exc_info=True)
        
        # 停止MQTT客户端
        if self.mqtt_client:
            try:
                self.logger.info("停止MQTT客户端...")
                self.mqtt_client.stop()
                components_stopped['mqtt_client'] = True
            except Exception as e:
                self.logger.error(f"停止MQTT客户端失败: {e}")
        else:
            components_stopped['mqtt_client'] = True
        
        # 停止蚊子检测文件监控
        if hasattr(self, 'mosquito_monitor'):
            try:
                self.logger.info("停止蚊子检测文件监控...")
                self.mosquito_monitor.stop()
            except Exception as e:
                self.logger.error(f"停止蚊子检测文件监控失败: {e}", exc_info=True)

        # 停止内存监控
        if hasattr(self, 'memory_monitor') and self.memory_monitor:
            try:
                self.logger.info("停止内存监控...")
                self.memory_monitor.stop()
            except Exception as e:
                self.logger.error(f"停止内存监控失败: {e}", exc_info=True)

        # 停止异步文件I/O
        if hasattr(self, 'async_file_io') and self.async_file_io:
            try:
                self.logger.info("停止异步文件I/O...")
                self.async_file_io.stop()
            except Exception as e:
                self.logger.error(f"停止异步文件I/O失败: {e}", exc_info=True)

        # 停止线程管理器
        if hasattr(self, 'thread_manager') and self.thread_manager:
            try:
                self.logger.info("停止线程管理器...")
                self.thread_manager.stop()
            except Exception as e:
                self.logger.error(f"停止线程管理器失败: {e}", exc_info=True)
        
        # 记录组件停止状态
        not_stopped = [comp for comp, stopped in components_stopped.items() if not stopped]
        if not_stopped:
            self.logger.warning(f"以下组件可能未正确停止: {', '.join(not_stopped)}")
        else:
            self.logger.info("所有组件已成功停止")
        
        # 清理GPIO资源
        try:
            self.logger.info("清理GPIO资源...")
            GPIO.cleanup()
        except Exception as e:
            self.logger.error(f"清理GPIO资源失败: {e}")
        
        # 清理全局变量
        try:
            import builtins
            if hasattr(builtins, 'device_health_instance'):
                delattr(builtins, 'device_health_instance')
            if hasattr(builtins, 'mqtt_client_instance'):
                delattr(builtins, 'mqtt_client_instance')
            if hasattr(builtins, 'sensor_collector_instance'):
                delattr(builtins, 'sensor_collector_instance')
            if hasattr(builtins, 'mosquito_monitor_instance'):
                delattr(builtins, 'mosquito_monitor_instance')
            if hasattr(builtins, 'co2_controller_instance'):
                delattr(builtins, 'co2_controller_instance')
        except Exception as e:
            self.logger.error(f"清理全局变量失败: {e}")
        
        self.logger.info("系统已完全停止")
    
    def power_on(self):
        """设备开机回调函数"""
        self.logger.info("执行设备开机操作")
        
        # 如果设备已经开机，无需操作
        if self.power_status == 1:
            self.logger.info("设备已经处于开机状态")
            return
        
        self.power_status = 1
        
        try:
            # 启动传感器收集线程
            if self.sensor_collector:
                self.logger.info("启动传感器收集线程...")
                self.sensor_collector.start()
                self.logger.info("传感器收集线程已启动，数据采集间隔为60秒")
                time.sleep(2)
            
            # 启动CO2控制器
            if self.co2_controller:
                self.logger.info("初始化CO2控制器...")
                self.co2_controller.start()
                self.logger.info("CO2控制器初始化完成，控制循环已启动")
            
            # 启动设备自检系统
            if self.device_health:
                self.logger.info("启动设备自检系统...")
                self.device_health.start()
            
            # 启动蚊子检测文件监控
            if hasattr(self, 'mosquito_monitor'):
                self.logger.info("启动蚊子检测文件监控...")
                self.mosquito_monitor.start()
            
            self.logger.info("设备开机操作执行完成")
            
        except Exception as e:
            self.logger.error(f"设备开机操作失败: {e}", exc_info=True)
    
    def power_off(self):
        """设备关机回调函数"""
        self.logger.info("执行设备关机操作")
        
        # 如果设备已经关机，无需操作
        if self.power_status == 2:
            self.logger.info("设备已经处于关机状态")
            return
        
        self.power_status = 2
        
        try:
            # 停止设备自检系统
            if self.device_health:
                self.logger.info("停止设备自检系统...")
                self.device_health.stop()
            
            # 停止CO2控制器
            if self.co2_controller:
                self.logger.info("停止CO2控制器...")
                self.co2_controller.stop()
                self.logger.info("CO2控制器已停止，所有CO2相关设备已设置为安全状态")
            
            # 停止传感器收集线程
            if self.sensor_collector:
                self.logger.info("停止传感器收集线程...")
                self.sensor_collector.stop()
            
            # MQTT客户端保持运行，以便接收平台指令
            
            # 停止蚊子检测文件监控
            if hasattr(self, 'mosquito_monitor'):
                self.logger.info("停止蚊子检测文件监控...")
                self.mosquito_monitor.stop()
            
            self.logger.info("设备关机操作执行完成")
            
        except Exception as e:
            self.logger.error(f"设备关机操作失败: {e}", exc_info=True)
    
    def restart(self):
        """设备重启回调函数"""
        self.logger.info("执行设备重启操作")
        
        try:
            # 先上报重启状态
            success = self.mqtt_client.report_restart_status()
            if not success:
                self.logger.warning("设备重启状态上报未收到确认，但将继续执行重启")
            
            # 执行关机操作
            self.power_off()
            
            # 等待一段时间
            self.logger.info("等待 5 秒后重新启动设备...")
            time.sleep(5)
            
            # 执行开机操作
            self.power_on()
            
            self.logger.info("设备重启操作执行完成，所有组件已重新启动")
            
        except Exception as e:
            self.logger.error(f"设备重启操作失败: {e}", exc_info=True)

    def handle_component_switch(self, component_status):
        """处理组件动态开关
        
        Args:
            component_status: 组件状态字典
            
        Returns:
            bool: 是否成功处理所有组件状态
        """
        try:
            if not isinstance(component_status, dict):
                self.logger.error(f"组件状态必须是字典类型，收到: {type(component_status)}")
                return False
                
            # 记录成功处理的组件数量
            success_count = 0
            total_count = 0
            
            # 更新组件状态
            for component, status in component_status.items():
                total_count += 1
                if component in self.component_status:
                    old_status = self.component_status[component]
                    self.component_status[component] = status
                    self.logger.info(f"组件 {component} 状态从 {old_status} 更改为 {status}")
                    
                    # 根据组件类型执行相应操作
                    try:
                        if component in ['co2', 'gps', 'hum', 'temp', 'ws']:
                            self._handle_sensor_switch(component, status)
                            success_count += 1
                        elif component == 'camera':
                            self._handle_camera_switch(status)
                            success_count += 1
                        elif component == 'fan':
                            self._handle_fan_switch(status)
                            success_count += 1
                        elif component == 'co2device':
                            self._handle_co2device_switch(status)
                            success_count += 1
                    except Exception as e:
                        self.logger.error(f"处理组件 {component} 状态时发生错误: {e}")
                else:
                    self.logger.warning(f"未知组件: {component}")
            
            # 如果所有组件都处理成功，或者没有组件需要处理，返回True
            if total_count == 0 or success_count == total_count:
                return True
            # 如果至少有一个组件处理成功，也返回True
            elif success_count > 0:
                return True
            # 如果没有任何组件处理成功，返回False
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"处理组件开关状态时发生错误: {e}", exc_info=True)
            return False
    
    def _handle_sensor_switch(self, sensor_type, status):
        """处理传感器开关
        
        Args:
            sensor_type: 传感器类型
            status: 开关状态 (1: 开启, 0: 关闭)
        """
        if not self.sensor_collector:
            self.logger.warning(f"传感器收集器未初始化，无法切换 {sensor_type} 传感器状态")
            return
            
        try:
            if status == 1:
                self.logger.info(f"启用 {sensor_type} 传感器")
                # 在传感器收集器中启用该传感器
                if hasattr(self.sensor_collector, 'enable_sensor'):
                    self.sensor_collector.enable_sensor(sensor_type)
            else:
                self.logger.info(f"禁用 {sensor_type} 传感器")
                # 在传感器收集器中禁用该传感器
                if hasattr(self.sensor_collector, 'disable_sensor'):
                    self.sensor_collector.disable_sensor(sensor_type)
        except Exception as e:
            self.logger.error(f"切换 {sensor_type} 传感器状态时发生错误: {e}")
    
    def _handle_camera_switch(self, status):
        """处理摄像头开关
        
        Args:
            status: 开关状态 (1: 开启, 0: 关闭)
        """
        try:
            if status == 1:
                self.logger.info("启用摄像头")
                # 启用摄像头的代码
            else:
                self.logger.info("禁用摄像头")
                # 禁用摄像头的代码
        except Exception as e:
            self.logger.error(f"切换摄像头状态时发生错误: {e}")
    
    def _handle_fan_switch(self, status):
        """处理风扇开关
        
        Args:
            status: 开关状态 (1: 开启, 0: 关闭)
        """
        try:
            # 这里的风扇是指物理引脚23的独立风扇，不是CO2设备的风扇
            fan_pin = 23  # 独立风扇的物理引脚
            
            if status == 1:
                self.logger.info("启用独立风扇 (PIN: 23)")
                # 设置GPIO输出
                GPIO.setup(fan_pin, GPIO.OUT)
                GPIO.output(fan_pin, GPIO.HIGH)
            else:
                self.logger.info("禁用独立风扇 (PIN: 23)")
                # 设置GPIO输出
                GPIO.setup(fan_pin, GPIO.OUT)
                GPIO.output(fan_pin, GPIO.LOW)
        except Exception as e:
            self.logger.error(f"切换独立风扇状态时发生错误: {e}")
    
    def _handle_co2device_switch(self, status):
        """处理CO2设备开关
        
        Args:
            status: 开关状态 (1: 开启, 0: 关闭)
        """
        try:
            if not self.co2_controller:
                self.logger.warning("CO2控制器未初始化，无法切换CO2设备状态")
                return
                
            if status == 1:
                self.logger.info("启用CO2设备")
                # 启动CO2控制器
                if not self.co2_controller.running:
                    self.co2_controller.start()
            else:
                self.logger.info("禁用CO2设备")
                # 停止CO2控制器
                if self.co2_controller.running:
                    self.co2_controller.stop()
        except Exception as e:
            self.logger.error(f"切换CO2设备状态时发生错误: {e}")
            
    def enable_test_interface(self, host='0.0.0.0', port=5000):
        """启用测试接口
        
        Args:
            host (str): 测试服务器主机地址
            port (int): 测试服务器端口
            
        Returns:
            bool: 是否成功启用测试接口
        """
        try:
            self.logger.info(f"正在启用测试接口: {host}:{port}")
            
            # 导入测试服务器模块
            test_server_path = os.path.join(os.path.dirname(__file__), 'test', 'api')
            sys.path.insert(0, test_server_path)

            # 清除可能的模块缓存
            import importlib
            if 'test_server' in sys.modules:
                importlib.reload(sys.modules['test_server'])

            from test_server import run_test_server
            
            # 启动测试服务器v1
            result = True  # 由于run_test_server是阻塞的，所以我们启动一个线程来运行它
            threading.Thread(target=lambda: run_test_server(host=host, port=port, system=self), daemon=True).start()

            # 同时启动测试服务器v2（如果存在）
            try:
                test_server_v2_path = os.path.join(os.path.dirname(__file__), 'test', 'api', 'v2')
                sys.path.insert(0, test_server_v2_path)

                from test_server_v2 import run_test_server_v2
                v2_port = port + 1  # v2使用下一个端口

                threading.Thread(target=lambda: run_test_server_v2(host=host, port=v2_port, system=self), daemon=True).start()
                self.logger.info(f"测试接口 v2.0 已启用: http://{host}:{v2_port}/")

            except ImportError:
                self.logger.info("测试接口 v2.0 不可用，仅启用 v1.0")
            except Exception as e:
                self.logger.warning(f"启用测试接口 v2.0 失败: {e}")

            if result:
                self.logger.info(f"测试接口 v1.0 已成功启用: http://{host}:{port}/")
                return True
            else:
                self.logger.error("启用测试接口失败")
                return False
                
        except Exception as e:
            self.logger.error(f"启用测试接口时发生错误: {e}", exc_info=True)
            return False

def signal_handler(sig, frame):
    """信号处理函数，用于捕获Ctrl+C和SIGTERM，使用优化的超时机制"""
    print("\n捕获到信号 {0}，正在停止系统...".format(sig))
    
    # 创建一个标志，表示系统是否成功停止
    stop_completed = [False]
    
    # 尽可能快地将system标记为非运行状态，以便各组件能够及时检测到停止信号
    if 'system' in globals() and system:
        system.running = False
        # 如果CO2控制器存在，立即标记为停止中
        if hasattr(system, 'co2_controller') and system.co2_controller:
            system.co2_controller.stopping = True
    
    # 创建一个停止线程
    def stop_thread_func():
        try:
            if 'system' in globals() and system:
                # 即使system.running已经是False，也执行stop方法进行完整清理
                system.stop()
                stop_completed[0] = True
                print("系统已正常停止")
            else:
                stop_completed[0] = True
                print("系统未运行或已停止")
        except Exception as e:
            print(f"停止系统时发生错误: {e}")
            stop_completed[0] = True  # 即使发生错误也标记为完成，以确保程序退出
    
    # 启动停止线程
    stop_thread = threading.Thread(target=stop_thread_func)
    stop_thread.daemon = True
    stop_thread.start()
    
    # 第一阶段：等待正常停止（15秒）
    primary_timeout = 15  # 增加到15秒
    start_time = time.time()
    check_interval = 0.2
    
    # 只在开始和结束时显示等待消息
    print("正在等待系统停止，请稍候...")
    
    while not stop_completed[0] and time.time() - start_time < primary_timeout:
        time.sleep(check_interval)
    
    # 如果第一阶段超时，给出警告并进入第二阶段
    if not stop_completed[0]:
        print("系统停止需要额外时间，继续等待...")
        
        # 第二阶段：额外等待（10秒）
        secondary_timeout = 10
        secondary_start = time.time()
        
        while not stop_completed[0] and time.time() - secondary_start < secondary_timeout:
            time.sleep(check_interval)
    
    # 如果仍未完成，执行最终清理并退出
    if not stop_completed[0]:
        print(f"\n系统停止仍未完成，执行最终资源清理...")
        
        # 执行最终清理，只清理GPIO资源，不再重复关闭CO2控制器
        try:
            # 清理GPIO资源
            GPIO.cleanup()
            print("已清理GPIO资源")
        except Exception as e:
            print(f"清理GPIO资源时发生错误: {e}")
        
        print("程序即将退出")
        sys.exit(0)  # 确保程序完全退出

# 主程序入口
if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="传感器系统")
    parser.add_argument("--test", action="store_true", help="启用测试接口")
    parser.add_argument("--host", default="0.0.0.0", help="测试接口主机地址")
    parser.add_argument("--port", type=int, default=5000, help="测试接口端口")
    args = parser.parse_args()
    
    # 注册信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建系统实例
    system = SensorSystem()
    
    # 初始化系统
    if not system.initialize():
        print("系统初始化失败，程序退出")
        sys.exit(1)
    
    # 启动系统
    if not system.start():
        print("系统启动失败，程序退出")
        sys.exit(1)
    
    # 如果指定了--test参数，启用测试接口
    if args.test:
        system.enable_test_interface(host=args.host, port=args.port)
    
    # 主线程保持运行
    try:
        last_mqtt_status = None
        # 开机状态由MQTT连接回调统一处理，无需全局变量跟踪
        last_queue_check = time.time()
        queue_check_interval = system.config.get('mqtt', {}).get('queue_check_interval', 300)
        
        while system.running:
            current_time = time.time()
            
            # 监控MQTT连接状态
            if system.mqtt_client:
                current_mqtt_status = system.mqtt_client.connection_state
                
                # 如果MQTT连接状态发生变化，记录日志
                if current_mqtt_status != last_mqtt_status:
                    if current_mqtt_status == ConnectionState.CONNECTED:
                        system.logger.info("MQTT已连接")
                        # 如果之前的状态不是连接状态，则记录连接恢复
                        if last_mqtt_status != ConnectionState.CONNECTED:
                            system.logger.info("MQTT连接已恢复")
                        
                            # 开机状态已在MQTT连接建立时上报，无需重复上报
                            system.logger.info("MQTT连接已恢复，开机状态已在连接建立时上报")
                    elif current_mqtt_status == ConnectionState.DISCONNECTED:
                        system.logger.info("MQTT连接已断开")
                        system.logger.info("MQTT连接已断开，数据将缓存在本地")
                    elif current_mqtt_status == ConnectionState.CONNECTING:
                        system.logger.info("MQTT正在连接中...")
                        system.logger.info("MQTT正在尝试连接...")
                    
                    last_mqtt_status = current_mqtt_status
                
                # 定期检查队列状态
                if current_time - last_queue_check >= queue_check_interval:
                    if system.mqtt_client.connected:
                        system.logger.info("系统运行正常，MQTT连接已建立")
                    else:
                        system.logger.warning("系统运行中，但MQTT连接未建立，数据将缓存在本地")
                    last_queue_check = current_time
                    
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n接收到键盘中断，正在停止系统...")
        system.stop()
        sys.exit(0)  # 确保程序退出
