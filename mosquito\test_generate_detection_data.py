#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import time
import random
import argparse
from datetime import datetime

class MosquitoDetectionGenerator:
    """蚊子检测数据生成测试工具"""

    def __init__(self, shared_data_path=None):
        # 设置共享数据路径
        if shared_data_path:
            self.shared_data_path = shared_data_path
        else:
            # 默认路径
            self.shared_data_path = "/home/<USER>/shared_data"
            # 在Windows环境下修改路径
            if os.name == 'nt':
                self.shared_data_path = os.path.join(os.getcwd(), "shared_data")

        # 确保目录存在
        self.incoming_dir = os.path.join(self.shared_data_path, "incoming")
        self._ensure_directories()
        
        # 蚊子种类
        self.mosquito_types = [
            "Aedes albopictus",  # 白纹伊蚊
            "Anopheles sinensis",  # 中华按蚊
            "Culex pipiens",  # 家蚊
            "Aedes aegypti",  # 埃及伊蚊
            "Culex tritaeniorhynchus"  # 三带喙库蚊
        ]

    def _ensure_directories(self):
        """确保必要的目录存在"""
        os.makedirs(self.shared_data_path, exist_ok=True)
        os.makedirs(self.incoming_dir, exist_ok=True)
        print(f"已确保目录存在: {self.incoming_dir}")

    def _generate_random_coords(self):
        """生成随机坐标"""
        x1 = random.randint(100, 2000)
        y1 = random.randint(100, 1500)
        width = random.randint(300, 800)
        height = random.randint(300, 800)
        x2 = x1 + width
        y2 = y1 + height
        return f"{x1},{y1},{x2},{y2}"

    def _generate_track_id(self, index, timestamp):
        """生成跟踪ID"""
        time_str = timestamp.strftime("%Y%m%d_%H%M%S")
        return f"det_{index}_{time_str}"

    def _generate_image_urls(self, timestamp_str, count=4):
        """生成图片URL"""
        urls = []
        base_url = "https://imgdev.wlwise.com/Mosquito-borne"
        for _ in range(count):
            random_hex = ''.join(random.choices('0123456789abcdef', k=8))
            urls.append(f"{base_url}/{timestamp_str}_{random_hex}.jpg")
        return ','.join(urls)

    def generate_detection_data(self, detection_count=None):
        """生成蚊子检测数据
        
        Args:
            detection_count: 检测到的蚊子数量，默认为随机1-5个
            
        Returns:
            dict: 检测数据
        """
        now = datetime.now()
        timestamp = int(time.time())
        timestamp_str = now.strftime("%Y%m%d%H%M%S")
        
        if detection_count is None:
            detection_count = random.randint(1, 5)
        
        # 生成检测结果列表
        detections = []
        for i in range(detection_count):
            detection = {
                "count": 1,
                "type": random.choice(self.mosquito_types),
                "order": i + 1,
                "idnt": random.randint(75, 99),
                "coords": self._generate_random_coords(),
                "track_id": self._generate_track_id(i, now)
            }
            detections.append(detection)
        
        # 生成完整数据
        data = {
            "devid": "",  # 留空，将由系统填充
            "ver": "2.0",
            "data": {
                "time": timestamp,
                "picurl": self._generate_image_urls(timestamp_str),
                "lr": detections
            }
        }
        
        return data

    def save_detection_file(self, data, use_temp_file=True):
        """保存检测数据到文件
        
        Args:
            data: 检测数据
            use_temp_file: 是否使用临时文件
            
        Returns:
            str: 保存的文件路径
        """
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"mosquito_detection_{timestamp}.json"
        final_path = os.path.join(self.incoming_dir, filename)
        
        if use_temp_file:
            # 先写入临时文件
            temp_path = f"{final_path}.tmp"
            with open(temp_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            # 原子操作重命名
            os.rename(temp_path, final_path)
            print(f"已通过临时文件写入: {final_path}")
        else:
            # 直接写入目标文件
            with open(final_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"已直接写入文件: {final_path}")
        
        return final_path

    def generate_multiple_files(self, count, interval=2.0):
        """生成多个检测文件
        
        Args:
            count: 要生成的文件数量
            interval: 每个文件之间的间隔时间(秒)
        """
        print(f"开始生成{count}个蚊子检测文件，间隔{interval}秒...")
        
        for i in range(count):
            # 随机生成1-3个蚊子的检测数据
            detection_count = random.randint(1, 3)
            data = self.generate_detection_data(detection_count)
            
            # 保存文件
            file_path = self.save_detection_file(data)
            print(f"已生成文件 [{i+1}/{count}]: {os.path.basename(file_path)}, 包含 {detection_count} 个蚊子检测结果")
            
            # 如果不是最后一个文件，等待指定间隔
            if i < count - 1:
                time.sleep(interval)
        
        print(f"已完成生成 {count} 个检测文件")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="生成蚊子检测测试数据")
    parser.add_argument("--count", type=int, default=1, help="生成的文件数量")
    parser.add_argument("--interval", type=float, default=2.0, help="文件生成间隔(秒)")
    parser.add_argument("--path", type=str, default=None, help="共享数据路径")
    args = parser.parse_args()
    
    generator = MosquitoDetectionGenerator(args.path)
    generator.generate_multiple_files(args.count, args.interval) 