# 离线测试环境配置
# 用于测试时避免云端数据污染

test_environment:
  name: "offline"
  description: "离线测试环境，不上传云端数据"
  
# 数据存储配置
data_storage:
  base_path: "test/data/offline"
  structure:
    sensor_data: "sensor/YYYY-MM-DD/"
    co2_status: "co2/YYYY-MM-DD/"
    device_check: "check/YYYY-MM-DD/"
    mosquito_detection: "mosquito/YYYY-MM-DD/"
  retention:
    days: 7                    # 测试数据保留7天
    auto_cleanup: true         # 自动清理过期数据
    archive_before_cleanup: false  # 不归档，直接删除

# MQTT配置（离线模式）
mqtt:
  enabled: false               # 禁用MQTT上传
  mock_mode: true             # 启用MQTT模拟模式
  mock_reply_delay: 0.1       # 模拟回复延迟（秒）
  mock_success_rate: 0.95     # 模拟成功率

# 设备配置
device:
  id_prefix: "TEST-"          # 测试设备ID前缀
  mock_sensors: true          # 使用模拟传感器
  mock_co2_controller: true   # 使用模拟CO2控制器

# 性能监控配置
monitoring:
  enabled: true
  interval: 5                 # 监控间隔（秒）
  metrics:
    - cpu_usage
    - memory_usage
    - disk_usage
    - queue_status
    - thread_count

# 日志配置
logging:
  level: "INFO"
  file: "test/logs/offline_test.log"
  max_size: "10MB"
  backup_count: 5

# 测试数据标识
test_markers:
  device_id_prefix: "TEST-"
  mqtt_topic_prefix: "/test/"
  file_name_suffix: "_test"
  database_table_prefix: "test_"

# 云端上传防护
cloud_upload_prevention:
  mqtt_mock_mode: true
  platform_reply_simulation: true
  upload_queue_isolation: true
  data_validation_strict: false
