#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
压力测试运行器
包含数据注入压力测试、并发压力测试、系统资源压力测试等
"""

import os
import sys
import json
import time
import logging
import threading
import requests
import psutil
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import random

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from utils.logger import get_logger

# 设置日志
logger = get_logger("stress_test", logging.INFO)

class StressTestRunner:
    """压力测试运行器"""
    
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
        self.api_key = "test_key_123"
        self.headers = {
            "Content-Type": "application/json",
            "X-API-Key": self.api_key
        }
        
        # 测试结果统计
        self.test_results = {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "performance_metrics": {},
            "errors": []
        }
        
        # 性能基准
        self.performance_targets = {
            "sensor_data_throughput": 1000,      # 条/分钟
            "co2_status_response_time": 0.1,     # 秒
            "device_check_completion": 30,       # 秒
            "memory_usage_peak": 512,            # MB
            "cpu_usage_average": 30,             # %
            "concurrent_users": 100,             # 并发用户数
            "error_rate": 1                      # %
        }
    
    def monitor_system_resources(self, duration=60):
        """监控系统资源"""
        logger.info(f"开始监控系统资源，持续{duration}秒...")
        
        start_time = time.time()
        resource_data = []
        
        while time.time() - start_time < duration:
            try:
                # 获取系统资源信息
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')
                
                resource_info = {
                    "timestamp": time.time(),
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_used_mb": memory.used / 1024 / 1024,
                    "disk_percent": disk.percent
                }
                
                resource_data.append(resource_info)
                
            except Exception as e:
                logger.error(f"监控系统资源失败: {e}")
                break
        
        # 计算统计信息
        if resource_data:
            avg_cpu = sum(r["cpu_percent"] for r in resource_data) / len(resource_data)
            max_memory = max(r["memory_used_mb"] for r in resource_data)
            avg_memory_percent = sum(r["memory_percent"] for r in resource_data) / len(resource_data)
            
            self.test_results["performance_metrics"]["resource_monitoring"] = {
                "duration": duration,
                "avg_cpu_percent": avg_cpu,
                "max_memory_mb": max_memory,
                "avg_memory_percent": avg_memory_percent,
                "sample_count": len(resource_data)
            }
            
            logger.info(f"资源监控完成: 平均CPU {avg_cpu:.1f}%, 最大内存 {max_memory:.1f}MB")
            return resource_data
        
        return []
    
    def test_data_injection_stress(self, data_count=10000, concurrent_threads=10):
        """数据注入压力测试"""
        logger.info(f"开始数据注入压力测试: {data_count}条数据, {concurrent_threads}个并发线程...")
        
        def inject_batch_data(batch_size=100):
            """注入批量数据"""
            try:
                # 生成测试数据
                test_data = []
                for i in range(batch_size):
                    data = {
                        "temperature": 20 + random.uniform(-5, 15),
                        "humidity": 50 + random.uniform(-20, 30),
                        "co2": 400 + random.uniform(0, 800),
                        "timestamp": time.time(),
                        "test_id": f"stress_test_{i}"
                    }
                    test_data.append(data)
                
                # 发送批量数据
                response = requests.post(
                    f"{self.base_url}/api/v1/inject/sensor/batch",
                    headers=self.headers,
                    json=test_data,
                    timeout=30
                )
                
                return response.status_code == 200
                
            except Exception as e:
                logger.error(f"批量数据注入失败: {e}")
                return False
        
        start_time = time.time()
        
        # 启动资源监控
        monitor_thread = threading.Thread(
            target=self.monitor_system_resources,
            args=(60,)
        )
        monitor_thread.start()
        
        try:
            # 计算批次数量
            batch_size = 100
            batch_count = data_count // batch_size
            batches_per_thread = batch_count // concurrent_threads
            
            # 使用线程池执行并发数据注入
            with ThreadPoolExecutor(max_workers=concurrent_threads) as executor:
                futures = []
                for _ in range(concurrent_threads):
                    for _ in range(batches_per_thread):
                        future = executor.submit(inject_batch_data, batch_size)
                        futures.append(future)
                
                # 等待所有任务完成
                results = [future.result() for future in as_completed(futures)]
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 计算统计信息
            success_count = sum(results)
            total_batches = len(results)
            success_rate = success_count / total_batches * 100 if total_batches > 0 else 0
            throughput = (success_count * batch_size) / (duration / 60)  # 条/分钟
            
            # 记录性能指标
            self.test_results["performance_metrics"]["data_injection"] = {
                "total_data_count": data_count,
                "successful_batches": success_count,
                "total_batches": total_batches,
                "success_rate": success_rate,
                "duration": duration,
                "throughput_per_minute": throughput,
                "concurrent_threads": concurrent_threads
            }
            
            logger.info(f"数据注入压力测试完成:")
            logger.info(f"  成功批次: {success_count}/{total_batches}")
            logger.info(f"  成功率: {success_rate:.1f}%")
            logger.info(f"  吞吐量: {throughput:.1f} 条/分钟")
            logger.info(f"  耗时: {duration:.2f}秒")
            
            # 判断是否通过测试
            if success_rate >= 90 and throughput >= self.performance_targets["sensor_data_throughput"]:
                self.test_results["passed_tests"] += 1
                return True
            else:
                self.test_results["failed_tests"] += 1
                return False
                
        except Exception as e:
            logger.error(f"数据注入压力测试失败: {e}")
            self.test_results["errors"].append(f"数据注入压力测试: {e}")
            self.test_results["failed_tests"] += 1
            return False
        finally:
            self.test_results["total_tests"] += 1
            # 等待监控线程完成
            monitor_thread.join(timeout=10)
    
    def test_concurrent_api_stress(self, concurrent_users=50, duration=60):
        """并发API压力测试"""
        logger.info(f"开始并发API压力测试: {concurrent_users}个并发用户, 持续{duration}秒...")
        
        def api_user_simulation():
            """模拟单个用户的API调用"""
            start_time = time.time()
            request_count = 0
            success_count = 0
            
            while time.time() - start_time < duration:
                try:
                    # 随机选择API接口进行测试
                    api_endpoints = [
                        ("/api/v1/monitor/resources", "GET"),
                        ("/api/v1/monitor/queues", "GET"),
                        ("/api/v1/monitor/components", "GET"),
                        ("/api/v1/health", "GET")
                    ]
                    
                    endpoint, method = random.choice(api_endpoints)
                    
                    if method == "GET":
                        response = requests.get(
                            f"{self.base_url}{endpoint}",
                            headers=self.headers,
                            timeout=5
                        )
                    else:
                        response = requests.post(
                            f"{self.base_url}{endpoint}",
                            headers=self.headers,
                            json={},
                            timeout=5
                        )
                    
                    request_count += 1
                    if response.status_code == 200:
                        success_count += 1
                    
                    # 随机延迟
                    time.sleep(random.uniform(0.1, 0.5))
                    
                except Exception as e:
                    request_count += 1
                    # 忽略个别请求失败
                    pass
            
            return {
                "request_count": request_count,
                "success_count": success_count,
                "duration": time.time() - start_time
            }
        
        start_time = time.time()
        
        try:
            # 使用线程池模拟并发用户
            with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
                futures = [executor.submit(api_user_simulation) for _ in range(concurrent_users)]
                results = [future.result() for future in as_completed(futures)]
            
            end_time = time.time()
            total_duration = end_time - start_time
            
            # 计算统计信息
            total_requests = sum(r["request_count"] for r in results)
            total_success = sum(r["success_count"] for r in results)
            success_rate = total_success / total_requests * 100 if total_requests > 0 else 0
            requests_per_second = total_requests / total_duration
            
            # 记录性能指标
            self.test_results["performance_metrics"]["concurrent_api"] = {
                "concurrent_users": concurrent_users,
                "duration": total_duration,
                "total_requests": total_requests,
                "successful_requests": total_success,
                "success_rate": success_rate,
                "requests_per_second": requests_per_second
            }
            
            logger.info(f"并发API压力测试完成:")
            logger.info(f"  并发用户: {concurrent_users}")
            logger.info(f"  总请求数: {total_requests}")
            logger.info(f"  成功请求: {total_success}")
            logger.info(f"  成功率: {success_rate:.1f}%")
            logger.info(f"  请求速率: {requests_per_second:.1f} 请求/秒")
            
            # 判断是否通过测试
            if success_rate >= 95 and requests_per_second >= 10:
                self.test_results["passed_tests"] += 1
                return True
            else:
                self.test_results["failed_tests"] += 1
                return False
                
        except Exception as e:
            logger.error(f"并发API压力测试失败: {e}")
            self.test_results["errors"].append(f"并发API压力测试: {e}")
            self.test_results["failed_tests"] += 1
            return False
        finally:
            self.test_results["total_tests"] += 1
    
    def test_memory_stress(self, target_memory_mb=400):
        """内存压力测试"""
        logger.info(f"开始内存压力测试: 目标内存使用 {target_memory_mb}MB...")
        
        try:
            # 获取初始内存使用情况
            initial_memory = psutil.virtual_memory().used / 1024 / 1024
            
            # 通过大量数据注入来增加内存使用
            large_data_batches = []
            batch_size = 1000
            
            start_time = time.time()
            
            while True:
                current_memory = psutil.virtual_memory().used / 1024 / 1024
                memory_increase = current_memory - initial_memory
                
                if memory_increase >= target_memory_mb:
                    break
                
                # 生成大批量测试数据
                batch_data = []
                for i in range(batch_size):
                    data = {
                        "temperature": 20 + random.uniform(-5, 15),
                        "humidity": 50 + random.uniform(-20, 30),
                        "co2": 400 + random.uniform(0, 800),
                        "timestamp": time.time(),
                        "large_data": "x" * 1000,  # 增加数据大小
                        "test_id": f"memory_stress_{i}"
                    }
                    batch_data.append(data)
                
                # 发送数据并保存响应（增加内存使用）
                try:
                    response = requests.post(
                        f"{self.base_url}/api/v1/inject/sensor/batch",
                        headers=self.headers,
                        json=batch_data,
                        timeout=10
                    )
                    large_data_batches.append(response.text)
                except:
                    pass
                
                # 检查超时
                if time.time() - start_time > 120:  # 2分钟超时
                    break
            
            end_time = time.time()
            duration = end_time - start_time
            final_memory = psutil.virtual_memory().used / 1024 / 1024
            memory_increase = final_memory - initial_memory
            
            # 记录性能指标
            self.test_results["performance_metrics"]["memory_stress"] = {
                "target_memory_mb": target_memory_mb,
                "initial_memory_mb": initial_memory,
                "final_memory_mb": final_memory,
                "memory_increase_mb": memory_increase,
                "duration": duration,
                "batches_sent": len(large_data_batches)
            }
            
            logger.info(f"内存压力测试完成:")
            logger.info(f"  初始内存: {initial_memory:.1f}MB")
            logger.info(f"  最终内存: {final_memory:.1f}MB")
            logger.info(f"  内存增长: {memory_increase:.1f}MB")
            logger.info(f"  耗时: {duration:.2f}秒")
            
            # 清理内存
            del large_data_batches
            
            # 判断是否通过测试
            if memory_increase >= target_memory_mb * 0.8:  # 达到目标的80%即可
                self.test_results["passed_tests"] += 1
                return True
            else:
                self.test_results["failed_tests"] += 1
                return False
                
        except Exception as e:
            logger.error(f"内存压力测试失败: {e}")
            self.test_results["errors"].append(f"内存压力测试: {e}")
            self.test_results["failed_tests"] += 1
            return False
        finally:
            self.test_results["total_tests"] += 1
    
    def run_all_stress_tests(self):
        """运行所有压力测试"""
        logger.info("=" * 60)
        logger.info("开始压力测试套件")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        # 执行所有压力测试
        tests = [
            ("数据注入压力测试", lambda: self.test_data_injection_stress(5000, 5)),
            ("并发API压力测试", lambda: self.test_concurrent_api_stress(20, 30)),
            ("内存压力测试", lambda: self.test_memory_stress(200))
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n执行测试: {test_name}")
            try:
                test_func()
                time.sleep(5)  # 测试间隔
            except Exception as e:
                logger.error(f"测试执行异常: {test_name}, 错误: {e}")
                self.test_results["errors"].append(f"{test_name}: {e}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 输出测试结果
        self.print_test_results(duration)
        
        success_rate = self.test_results["passed_tests"] / self.test_results["total_tests"] * 100 if self.test_results["total_tests"] > 0 else 0
        return success_rate >= 70
    
    def print_test_results(self, duration):
        """打印测试结果"""
        logger.info("\n" + "=" * 60)
        logger.info("压力测试套件完成")
        logger.info("=" * 60)
        logger.info(f"总测试数: {self.test_results['total_tests']}")
        logger.info(f"通过: {self.test_results['passed_tests']}")
        logger.info(f"失败: {self.test_results['failed_tests']}")
        logger.info(f"总耗时: {duration:.2f}秒")
        
        # 输出性能指标
        if self.test_results["performance_metrics"]:
            logger.info("\n性能指标:")
            for test_name, metrics in self.test_results["performance_metrics"].items():
                logger.info(f"  {test_name}:")
                for key, value in metrics.items():
                    if isinstance(value, float):
                        logger.info(f"    {key}: {value:.2f}")
                    else:
                        logger.info(f"    {key}: {value}")
        
        if self.test_results["errors"]:
            logger.info("\n错误详情:")
            for error in self.test_results["errors"]:
                logger.error(f"  - {error}")
        
        success_rate = self.test_results["passed_tests"] / self.test_results["total_tests"] * 100 if self.test_results["total_tests"] > 0 else 0
        logger.info(f"\n总体成功率: {success_rate:.1f}%")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="压力测试运行器")
    parser.add_argument("--url", default="http://localhost:8080", help="测试服务器URL")
    parser.add_argument("--data-count", type=int, default=5000, help="数据注入测试数量")
    parser.add_argument("--concurrent", type=int, default=20, help="并发用户数")
    
    args = parser.parse_args()
    
    # 创建压力测试运行器
    stress_runner = StressTestRunner(args.url)
    
    # 运行压力测试
    success = stress_runner.run_all_stress_tests()
    
    # 退出码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
