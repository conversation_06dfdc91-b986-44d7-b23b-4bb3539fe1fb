#!/usr/bin/env python3
"""
异步文件I/O操作模块
提供高性能的异步文件读写操作，减少I/O阻塞
"""

import os
import json
import time
import threading
import queue
import fcntl
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import logging
from utils.logger import get_logger

class WriteOperation(Enum):
    """写入操作类型"""
    APPEND = "append"
    OVERWRITE = "overwrite"
    MERGE = "merge"

@dataclass
class WriteTask:
    """写入任务"""
    file_path: str
    data: Any
    operation: WriteOperation
    priority: int = 0  # 优先级，数字越大优先级越高
    callback: Optional[Callable] = None
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

class AsyncFileIO:
    """异步文件I/O管理器"""
    
    def __init__(self, config=None):
        self.logger = get_logger("async_file_io", logging.INFO)
        self.config = config or {}
        
        # 配置参数
        self.max_queue_size = self.config.get('max_queue_size', 1000)
        self.worker_threads = self.config.get('worker_threads', 2)
        self.batch_size = self.config.get('batch_size', 10)
        self.flush_interval = self.config.get('flush_interval', 5)
        
        # 写入队列（优先级队列）
        self.write_queue = queue.PriorityQueue(maxsize=self.max_queue_size)
        
        # 批量写入缓冲区
        self.write_buffers = {}  # file_path -> list of data
        self.buffer_locks = {}   # file_path -> threading.Lock
        
        # 工作线程
        self.workers = []
        self.running = False
        
        # 统计信息
        self.stats = {
            'writes_completed': 0,
            'writes_failed': 0,
            'bytes_written': 0,
            'avg_write_time': 0,
            'queue_size': 0
        }
        
        self.logger.info("异步文件I/O管理器初始化完成")
    
    def start(self):
        """启动异步I/O工作线程"""
        if self.running:
            self.logger.warning("异步I/O已在运行")
            return
        
        self.running = True
        
        # 启动工作线程
        for i in range(self.worker_threads):
            worker = threading.Thread(
                target=self._worker_loop,
                name=f"AsyncFileIO-Worker-{i+1}",
                daemon=True
            )
            worker.start()
            self.workers.append(worker)
        
        # 启动批量刷新线程
        flush_thread = threading.Thread(
            target=self._flush_loop,
            name="AsyncFileIO-Flush",
            daemon=True
        )
        flush_thread.start()
        self.workers.append(flush_thread)
        
        self.logger.info(f"异步I/O已启动，工作线程数: {self.worker_threads}")
    
    def stop(self):
        """停止异步I/O工作线程"""
        if not self.running:
            return
        
        self.running = False
        
        # 等待所有任务完成
        self.write_queue.join()
        
        # 刷新所有缓冲区
        self._flush_all_buffers()
        
        # 等待工作线程结束
        for worker in self.workers:
            if worker.is_alive():
                worker.join(timeout=5)
        
        self.logger.info("异步I/O已停止")
    
    def write_async(self, file_path: str, data: Any, operation: WriteOperation = WriteOperation.APPEND, 
                   priority: int = 0, callback: Optional[Callable] = None) -> bool:
        """异步写入文件
        
        Args:
            file_path: 文件路径
            data: 要写入的数据
            operation: 写入操作类型
            priority: 优先级（数字越大优先级越高）
            callback: 完成回调函数
            
        Returns:
            bool: 是否成功加入队列
        """
        try:
            task = WriteTask(file_path, data, operation, priority, callback)
            
            # 使用负优先级，因为PriorityQueue是最小堆
            self.write_queue.put((-priority, time.time(), task), timeout=1)
            
            self.stats['queue_size'] = self.write_queue.qsize()
            return True
            
        except queue.Full:
            self.logger.error(f"写入队列已满，丢弃写入任务: {file_path}")
            return False
        except Exception as e:
            self.logger.error(f"加入写入队列失败: {e}")
            return False
    
    def write_batch_async(self, file_path: str, data: Any, priority: int = 0) -> bool:
        """批量异步写入（数据会被缓冲，定期批量写入）
        
        Args:
            file_path: 文件路径
            data: 要写入的数据
            priority: 优先级
            
        Returns:
            bool: 是否成功加入缓冲区
        """
        try:
            # 获取或创建文件锁
            if file_path not in self.buffer_locks:
                self.buffer_locks[file_path] = threading.Lock()
            
            with self.buffer_locks[file_path]:
                # 获取或创建缓冲区
                if file_path not in self.write_buffers:
                    self.write_buffers[file_path] = []
                
                # 添加数据到缓冲区
                self.write_buffers[file_path].append(data)
                
                # 如果缓冲区达到批量大小，立即刷新
                if len(self.write_buffers[file_path]) >= self.batch_size:
                    self._flush_buffer(file_path, priority)
            
            return True
            
        except Exception as e:
            self.logger.error(f"批量写入缓冲失败: {e}")
            return False
    
    def _worker_loop(self):
        """工作线程主循环"""
        thread_name = threading.current_thread().name
        self.logger.info(f"{thread_name} 已启动")
        
        while self.running:
            try:
                # 获取写入任务
                try:
                    priority, timestamp, task = self.write_queue.get(timeout=1)
                except queue.Empty:
                    continue
                
                # 执行写入任务
                start_time = time.time()
                success = self._execute_write_task(task)
                write_time = time.time() - start_time
                
                # 更新统计信息
                if success:
                    self.stats['writes_completed'] += 1
                else:
                    self.stats['writes_failed'] += 1
                
                # 更新平均写入时间
                total_writes = self.stats['writes_completed'] + self.stats['writes_failed']
                if total_writes > 0:
                    self.stats['avg_write_time'] = (
                        (self.stats['avg_write_time'] * (total_writes - 1) + write_time) / total_writes
                    )
                
                # 执行回调
                if task.callback:
                    try:
                        task.callback(success, task)
                    except Exception as e:
                        self.logger.error(f"执行写入回调失败: {e}")
                
                # 标记任务完成
                self.write_queue.task_done()
                
            except Exception as e:
                self.logger.error(f"{thread_name} 处理写入任务异常: {e}")
        
        self.logger.info(f"{thread_name} 已停止")
    
    def _flush_loop(self):
        """批量刷新线程主循环"""
        self.logger.info("批量刷新线程已启动")
        
        while self.running:
            try:
                time.sleep(self.flush_interval)
                self._flush_all_buffers()
                
            except Exception as e:
                self.logger.error(f"批量刷新线程异常: {e}")
        
        self.logger.info("批量刷新线程已停止")
    
    def _execute_write_task(self, task: WriteTask) -> bool:
        """执行写入任务"""
        try:
            if task.operation == WriteOperation.APPEND:
                return self._append_to_file(task.file_path, task.data)
            elif task.operation == WriteOperation.OVERWRITE:
                return self._overwrite_file(task.file_path, task.data)
            elif task.operation == WriteOperation.MERGE:
                return self._merge_to_file(task.file_path, task.data)
            else:
                self.logger.error(f"未知的写入操作类型: {task.operation}")
                return False
                
        except Exception as e:
            self.logger.error(f"执行写入任务失败: {task.file_path}, 错误: {e}")
            return False
    
    def _append_to_file(self, file_path: str, data: Any) -> bool:
        """追加数据到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 读取现有数据
            existing_data = []
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    fcntl.flock(f, fcntl.LOCK_SH)
                    try:
                        content = f.read().strip()
                        if content:
                            existing_data = json.loads(content)
                    finally:
                        fcntl.flock(f, fcntl.LOCK_UN)
            
            # 合并数据
            if isinstance(data, list):
                existing_data.extend(data)
            else:
                existing_data.append(data)
            
            # 写入文件
            return self._write_json_atomic(file_path, existing_data)
            
        except Exception as e:
            self.logger.error(f"追加文件失败: {file_path}, 错误: {e}")
            return False
    
    def _overwrite_file(self, file_path: str, data: Any) -> bool:
        """覆盖文件"""
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            return self._write_json_atomic(file_path, data)
        except Exception as e:
            self.logger.error(f"覆盖文件失败: {file_path}, 错误: {e}")
            return False
    
    def _merge_to_file(self, file_path: str, data: Any) -> bool:
        """合并数据到文件（去重）"""
        try:
            # 读取现有数据
            existing_data = []
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    fcntl.flock(f, fcntl.LOCK_SH)
                    try:
                        content = f.read().strip()
                        if content:
                            existing_data = json.loads(content)
                    finally:
                        fcntl.flock(f, fcntl.LOCK_UN)
            
            # 合并并去重
            if isinstance(data, list):
                all_data = existing_data + data
            else:
                all_data = existing_data + [data]
            
            # 基于时间戳去重
            seen = set()
            unique_data = []
            for item in reversed(all_data):
                timestamp = item.get('time') or item.get('timestamp')
                if timestamp and timestamp not in seen:
                    unique_data.append(item)
                    seen.add(timestamp)
            
            unique_data.reverse()
            
            return self._write_json_atomic(file_path, unique_data)
            
        except Exception as e:
            self.logger.error(f"合并文件失败: {file_path}, 错误: {e}")
            return False
    
    def _write_json_atomic(self, file_path: str, data: Any) -> bool:
        """原子性写入JSON文件"""
        try:
            # 写入临时文件
            temp_file = f"{file_path}.tmp"
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            # 原子性替换
            with open(file_path, 'w', encoding='utf-8') as f:
                fcntl.flock(f, fcntl.LOCK_EX)
                try:
                    f.truncate(0)
                    with open(temp_file, 'r', encoding='utf-8') as temp:
                        content = temp.read()
                        f.write(content)
                        self.stats['bytes_written'] += len(content.encode('utf-8'))
                finally:
                    fcntl.flock(f, fcntl.LOCK_UN)
            
            # 删除临时文件
            if os.path.exists(temp_file):
                os.remove(temp_file)
            
            return True
            
        except Exception as e:
            self.logger.error(f"原子性写入失败: {file_path}, 错误: {e}")
            return False
    
    def _flush_buffer(self, file_path: str, priority: int = 0):
        """刷新指定文件的缓冲区"""
        if file_path not in self.write_buffers:
            return
        
        with self.buffer_locks[file_path]:
            buffer_data = self.write_buffers[file_path].copy()
            self.write_buffers[file_path].clear()
        
        if buffer_data:
            self.write_async(file_path, buffer_data, WriteOperation.APPEND, priority)
            self.logger.debug(f"刷新缓冲区: {file_path}, 数据条数: {len(buffer_data)}")
    
    def _flush_all_buffers(self):
        """刷新所有缓冲区"""
        for file_path in list(self.write_buffers.keys()):
            self._flush_buffer(file_path)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        stats['queue_size'] = self.write_queue.qsize()
        stats['buffer_count'] = len(self.write_buffers)
        return stats


# 全局异步文件I/O实例
_async_file_io = None

def get_async_file_io(config=None):
    """获取全局异步文件I/O实例"""
    global _async_file_io
    if _async_file_io is None:
        _async_file_io = AsyncFileIO(config)
    return _async_file_io

def start_async_file_io(config=None):
    """启动全局异步文件I/O"""
    async_io = get_async_file_io(config)
    async_io.start()
    return async_io

def stop_async_file_io():
    """停止全局异步文件I/O"""
    global _async_file_io
    if _async_file_io:
        _async_file_io.stop()
