#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试套件启动脚本
提供简单的命令行界面来启动各种测试
"""

import os
import sys
import time
import subprocess
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🧪 传感器系统测试套件 v2.0")
    print("=" * 60)
    print("功能: 功能测试 | 性能测试 | 压力测试 | 设备绑定测试")
    print("特点: 离线模式 | 自动清理 | 详细报告 | 完全兼容")
    print("=" * 60)

def check_environment():
    """检查测试环境"""
    print("🔍 检查测试环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    
    # 检查必要的目录
    required_dirs = [
        "test/api/v2",
        "test/api/device_binding",
        "test/api/stress_testing",
        "test/data",
        "test/reports"
    ]
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            print(f"📁 创建目录: {dir_path}")
            os.makedirs(dir_path, exist_ok=True)
    
    print("✅ 测试环境检查完成")
    return True

def show_menu():
    """显示菜单"""
    print("\n📋 请选择测试类型:")
    print("1. 🚀 运行完整测试套件")
    print("2. ⚡ 功能测试")
    print("3. 📊 性能测试")
    print("4. 💪 压力测试")
    print("5. 🔗 设备绑定测试")
    print("6. 🧹 数据清理")
    print("7. 📖 查看测试文档")
    print("8. ⚙️  测试配置")
    print("9. 🖥️  启动测试服务器")
    print("0. 🚪 退出")
    print("-" * 40)

def run_command(cmd, description):
    """运行命令"""
    print(f"\n🔄 {description}...")
    print(f"命令: {' '.join(cmd)}")
    print("-" * 60)

    try:
        # 使用实时输出，不捕获输出，让用户看到测试进度
        result = subprocess.run(cmd, check=True)
        print("-" * 60)
        print("✅ 执行成功")
        return True
    except subprocess.CalledProcessError as e:
        print("-" * 60)
        print(f"❌ 执行失败: 退出码 {e.returncode}")
        return False
    except Exception as e:
        print("-" * 60)
        print(f"❌ 执行异常: {e}")
        return False

def run_full_test_suite():
    """运行完整测试套件"""
    cmd = [sys.executable, "test/run_all_tests.py"]
    return run_command(cmd, "运行完整测试套件")

def run_function_tests():
    """运行功能测试"""
    cmd = [sys.executable, "test/run_all_tests.py", "--function-only"]
    return run_command(cmd, "运行功能测试")

def run_performance_tests():
    """运行性能测试"""
    cmd = [sys.executable, "test/run_all_tests.py", "--performance-only"]
    return run_command(cmd, "运行性能测试")

def run_stress_tests():
    """运行压力测试"""
    cmd = [sys.executable, "test/api/stress_testing/stress_test_runner.py"]
    return run_command(cmd, "运行压力测试")

def run_binding_tests():
    """运行设备绑定测试"""
    cmd = [sys.executable, "test/api/device_binding/binding_test_suite.py"]
    return run_command(cmd, "运行设备绑定测试")

def run_data_cleanup():
    """运行数据清理"""
    print("\n🧹 数据清理选项:")
    print("1. 标准清理 (7天)")
    print("2. 快速清理 (3天)")
    print("3. 紧急清理 (1天)")
    print("4. 生成清理报告")
    
    choice = input("请选择清理类型 (1-4): ").strip()
    
    if choice == "1":
        cmd = [sys.executable, "test/data/cleanup/auto_cleanup.py", "--retention-days", "7"]
    elif choice == "2":
        cmd = [sys.executable, "test/data/cleanup/auto_cleanup.py", "--retention-days", "3"]
    elif choice == "3":
        cmd = [sys.executable, "test/data/cleanup/auto_cleanup.py", "--emergency"]
    elif choice == "4":
        cmd = [sys.executable, "test/data/cleanup/auto_cleanup.py", "--report"]
    else:
        print("❌ 无效选择")
        return False
    
    return run_command(cmd, "执行数据清理")

def show_documentation():
    """显示测试文档"""
    print("\n📖 测试文档:")
    print("1. API v2.0 文档: test/docs/api_v2.md")
    print("2. 设备绑定测试: test/docs/binding_test.md")
    print("3. 性能测试指南: test/docs/performance_guide.md")
    print("4. 原版API文档: test/README.md")
    
    choice = input("请选择要查看的文档 (1-4): ").strip()
    
    doc_files = {
        "1": "test/docs/api_v2.md",
        "2": "test/docs/binding_test.md", 
        "3": "test/docs/performance_guide.md",
        "4": "test/README.md"
    }
    
    doc_file = doc_files.get(choice)
    if doc_file and os.path.exists(doc_file):
        try:
            with open(doc_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"\n📄 {doc_file}:")
                print("-" * 60)
                print(content[:2000])  # 显示前2000字符
                if len(content) > 2000:
                    print("\n... (文档较长，请直接查看文件获取完整内容)")
        except Exception as e:
            print(f"❌ 读取文档失败: {e}")
    else:
        print("❌ 文档文件不存在")

def start_test_server():
    """启动测试服务器"""
    print("\n🖥️ 启动测试服务器...")
    print("选择启动方式:")
    print("1. 启动完整系统 (推荐)")
    print("2. 仅启动测试服务器")

    choice = input("请选择 (1-2): ").strip()

    if choice == "1":
        print("\n🚀 启动完整系统...")
        print("这将启动主系统和测试接口 (v1: 5000端口, v2: 5001端口)")
        print("命令: python main.py --test")
        print("\n⚠️ 注意: 这将在前台运行，按 Ctrl+C 停止")

        confirm = input("确认启动? (y/N): ").strip().lower()
        if confirm == 'y':
            cmd = [sys.executable, "main.py", "--test"]
            return run_command(cmd, "启动完整系统")
        else:
            print("❌ 已取消")
            return False

    elif choice == "2":
        print("\n🖥️ 启动测试服务器...")
        print("这将仅启动v1测试服务器 (5000端口)")
        print("命令: python test/api/test_server.py")
        print("\n⚠️ 注意: 这将在前台运行，按 Ctrl+C 停止")

        confirm = input("确认启动? (y/N): ").strip().lower()
        if confirm == 'y':
            cmd = [sys.executable, "test/api/test_server.py"]
            return run_command(cmd, "启动测试服务器")
        else:
            print("❌ 已取消")
            return False

    else:
        print("❌ 无效选择")
        return False

def show_test_config():
    """显示测试配置"""
    print("\n⚙️ 测试配置:")
    print("1. 查看当前配置")
    print("2. 启用离线模式")
    print("3. 启用在线模式")
    print("4. 重置配置")

    choice = input("请选择配置操作 (1-4): ").strip()

    if choice == "1":
        config_file = "test/environments/offline/config.yaml"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    print(f"\n📄 {config_file}:")
                    print(f.read())
            except Exception as e:
                print(f"❌ 读取配置失败: {e}")
        else:
            print("❌ 配置文件不存在")

    elif choice == "2":
        print("✅ 离线模式已启用（默认配置）")
        print("测试数据将不会上传到云端")

    elif choice == "3":
        print("⚠️ 在线模式需要修改配置文件")
        print("请编辑: test/environments/offline/config.yaml")
        print("设置: mqtt.enabled = true")

    elif choice == "4":
        print("✅ 配置已重置为默认值（离线模式）")

    else:
        print("❌ 无效选择")

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    while True:
        show_menu()
        choice = input("请选择 (0-9): ").strip()

        if choice == "0":
            print("👋 再见！")
            break

        elif choice == "1":
            run_full_test_suite()

        elif choice == "2":
            run_function_tests()

        elif choice == "3":
            run_performance_tests()

        elif choice == "4":
            run_stress_tests()

        elif choice == "5":
            run_binding_tests()

        elif choice == "6":
            run_data_cleanup()

        elif choice == "7":
            show_documentation()

        elif choice == "8":
            show_test_config()

        elif choice == "9":
            start_test_server()

        else:
            print("❌ 无效选择，请重新输入")

        # 等待用户确认
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
