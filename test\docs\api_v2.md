# API测试接口 v2.0 文档

## 概述

API测试接口 v2.0 是升级版的测试系统，在保持与 v1.0 完全兼容的基础上，新增了以下功能：

- ✅ **离线测试模式** - 避免云端数据污染
- ✅ **设备绑定测试** - 完整的设备绑定功能测试
- ✅ **压力测试增强** - 更全面的压力测试套件
- ✅ **性能监控** - 详细的性能指标监控
- ✅ **自动数据清理** - 测试数据自动管理

## 快速开始

### 1. 启动测试服务器

```bash
# 启动主系统（会同时启动v1和v2测试接口）
python main.py --test

# v1.0 接口: http://localhost:5000
# v2.0 接口: http://localhost:5001
```

### 2. 运行完整测试套件

```bash
# 运行所有测试
python test/run_all_tests.py

# 运行特定测试
python test/run_all_tests.py --function-only
python test/run_all_tests.py --performance-only
python test/run_all_tests.py --stress-only
python test/run_all_tests.py --binding-only
```

## 新增接口

### 离线数据测试接口

#### 传感器数据离线测试
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-API-Key: test_key_123" \
  -d '{"temperature": 25.5, "humidity": 60.2, "co2": 800}' \
  http://localhost:5001/api/v2/data/sensor/offline
```

#### CO2状态离线测试
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-API-Key: test_key_123" \
  -d '{"status": 1, "round": 1, "phase_remaining": 110}' \
  http://localhost:5001/api/v2/data/co2/offline
```

#### 设备自检离线测试
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-API-Key: test_key_123" \
  -d '{"component": "temp", "status": 0, "message": "正常"}' \
  http://localhost:5001/api/v2/data/check/offline
```

#### 蚊子检测离线测试
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-API-Key: test_key_123" \
  -d '{"detection_count": 5, "confidence": 0.95}' \
  http://localhost:5001/api/v2/data/mosquito/offline
```

### 增强监控接口

#### 详细线程状态监控
```bash
curl -H "X-API-Key: test_key_123" \
  http://localhost:5001/api/v2/monitor/threads/detail
```

#### 内存分析
```bash
curl -H "X-API-Key: test_key_123" \
  http://localhost:5001/api/v2/monitor/memory/analysis
```

#### 队列健康度监控
```bash
curl -H "X-API-Key: test_key_123" \
  http://localhost:5001/api/v2/monitor/queues/health
```

### 配置管理接口

#### 获取测试配置
```bash
curl -H "X-API-Key: test_key_123" \
  http://localhost:5001/api/v2/config
```

#### 更新测试配置
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-API-Key: test_key_123" \
  -d '{"offline_mode": true, "mock_platform_reply": true}' \
  http://localhost:5001/api/v2/config
```

## 离线测试模式

### 特点

- ✅ **完全隔离** - 测试数据不会上传到云端
- ✅ **模拟回复** - 自动模拟平台回复
- ✅ **数据标识** - 所有测试数据添加 `TEST-` 前缀
- ✅ **自动清理** - 7天后自动清理测试数据

### 配置

```yaml
# test/environments/offline/config.yaml
test_environment:
  name: "offline"
  
mqtt:
  enabled: false
  mock_mode: true
  mock_success_rate: 0.95

device:
  id_prefix: "TEST-"
  mock_sensors: true
```

### 使用方法

```bash
# 设置环境变量启用离线模式
export TEST_MODE=offline

# 或在代码中配置
TEST_CONFIG["offline_mode"] = True
```

## 性能基准

### 目标指标

| 指标 | 目标值 | 说明 |
|------|--------|------|
| 传感器数据吞吐量 | > 1000条/分钟 | 数据处理能力 |
| CO2状态响应时间 | < 100ms | 状态变化响应 |
| 设备自检完成时间 | < 30秒 | 完整自检时间 |
| 内存使用峰值 | < 512MB | 内存占用限制 |
| CPU使用率平均 | < 30% | CPU负载控制 |
| 并发用户数 | > 100 | 并发处理能力 |

### 性能测试

```bash
# 运行性能基准测试
python test/local/performance/benchmark_test.py

# 运行压力测试
python test/api/stress_testing/stress_test_runner.py
```

## 数据管理

### 自动清理

```bash
# 手动清理过期数据
python test/data/cleanup/auto_cleanup.py

# 紧急清理（磁盘空间不足时）
python test/data/cleanup/auto_cleanup.py --emergency

# 生成清理报告
python test/data/cleanup/auto_cleanup.py --report
```

### 数据结构

```
test/data/offline/
├── sensor/2025-01-28/     # 传感器测试数据
├── co2/2025-01-28/        # CO2状态测试数据
├── check/2025-01-28/      # 设备自检测试数据
└── mosquito/2025-01-28/   # 蚊子检测测试数据
```

## 兼容性

### 向后兼容

- ✅ **完全兼容** - v1.0 的所有接口在 v2.0 中仍然可用
- ✅ **并行运行** - v1.0 和 v2.0 可以同时运行
- ✅ **渐进迁移** - 可以逐步迁移到新版本

### 迁移指南

1. **保持现有测试** - 现有的 v1.0 测试脚本无需修改
2. **逐步添加新功能** - 根据需要添加 v2.0 的新功能
3. **配置管理** - 使用新的配置管理接口优化测试环境

## 故障排除

### 常见问题

#### 1. 测试服务器启动失败
```bash
# 检查端口占用
netstat -tulpn | grep :5001

# 查看日志
tail -f logs/test_server_v2.log
```

#### 2. 离线模式不生效
```bash
# 检查配置
curl -H "X-API-Key: test_key_123" http://localhost:5001/api/v2/config

# 重新设置配置
curl -X POST -H "Content-Type: application/json" -H "X-API-Key: test_key_123" \
  -d '{"offline_mode": true}' http://localhost:5001/api/v2/config
```

#### 3. 测试数据未清理
```bash
# 手动执行清理
python test/data/cleanup/auto_cleanup.py --retention-days 1

# 检查磁盘空间
df -h
```

## 最佳实践

### 测试流程

1. **环境准备** - 启动测试服务器，检查配置
2. **功能测试** - 验证基础功能正常
3. **性能测试** - 检查性能指标达标
4. **压力测试** - 验证系统稳定性
5. **数据清理** - 清理测试数据

### 测试策略

- ✅ **分层测试** - 单元测试 → 集成测试 → 系统测试
- ✅ **自动化** - 使用脚本自动执行测试
- ✅ **监控** - 实时监控系统状态
- ✅ **报告** - 生成详细的测试报告

## 支持

如有问题，请查看：
- 测试日志: `test/logs/`
- 测试报告: `test/reports/`
- 配置文件: `test/environments/`
