import paho.mqtt.client as mqtt
import json
import time
import threading
import datetime
import os
import sys
import uuid
import socket
import queue
import math
import hmac
import hashlib
import random
import string
import requests
import fcntl
import struct
import subprocess # Added for ip command
import shutil # Added for shutil.move
from enum import Enum
from utils.config_loader import load_config
from utils.logger import get_logger

# 定义连接状态枚举
class ConnectionState(Enum):
    DISCONNECTED = 0
    CONNECTING = 1
    CONNECTED = 2

class MQTTClient:
    def __init__(self):
        # 初始化日志
        self.logger = get_logger("mqtt_client")
        
        # 加载配置
        self.config = load_config()
        if not self.config:
            self.logger.error("无法加载配置文件")
            raise ValueError("无法加载配置文件")
        
        # 保存配置
        self.mqtt_config = self.config['mqtt']
        self.storage_config = self.config['data_storage']
        
        # 设备绑定配置
        self.device_bind_url = self.mqtt_config.get('device_bind_url', 'https://vsp.aixxc.com/api/v1/device/bind')
        self.device_type = self.mqtt_config.get('device_type', 'mosquito')
        self.hmac_key = self.mqtt_config.get('hmac_key', 'MQ2025_7f8e9d6c5b4a3f2e1d0c9b8a7f6e5d4c3b2a1f0e9d8c7b6a5f4e3d2c1b0a9f8e7d6c')
        self.credential_retry_interval = self.mqtt_config.get('credential_retry_interval', 30)  # 初始重试间隔（秒）
        self.credential_max_retry_interval = self.mqtt_config.get('credential_max_retry_interval', 3600)  # 最大重试间隔（秒）
        self.credential_retry_count = 0
        self.credential_max_retry_attempts = self.mqtt_config.get('credential_max_retry_attempts', 10)  # 最大尝试次数
        self._credential_timer = None
        self._credential_lock = threading.Lock()
        
        # 连接模式设置
        self.enable_mqtt_connection = self.mqtt_config.get('enable_mqtt_connection', True)
        
        # 连接状态
        self.connected = False
        self.connection_state = ConnectionState.DISCONNECTED
        self.network_available = True  # 初始假设网络可用
        
        # 连接重试设置
        self.retry_count = 0
        self.reconnect_count = 0  # 添加重连计数器初始化
        self.max_retries = self.mqtt_config.get('max_retries', 10)
        self.max_reconnect_attempts = self.mqtt_config.get('max_reconnect_attempts', 0)  # 0表示无限重试
        self.base_retry_interval = self.mqtt_config.get('reconnect_base_interval', 5)  # 基础重试间隔
        self.retry_interval = self.mqtt_config.get('reconnect_base_interval', 5)
        self.max_retry_interval = self.mqtt_config.get('reconnect_max_interval', 300)
        self.network_check_interval = self.mqtt_config.get('network_check_interval', 30)
        self._reconnect_scheduled = False  # 重连调度标志，避免重复调度
        self._reconnect_lock = threading.Lock()  # 重连锁，确保线程安全
        self._reconnect_timer = None  # 重连定时器引用
        self._connecting = False  # 表示是否正在尝试连接，避免多次同时连接
        
        # 创建MQTT客户端实例（将在绑定成功后替换）
        client_id = f"mqtt-client-{random.randint(0, 1000)}"
        self.client = mqtt.Client(client_id=client_id, clean_session=True)
        
        # 设置回调函数
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.client.on_disconnect = self.on_disconnect
        
        # 禁用Paho MQTT的自动重连机制，完全使用我们自己的重连逻辑
        self.client.reconnect_delay_set(min_delay=self.base_retry_interval, max_delay=self.max_retry_interval)
        self.client.reconnect_on_failure = False  # 禁用Paho的自动重连
        
        # 属性初始化（将在绑定成功后设置）
        self.client_id = None
        self.username = None
        self.password = None
        self.broker = None
        self.port = None
        self.device_id = None
            
        # 运行标志
        self.running = False
        self.upload_thread = None
        self.network_check_thread = None

        # 立即上报标记机制
        self.immediate_upload_flags = {
            'co2_status': False,
            'device_check': False,
            'sensor_data': False
        }
        self.immediate_upload_lock = threading.Lock()

        # 移除消息上传间隔控制，不再需要间隔限制
        
        # 上传时间戳记录，用于避免短时间内重复上传
        self._last_sensor_upload_time = 0
        self._last_co2_upload_time = 0
        self._last_check_upload_time = 0
        self._last_sensor_mark_time = 0
        self._last_co2_mark_time = 0
        self._last_check_mark_time = 0
        
        # 上传成功标志，用于定时上传时间更新
        self.sensor_data_upload_success = False
        self.co2_status_upload_success = False
        self.device_check_upload_success = False

        # 已上传数据跟踪（防止重复上传）
        self.uploaded_sensor_timestamps = set()
        self.uploaded_timestamps_lock = threading.Lock()

        # 延迟归档标记
        self.sensor_archive_pending = False
        self.last_archive_attempt = 0

        # 设备状态
        self.device_status = {
            "power_status": 1,  # 1: 开机, 2: 关机, 3: 重启
            "components": {}    # 各组件状态
        }
        
        # 内存队列缓存
        sensor_queue_size = self.mqtt_config.get('sensor_queue_size', 100)
        error_queue_size = self.mqtt_config.get('error_queue_size', 50)
        co2_queue_size = self.mqtt_config.get('co2_queue_size', 20)
        check_queue_size = self.mqtt_config.get('check_queue_size', 50)
        mosquito_queue_size = self.mqtt_config.get('mosquito_queue_size', 50)
        
        self.sensor_data_queue = queue.Queue(maxsize=sensor_queue_size)  # 待处理传感器数据队列
        self.error_data_queue = queue.Queue(maxsize=error_queue_size)    # 待处理错误数据队列
        self.co2_status_queue = queue.Queue(maxsize=co2_queue_size)      # CO2状态数据队列
        self.check_data_queue = queue.Queue(maxsize=check_queue_size)    # 设备自检数据队列
        # 蚊虫数据不再使用队列，直接通过文件监控器处理
        
        # 队列锁
        self.co2_status_queue_lock = threading.Lock()  # CO2状态队列锁
        
        # 性能监控指标
        self.sensor_data_processed_count = 0  # 已保存到本地的传感器数据总数
        self.sensor_data_success_count = 0    # 成功保存到本地的传感器数据数量
        self.sensor_data_error_count = 0      # 错误的传感器数据数量
        self.sensor_data_total_count = 0      # 接口调用总次数
        self.sensor_data_process_time = 0     # 处理平均时间(毫秒)
        
        self.error_data_processed_count = 0   # 已保存到本地的错误数据总数
        self.error_data_process_time = 0      # 错误数据处理平均时间(毫秒)
        
        # API性能统计
        self.api_single_stats = {"count": 0, "total_time": 0, "avg_time": 0}
        self.api_batch_stats = {"count": 0, "total_time": 0, "avg_time": 0}
        self.api_massive_stats = {"count": 0, "total_items": 0, "rate": 0}
        
        # 组件就绪标志
        self.components_ready = {
            "co2_controller": False,
            "sensor_collector": False,
            "device_health": False
        }
        
        # 数据文件路径
        self.data_dir = os.path.join(self.storage_config['data_dir'])
        
        # 传感器数据目录
        self.sensors_dir = os.path.join(self.data_dir, self.storage_config.get('sensors_dir', 'sensors'))
        self.sensors_current_dir = os.path.join(self.sensors_dir, 'current')
        self.sensors_history_dir = os.path.join(self.sensors_dir, 'history')
        
        # 确保传感器目录存在
        for directory in [self.sensors_dir, self.sensors_current_dir, self.sensors_history_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                self.logger.info(f"创建目录: {directory}")
        
        # 传感器数据文件
        self.sensor_data_file = os.path.join(self.sensors_current_dir, self.storage_config.get('sensor_data_file', 'sensor_data.json'))
        self.sensor_errors_file = os.path.join(self.sensors_current_dir, self.storage_config.get('sensor_errors_file', 'sensor_errors.json'))
        self.sensor_data_archive_file = os.path.join(self.sensors_history_dir, self.storage_config.get('sensor_data_archive_file', 'sensor_data_archive.json'))
        self.sensor_errors_archive_file = os.path.join(self.sensors_history_dir, self.storage_config.get('sensor_errors_archive_file', 'sensor_errors_archive.json'))
        
        # CO2控制器数据目录
        self.co2_dir = os.path.join(self.data_dir, self.storage_config.get('devices_dir', 'devices'))
        self.co2_current_dir = os.path.join(self.co2_dir, 
                                         self.storage_config.get('co2_current_dir', 'current'))
        self.co2_history_dir = os.path.join(self.co2_dir, 
                                         self.storage_config.get('co2_history_dir', 'history'))
        
        # 确保CO2控制器目录存在
        for directory in [self.co2_dir, self.co2_current_dir, self.co2_history_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                self.logger.info(f"创建目录: {directory}")
        
        # CO2控制器数据文件（CO2状态现在使用独立文件存储）
        self.co2_errors_file = os.path.join(self.co2_current_dir, self.storage_config.get('co2_errors_file', 'co2_errors.json'))
        self.co2_errors_archive_file = os.path.join(self.co2_history_dir, self.storage_config.get('co2_errors_archive_file', 'co2_errors_archive.json'))
        
        # 设备自检数据目录
        self.check_dir = os.path.join(self.data_dir, self.storage_config.get('check_dir', 'check'))
        self.check_current_dir = os.path.join(self.check_dir, 
                                           self.storage_config.get('check_current_dir', 'current'))
        self.check_history_dir = os.path.join(self.check_dir, 
                                           self.storage_config.get('check_history_dir', 'history'))
        
        # 确保设备自检目录存在
        for directory in [self.check_dir, self.check_current_dir, self.check_history_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                self.logger.info(f"创建目录: {directory}")
        
        # 设备自检数据现在使用独立文件存储，不再需要数组文件
        
        # 确保文件存在
        self._ensure_files_exist()
        
        # 消息确认跟踪
        self.pending_confirmations = {}  # 设备ID -> 待确认项列表
        self.confirmation_lock = threading.Lock()
        
        # 设备状态
        self.device_status = {
            "power_status": 1  # 1: 开机, 2: 关机
        }
        
        # 设备操作回调
        self.device_callbacks = {
            "power_on": None,    # 开机回调
            "power_off": None,   # 关机回调
            "restart": None      # 重启回调
        }
        
        # 消息去重配置
        self.message_expiry = self.mqtt_config.get('message_expiry', 3600)  # 消息ID过期时间（秒）
        self.max_message_cache = self.mqtt_config.get('max_message_cache', 1000)  # 最大消息ID缓存数量
        self.sent_messages = {}  # 已发送消息ID缓存
        self.sent_messages_lock = threading.Lock()  # 消息缓存锁
        self.file_operation_lock = threading.Lock()  # 文件操作锁
    
    def _wait_for_message_interval(self):
        """移除消息上传间隔控制，直接返回"""
        pass

    def _ensure_files_exist(self):
        """确保数据文件存在"""
        files = [
            self.sensor_data_file, self.sensor_errors_file,
            self.sensor_data_archive_file, self.sensor_errors_archive_file,
            self.co2_errors_file, self.co2_errors_archive_file
        ]
        
        for file_path in files:
            if not os.path.exists(file_path):
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump([], f)
                self.logger.info(f"创建文件: {file_path}")
        
    def start(self):
        """启动MQTT客户端和数据上传线程"""
        self.running = True
        
        # 启动网络检测线程
        self.network_check_thread = threading.Thread(target=self._network_check_loop, daemon=True)
        self.network_check_thread.start()
        # 启动上传线程
        self.upload_thread = threading.Thread(target=self.upload_loop, daemon=True)
        self.upload_thread.start()
        
        # 尝试加载本地凭证
        if self._load_credentials():
            # 使用加载的凭证直接连接
            connection_thread = threading.Thread(target=self._reset_mqtt_client, daemon=True)
            connection_thread.start()
        else:
            # 本地无凭证或凭证无效，请求新凭证
            binding_thread = threading.Thread(target=self._request_device_binding, daemon=True)
            binding_thread.start()
        
        # 不等待凭证获取完成，直接返回，允许其他组件并行运行
        return True
        
    def stop(self):
        """停止MQTT客户端和数据上传线程，使用非阻塞方式"""
        # 先标记系统为停止状态，避免任何新的操作
        self.running = False
        
        # 确保不会有新的重试和连接操作
        with self._reconnect_lock:
            self._reconnect_scheduled = False
            self._connecting = False
        
        # 取消所有定时器
        self._cancel_timers()
        self.logger.debug("所有定时器已取消")
        
        # 强制终止所有后台线程
        active_threads = threading.enumerate()
        background_threads = []
        
        # 识别所有与MQTT客户端相关的后台线程
        for thread in active_threads:
            # 排除主线程和当前线程
            if thread != threading.main_thread() and thread != threading.current_thread():
                # 检查线程名称或其他特征，识别出MQTT客户端创建的线程
                if (thread.name.startswith("credential_retry_") or 
                    thread.name.startswith("mqtt_") or
                    "credential" in thread.name.lower() or
                    "connect" in thread.name.lower()):
                    background_threads.append(thread)
                    self.logger.debug(f"检测到MQTT相关后台线程: {thread.name}，将忽略它")
        
        # 不等待这些线程，立即继续
        if background_threads:
            self.logger.info(f"检测到 {len(background_threads)} 个MQTT相关后台线程，将忽略它们")
        
        # 处理主要线程，但使用非常短的超时
        threads = []
        short_timeout = 0.5  # 缩短每个线程的等待时间为0.5秒
        
        # 停止网络检测线程
        if self.network_check_thread and self.network_check_thread.is_alive():
            self.logger.info("正在停止网络检测线程...")
            t = threading.Thread(target=lambda: self.network_check_thread.join(timeout=short_timeout))
            t.daemon = True
            t.start()
            threads.append((t, "网络检测线程"))
        else:
            self.logger.debug("网络检测线程不存在或已停止")
        
        # 停止上传线程
        if self.upload_thread and self.upload_thread.is_alive():
            self.logger.info("正在停止上传线程...")
            t = threading.Thread(target=lambda: self.upload_thread.join(timeout=short_timeout))
            t.daemon = True
            t.start()
            threads.append((t, "上传线程"))
        else:
            self.logger.debug("上传线程不存在或已停止")
        
        # 如果连接存在，立即关闭，不要等待断开连接的完成
        if self.client and self.connected:
            self.logger.info("正在断开MQTT连接...")
            try:
                # 添加兼容性处理
                try:
                    # 先尝试新版本API
                    self.client.loop_stop(force=True)
                    self.logger.debug("已停止MQTT客户端循环(force=True)")
                except (TypeError, AttributeError) as e:
                    self.logger.debug(f"使用兼容模式停止MQTT客户端循环: {str(e)}")
                    self.client.loop_stop()
                    self.logger.debug("已停止MQTT客户端循环(兼容模式)")
                
                # 创建一个超短超时的断开连接尝试
                def quick_disconnect():
                    try:
                        disconnect_future = self.client.disconnect()
                        # 对于某些MQTT客户端库，断开操作可能是同步的，不返回future
                        if disconnect_future:
                            # 如果是异步操作，只等待很短时间
                            disconnect_future.wait_for_completion(timeout=0.5)
                    except:
                        pass
                
                t = threading.Thread(target=quick_disconnect)
                t.daemon = True
                t.start()
                # 只等待极短时间
                t.join(0.2)
            except Exception as e:
                self.logger.warning(f"断开MQTT连接时发生错误: {e}")
        else:
            self.logger.debug("MQTT未连接，跳过断开连接步骤")
        
        # 无论连接状态如何，确保标记为已断开
        self.connected = False
        self.connection_state = ConnectionState.DISCONNECTED
        
        # 等待主要线程非常短的时间
        overall_timeout = 1.0  # 总超时减少到1秒
        start_time = time.time()
        remaining_threads = list(threads)
        
        if not remaining_threads:
            self.logger.info("没有需要等待的线程，MQTT客户端停止完成")
            return
        
        while remaining_threads and time.time() - start_time < overall_timeout:
            for t, name in list(remaining_threads):
                if not t.is_alive():
                    self.logger.debug(f"{name}已完成")
                    remaining_threads.remove((t, name))
            if remaining_threads:
                time.sleep(0.1)  # 更短的等待时间
        
        # 如果仍有线程在运行，记录警告但继续退出
        if remaining_threads:
            thread_names = [name for _, name in remaining_threads]
            self.logger.warning(f"以下线程未能在{overall_timeout}秒内完成，将被忽略: {', '.join(thread_names)}")
        
        # 确保最终停止任何可能仍在运行的客户端循环
        if hasattr(self, 'client') and self.client:
            try:
                self.client = None
            except:
                pass
        
        self.logger.info("MQTT客户端已停止")
    
    def _cancel_timers(self):
        """取消所有定时器"""
        # 取消凭证获取定时器
        with self._credential_lock:
            if self._credential_timer:
                try:
                    self._credential_timer.cancel()
                    self.logger.debug("凭证重试定时器已取消")
                except Exception as e:
                    self.logger.warning(f"取消凭证重试定时器时发生错误: {e}")
                self._credential_timer = None
        
        # 取消重连定时器
        with self._reconnect_lock:
            if self._reconnect_timer:
                try:
                    self._reconnect_timer.cancel()
                    self.logger.debug("重连定时器已取消")
                except Exception as e:
                    self.logger.warning(f"取消重连定时器时发生错误: {e}")
                self._reconnect_timer = None
        
    def _network_check_loop(self):
        """网络检测循环，定期检查网络连接状态"""
        check_interval = self.network_check_interval  # 使用配置的网络检测间隔
        while self.running:
            try:
                # 检查网络连接
                network_available = self._check_network_connection()
                
                # 如果网络状态发生变化
                if network_available != self.network_available:
                    if network_available:
                        # 使用线程锁保护，确保与重连机制协调
                        with self._reconnect_lock:
                            self.network_available = True  # 先更新网络状态
                            # 只有在未连接、未调度重连、未连接中、且系统运行时才尝试连接
                            if (self.connection_state != ConnectionState.CONNECTED and
                                not self._reconnect_scheduled and
                                not self._connecting and
                                self.running):
                                self.logger.info(f"网络检测恢复，尝试重新连接MQTT服务器，当前连接状态: {self.connection_state}")
                                self.connect()
                            else:
                                # 区分不同情况的日志
                                if self.connection_state == ConnectionState.CONNECTED:
                                    self.logger.debug(f"网络检测恢复，MQTT连接已正常，无需重连")
                                else:
                                    self.logger.info(f"网络检测恢复，但已存在连接/重连计划，当前连接状态: {self.connection_state}, 重连调度: {self._reconnect_scheduled}, 连接中: {self._connecting}")
                    else:
                        # 检查是否是真正的网络断开还是MQTT连接仍然正常
                        if self.connection_state == ConnectionState.CONNECTED and self.connected:
                            self.logger.warning(f"网络检测失败但MQTT连接仍然正常，可能是网络抖动或服务器负载高，当前连接状态: {self.connection_state}")
                        else:
                            self.logger.warning(f"网络连接断开，MQTT服务将在网络恢复后自动重连，当前连接状态: {self.connection_state}")
                        self.network_available = False
                else:
                    # 检查网络检测与MQTT连接状态的一致性
                    if not network_available and (self.connection_state == ConnectionState.CONNECTED or self.connected):
                        # 获取失败次数来判断严重程度
                        failure_count = getattr(self, '_network_failure_count', 0)
                        if failure_count >= 2:  # 连续失败2次以上才警告
                            self.logger.warning(f"网络检测连续失败{failure_count}次，但MQTT连接仍正常，可能是网络抖动或服务器负载高")
                        else:
                            self.logger.debug(f"网络检测失败但MQTT连接正常，失败次数: {failure_count}/3")
                
                # 等待下一次检查，缩短检查间隔，提高响应性
                wait_time = min(check_interval, 5)  # 不超过5秒检查一次运行状态
                for _ in range(wait_time):
                    if not self.running:
                        self.logger.debug("检测到停止信号，退出网络检测循环")
                        break
                    time.sleep(1)
                    
                # 检查并同步连接状态
                self._check_connection_status()
                
            except Exception as e:
                self.logger.error(f"网络检测循环发生错误: {e}", exc_info=True)
                # 缩短错误恢复时间，提高响应停止信号的能力
                for _ in range(5):  # 最多等待5秒而不是30秒
                    if not self.running:
                        self.logger.debug("检测到停止信号，退出网络检测循环错误恢复")
                        break
                    time.sleep(1)
    
    def _check_network_connection(self):
        """检查网络连接状态
        
        Returns:
            bool: 网络是否可用
        """
        try:
            # 初始化连续失败计数器
            if not hasattr(self, '_network_failure_count'):
                self._network_failure_count = 0

            # 尝试连接到MQTT服务器所在的主机
            if not self.broker or not self.port:
                self.logger.debug("MQTT服务器配置未设置，无法检查网络连接")
                return True  # 先假设网络正常，等获取凭证后再真正检查

            # 创建socket连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((self.broker, self.port))
            sock.close()

            # 定义临时错误码（不视为网络断开）
            TEMPORARY_ERRORS = [11, 115]  # EAGAIN/EWOULDBLOCK, EINPROGRESS

            # 如果连接成功，result应该为0
            if result == 0:
                # 连接成功，重置失败计数
                self._network_failure_count = 0
                self.logger.debug(f"网络连接正常，可以访问MQTT服务器 {self.broker}:{self.port}")
                # 使用同步连接状态方法来检查并修正状态不一致
                self.sync_connection_state()
                return True
            elif result in TEMPORARY_ERRORS:
                # 临时错误，不计入失败次数
                self.logger.debug(f"MQTT服务器暂时无法接受新连接(错误码{result})，现有连接正常")
                return True
            else:
                # 真正的连接失败，增加失败计数
                self._network_failure_count += 1
                self.logger.warning(f"网络连接测试失败，错误码: {result}，连续失败次数: {self._network_failure_count}")

                # 连续3次失败才判定为网络断开
                if self._network_failure_count >= 3:
                    self.logger.error(f"网络连接连续{self._network_failure_count}次失败，判定为网络断开")
                    return False
                else:
                    self.logger.info(f"网络连接失败但未达到阈值({self._network_failure_count}/3)，仍视为网络可用")
                    return True

        except Exception as e:
            # 异常也计入失败次数
            self._network_failure_count = getattr(self, '_network_failure_count', 0) + 1
            self.logger.error(f"网络连接检查异常: {e}，连续失败次数: {self._network_failure_count}")

            # 异常也需要连续3次才判定为网络断开
            if self._network_failure_count >= 3:
                return False
            else:
                return True

    def connect(self):
        """连接到MQTT服务器，使用指数退避重连策略"""
        # 使用锁保护状态检查和设置
        with self._reconnect_lock:
            # 检查是否已经在连接中
            if self._connecting:
                self.logger.debug("连接正在进行中，跳过此次连接尝试")
                return False

            # 检查凭证是否已经获取
            if not self.client_id or not self.username or not self.password or not self.broker or not self.port:
                self.logger.warning("MQTT凭证尚未获取，无法连接")
                return False

            # 设置连接中标志
            self._connecting = True
            self.connection_state = ConnectionState.CONNECTING
            
        try:
            # 创建MQTT客户端，使用MQTT v3.1.1协议
            client_id = self.client_id

            # 检查是否需要清理旧客户端
            if hasattr(self, 'client') and self.client:
                try:
                    self.client.disconnect()
                    self.client.loop_stop()
                except:
                    pass

            self.client = mqtt.Client(client_id=client_id, protocol=mqtt.MQTTv311)

            # 设置认证信息
            self.client.username_pw_set(self.username, self.password)

            # 设置回调函数
            self.client.on_connect = self.on_connect
            self.client.on_disconnect = self.on_disconnect
            self.client.on_message = self.on_message

            # 禁用Paho MQTT的自动重连，使用我们自己的重连逻辑
            self.client.reconnect_delay_set(min_delay=self.base_retry_interval, max_delay=self.max_retry_interval)
            self.client.reconnect_on_failure = False
            
            # 连接到服务器
            self.logger.info(f"开始连接MQTT服务器: {self.broker}:{self.port}")
            self.logger.info(f"连接参数: client_id={client_id}, username={self.username[:10]}..., keepalive={self.mqtt_config.get('keep_alive', 60)}")

            # 简单的网络连通性检查
            try:
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((self.broker, self.port))
                sock.close()
                if result == 0:
                    self.logger.info(f"网络连通性检查通过: {self.broker}:{self.port}")
                else:
                    self.logger.warning(f"网络连通性检查失败: {self.broker}:{self.port}, 错误码: {result}")
            except Exception as e:
                self.logger.warning(f"网络连通性检查异常: {e}")

            try:
                # 使用从配置中读取的keepalive值
                keepalive = self.mqtt_config.get('keep_alive', 60)

                self.client.connect(
                    self.broker,
                    self.port,
                    keepalive=keepalive
                )

                # 启动MQTT循环
                self.client.loop_start()
            except Exception as e:
                self.logger.error(f"连接MQTT服务器失败: {e}")
                with self._reconnect_lock:
                    self.connected = False
                    self.connection_state = ConnectionState.DISCONNECTED
                    self._connecting = False  # 清除连接中标志
                    if not self._reconnect_scheduled and self.running:
                        self._schedule_reconnect()
                    return False
            
            # 等待连接成功
            timeout = self.mqtt_config.get('connection_timeout', 30)  # 从配置中读取超时时间，默认30秒
            start_time = time.time()
            self.logger.info(f"等待MQTT连接建立，超时时间: {timeout}秒...")

            while not self.connected and time.time() - start_time < timeout:
                elapsed = time.time() - start_time
                time.sleep(0.2)  # 更频繁地检查

                # 每5秒记录一次等待状态
                if int(elapsed) % 5 == 0 and elapsed > 0:
                    self.logger.debug(f"等待连接中... 已等待{elapsed:.1f}秒/{timeout}秒")

                # 检查实际连接状态
                if hasattr(self.client, 'is_connected') and callable(self.client.is_connected):
                    if self.client.is_connected():
                        self.logger.info("检测到MQTT客户端已连接")
                        self.connected = True
                        self.connection_state = ConnectionState.CONNECTED
                        break

            if self.connected:
                elapsed_time = time.time() - start_time
                self.logger.info(f"已成功连接到MQTT服务器，耗时: {elapsed_time:.2f}秒")
                # 获取锁来更新状态
                with self._reconnect_lock:
                    self.connection_state = ConnectionState.CONNECTED
                    self.reconnect_count = 0  # 重置重连计数
                    self._connecting = False  # 清除连接中标志
                # 处理队列中缓存的数据
                self._process_queued_data()

                return True
            else:
                elapsed_time = time.time() - start_time
                client_status = "未知"
                if hasattr(self.client, 'is_connected') and callable(self.client.is_connected):
                    client_status = "已连接" if self.client.is_connected() else "未连接"

                self.logger.error(f"连接MQTT服务器超时: 已等待{elapsed_time:.1f}秒, 连接状态={self.connected}, 客户端状态={client_status}")
                # 获取锁来更新状态
                with self._reconnect_lock:
                    self.connection_state = ConnectionState.DISCONNECTED
                    self._connecting = False  # 清除连接中标志
                    if not self._reconnect_scheduled and self.running:
                        self._schedule_reconnect()
                return False
                
        except Exception as e:
            self.logger.error(f"连接过程发生异常: {e}", exc_info=True)
            # 获取锁来更新状态
            with self._reconnect_lock:
                self.connection_state = ConnectionState.DISCONNECTED
                self._connecting = False  # 清除连接中标志
                if not self._reconnect_scheduled and self.running:
                    self._schedule_reconnect()
            return False
        finally:
            # 连接完成后进行最终检查
            if hasattr(self.client, 'is_connected') and self.client and callable(self.client.is_connected):
                actual_connected = self.client.is_connected()
                if actual_connected != self.connected:
                    self.logger.warning(f"连接状态不一致！物理连接={actual_connected}，记录状态={self.connected}")
            

    
    def sync_connection_state(self):
        """同步客户端的实际连接状态与记录的状态"""
        if not self.client or not hasattr(self.client, 'is_connected') or not callable(self.client.is_connected):
            return
            
        try:
            with self._reconnect_lock:  # 使用现有的重连锁来保护状态更新
                actual_state = self.client.is_connected()
                if actual_state != self.connected:
                    self.logger.warning(f"检测到连接状态不一致：客户端状态={actual_state}，记录状态={self.connected}，正在同步")
                    
                    # 更新连接标志
                    self.connected = actual_state
                    
                    # 更新连接状态枚举
                    if actual_state:
                        self.connection_state = ConnectionState.CONNECTED
                        self.logger.info("连接状态已同步为: CONNECTED, connected=True")
                        # 如果检测到已连接，尝试处理队列数据
                        self._process_queued_data()
                    else:
                        self.connection_state = ConnectionState.DISCONNECTED
                        self.logger.info("连接状态已同步为: DISCONNECTED, connected=False")
        except Exception as e:
            self.logger.error(f"同步连接状态时出错: {e}")
    
    def _schedule_reconnect(self):
        """安排重新连接，使用指数退避策略"""
        if not self.running:
            return
            
        # 使用线程锁保护重连调度
        with self._reconnect_lock:
            # 添加重复调度保护，避免同时多个重连任务
            if self._reconnect_scheduled:
                self.logger.debug(f"已经安排了第 {self.reconnect_count} 次重连，跳过此次调度")
                return
                
            self._reconnect_scheduled = True  # 标记已安排重连
            self.reconnect_count += 1
            
            # 如果设置了最大重连次数且已达到，则停止重连
            if self.max_reconnect_attempts > 0 and self.reconnect_count > self.max_reconnect_attempts:
                self.logger.error(f"已达到最大重连次数 {self.max_reconnect_attempts}，停止重连")
                self._reconnect_scheduled = False  # 重置调度标志
                return
                
            # 计算下次重连间隔，使用指数退避策略
            retry_interval = min(
                self.base_retry_interval * (2 ** (self.reconnect_count - 1)),
                self.max_retry_interval
            )
            
            self.logger.info(f"计划在 {retry_interval:.1f} 秒后进行第 {self.reconnect_count} 次重连")
            
            # 取消可能存在的旧定时器
            if self._reconnect_timer:
                try:
                    self._reconnect_timer.cancel()
                except:
                    pass
            
            # 创建定时器线程进行重连
            self._reconnect_timer = threading.Timer(retry_interval, self._reconnect)
            self._reconnect_timer.daemon = True
            self._reconnect_timer.start()
            
    def _schedule_credential_retry(self):
        """安排凭证获取重试，使用指数退避策略"""
        with self._credential_lock:
            if not self.running:
                self.logger.debug("系统已停止，不再安排凭证重试")
                return
            
            # 取消现有的重试计时器
            if self._credential_timer:
                try:
                    self._credential_timer.cancel()
                    self.logger.debug("已取消现有的凭证重试计时器")
                except Exception as e:
                    self.logger.warning(f"取消凭证重试计时器失败: {e}")
                self._credential_timer = None
            
            # 计算重试间隔（指数退避）
            if self.credential_retry_count < self.credential_max_retry_attempts:
                # 增加重试计数
                self.credential_retry_count += 1
                
                # 计算下一次重试间隔（指数退避），但保持较短间隔以便系统可以更快停止
                retry_interval = min(
                    self.credential_retry_interval * (2 ** (self.credential_retry_count - 1)),
                    self.credential_max_retry_interval,
                    30  # 最长不超过30秒，使系统可以更快停止
                )
                
                self.logger.info(f"安排凭证获取重试，{retry_interval:.1f}秒后进行第{self.credential_retry_count}次尝试")
                
                # 创建定时器并保持引用，使其可以被外部取消
                self._credential_timer = threading.Timer(retry_interval, self._retry_credential_request)
                self._credential_timer.daemon = True
                self._credential_timer.name = f"credential_retry_{self.credential_retry_count}"
                self._credential_timer.start()
                self.logger.debug(f"凭证重试定时器已启动: {self._credential_timer.name}")
            else:
                self.logger.warning(f"已达到最大凭证获取重试次数({self.credential_max_retry_attempts})，停止重试")
        
    def _reconnect(self):
        """执行重新连接"""
        try:
            # 首先检查是否需要继续重连
            with self._reconnect_lock:
                if not self.running or self._connecting:
                    self._reconnect_scheduled = False
                    self._reconnect_timer = None
                    return
            
            self.logger.info(f"执行第 {self.reconnect_count} 次重连...")
        
            # 如果客户端存在，先停止循环并断开连接
            if self.client:
                try:
                    self.client.loop_stop()
                    self.client.disconnect()
                except:
                    pass
                
            # 尝试重新连接
            self.connect()
        finally:
            # 无论成功与否，都重置重连调度标志
            # 如果连接失败，connect()方法内会再次调用_schedule_reconnect
            with self._reconnect_lock:
                # 只有在没有新的连接尝试时才清除标志
                if not self._connecting:
                    self._reconnect_scheduled = False
                    self._reconnect_timer = None
                    
    def _retry_credential_request(self):
        """执行凭证获取重试"""
        with self._credential_lock:
            self._credential_timer = None
        
        self.logger.info(f"执行第 {self.credential_retry_count} 次凭证获取尝试...")
        # 重新请求设备绑定
        self._request_device_binding()
        
    def _reset_mqtt_client(self):
        """重置MQTT客户端配置并尝试连接"""
        # 首先检查系统是否正在停止
        if not self.running:
            self.logger.info("系统已停止，取消MQTT客户端重置")
            return
        
        try:
            # 断开现有连接
            if self.client and self.connection_state == ConnectionState.CONNECTED:
                try:
                    # 使用短超时断开
                    self.client.disconnect()
                except:
                    pass
                    
            # 重置连接状态
            self.connection_state = ConnectionState.DISCONNECTED
            self.reconnect_count = 0
            
            # 再次检查运行状态
            if not self.running:
                self.logger.info("系统已停止，取消MQTT客户端重置和连接")
                return
            
            # 重新创建MQTT客户端
            if hasattr(self, 'client'):
                try:
                    self.client.loop_stop()
                except:
                    pass
            
            # 再次检查运行状态        
            if not self.running:
                self.logger.info("系统已停止，取消MQTT连接")
                return
            
            # 添加延迟，让服务器有充足时间处理凭证
            self.logger.info("等待5秒，确保服务器完成凭证处理...")
            time.sleep(5)  # 增加等待时间到10秒
                    
            # 检查运行状态并启动连接线程
            should_connect = False
            with self._reconnect_lock:
                if self.running:
                    should_connect = True

            if should_connect:
                # 在锁外启动连接线程，避免死锁
                try:
                    # 设置一个超时保护，确保连接不会永久阻塞
                    connect_thread = threading.Thread(target=self.connect, daemon=True)
                    connect_thread.start()
                    connect_thread.join(timeout=5)  # 最多等待5秒

                    if connect_thread.is_alive():
                        self.logger.warning("MQTT连接超时，但将在后台继续尝试")

                except Exception as e:
                    self.logger.error(f"启动MQTT连接线程时发生错误: {e}")
            
            # 添加连接状态日志，帮助诊断问题
            self.logger.info(f"MQTT客户端重置完成，当前连接状态: {self.connection_state}, 连接标志: {self.connected}")
            
        except Exception as e:
            self.logger.error(f"重置MQTT客户端失败: {str(e)}")
            # 只有在系统仍在运行时才安排重新获取凭证
            if self.running:
                self._schedule_credential_retry()

    def _get_mac_address(self):
        """获取设备MAC地址，适用于Jetson Orin Nano"""
        try:
            # 方法1：使用ip命令获取所有网络接口信息
            try:
                # 列出所有网络接口
                cmd = "ip -o link show | awk -F': ' '{print $2}'"
                interfaces = subprocess.check_output(cmd, shell=True, universal_newlines=True).strip().split('\n')
                
                # 尝试每个接口
                for ifname in interfaces:
                    # 过滤掉loopback接口和虚拟接口
                    if ifname == 'lo' or 'virtual' in ifname or 'docker' in ifname or 'br-' in ifname:
                        continue
                        
                    # 获取该接口的MAC地址
                    cmd = f"cat /sys/class/net/{ifname}/address"
                    mac_address = subprocess.check_output(cmd, shell=True, universal_newlines=True).strip()
                    
                    # 验证MAC地址格式
                    if mac_address and mac_address != '00:00:00:00:00:00' and ':' in mac_address and len(mac_address.split(':')) == 6:
                        self.logger.info(f"获取到MAC地址: {mac_address} (接口: {ifname})")
                        return mac_address
            except Exception as e:
                self.logger.warning(f"通过ip命令获取MAC地址失败: {e}")
            
            # 方法2：使用socket和fcntl尝试常见的Jetson接口
            interfaces = ['eth0', 'wlan0', 'enp1s0', 'eth1', 'enp2s0']
            for ifname in interfaces:
                try:
                    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                    mac = fcntl.ioctl(
                        s.fileno(),
                        0x8927,  # SIOCGIFHWADDR
                        struct.pack('256s', ifname.encode()[:15])
                    )
                    mac_address = ':'.join(['%02x' % b for b in mac[18:24]])
                    if mac_address != '00:00:00:00:00:00':
                        self.logger.info(f"获取到MAC地址: {mac_address} (接口: {ifname})")
                        return mac_address
                except (IOError, OSError):
                    continue
            
            # 方法3：如果无法从网络接口获取，使用uuid生成一个一致的"MAC地址"
            node_id = uuid.getnode()
            mac_address = ':'.join(('%012x' % node_id)[i:i+2] for i in range(0, 12, 2))
            self.logger.info(f"使用UUID生成的MAC地址: {mac_address}")
            return mac_address
        except Exception as e:
            self.logger.error(f"获取MAC地址失败: {e}")
            # 生成一个固定的备用地址
            return "00:11:22:33:44:55"
            
    def _generate_device_unique_id(self):
        """基于MAC地址生成设备唯一ID"""
        mac_address = self._get_mac_address()
        # 移除冒号并转为小写
        mac_clean = mac_address.replace(':', '').lower()
        # 添加设备类型前缀
        device_id = f"{self.device_type}_{mac_clean}"
        self.logger.info(f"生成设备唯一ID: {device_id}")
        return device_id
        
    def _generate_serial_number(self):
        """生成设备序列号"""
        mac_address = self._get_mac_address()
        # 使用MAC地址的最后6位作为序列号基础
        mac_clean = mac_address.replace(':', '')[-6:]
        # 添加年月日作为前缀
        today = datetime.datetime.now().strftime("%Y%m%d")
        serial = f"{self.device_type}_{today}_{mac_clean}"
        self.logger.info(f"生成设备序列号: {serial}")
        return serial
        
    def _generate_nonce(self, length=16):
        """生成随机nonce字符串"""
        chars = string.ascii_letters + string.digits
        nonce = ''.join(random.choice(chars) for _ in range(length))
        return nonce
        
    def _calculate_signature(self, data):
        """计算HMAC-SHA256签名
        
        Args:
            data: 包含device_unique_id, timestamp, nonce, device_type的字典
            
        Returns:
            str: 签名的十六进制字符串
        """
        # 构建签名字符串
        sign_str = f"{data['device_unique_id']}|{data['timestamp']}|{data['nonce']}|{data['device_type']}"
        self.logger.info(f"签名字符串: {sign_str}")
        
        # 计算HMAC-SHA256签名
        signature = hmac.new(
            self.hmac_key.encode('utf-8'),
            sign_str.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        self.logger.info(f"生成签名: {signature}")
        return signature
        
    def _request_device_binding(self):
        """发送设备绑定请求获取MQTT凭证"""
        with self._credential_lock:
            if not self.running:
                self.logger.debug("系统停止中，取消凭证请求")
                return False
            
            try:
                # 准备请求数据
                device_unique_id = self._generate_device_unique_id()
                nonce = self._generate_nonce()
                timestamp = str(int(time.time()))
                serial_number = self._generate_serial_number()
                mac_address = self._get_mac_address()
                
                # 再次检查运行状态
                if not self.running:
                    self.logger.debug("系统停止中，取消凭证请求")
                    return False
                
                # 构建请求数据
                data = {
                    "device_unique_id": device_unique_id,
                    "device_type": self.device_type,
                    "serial_number": serial_number,
                    "mac_address": mac_address,
                    "timestamp": timestamp,
                    "nonce": nonce
                }
                
                # 计算签名
                signature = self._calculate_signature(data)
                data["signature"] = signature
                
                # 再次检查运行状态
                if not self.running:
                    self.logger.debug("系统停止中，取消凭证请求")
                    return False
                
                # 发送请求，使用更短的超时
                self.logger.info(f"正在请求设备绑定: {device_unique_id}")
                response = requests.post(
                    self.device_bind_url,
                    json=data,
                    headers={"Content-Type": "application/json"},
                    timeout=5  # 减少超时时间，避免长时间阻塞
                )
                
                # 再次检查运行状态
                if not self.running:
                    self.logger.debug("系统停止中，取消处理凭证响应")
                    return False
                
                # 处理响应
                if response.status_code == 200:
                    result = response.json()
                    # 检查业务状态码，可能是0或200都表示成功，这里做更灵活的处理
                    success_codes = [0, 200]
                    if result.get("code") in success_codes:
                        # 成功获取MQTT凭证
                        mqtt_config = result.get("data", {}).get("mqtt_config", {})
                        self.logger.info(f"设备绑定请求成功，业务代码: {result.get('code')}")
                        self.logger.info(f"成功获取MQTT凭证")
                        
                        # 更新MQTT配置
                        self.client_id = mqtt_config.get("client_id")
                        self.username = mqtt_config.get("username")
                        self.password = mqtt_config.get("password")
                        self.broker = mqtt_config.get("broker")
                        self.port = mqtt_config.get("port")
                        
                        # 从client_id中提取设备ID
                        original_device_id = result.get("data", {}).get("device_id")
                        if self.client_id and "@@@" in self.client_id:
                            extracted_device_id = self.client_id.split("@@@")[1]
                            self.device_id = extracted_device_id
                            self.logger.info(f"从client_id提取设备ID: {self.device_id} (原始ID: {original_device_id})")
                        else:
                            # 如果client_id中没有@@@，则使用原始device_id但转为字符串
                            self.device_id = str(original_device_id) if original_device_id is not None else ""
                            self.logger.info(f"使用原始设备ID: {self.device_id}")
                        
                        # 再次检查运行状态
                        if not self.running:
                            self.logger.debug("系统停止中，取消后续连接操作")
                            return False
                        
                        # 保存凭证到本地
                        self._save_credentials()
                        
                        # 重置凭证重试计数
                        self.credential_retry_count = 0
                        
                        # 重置MQTT客户端并尝试连接
                        self._reset_mqtt_client()
                        return True
                    else:
                        error_msg = result.get("message", "未知错误")
                        self.logger.warning(f"设备绑定请求业务处理返回非成功状态: {error_msg}, 业务代码: {result.get('code')}")
                elif response.status_code == 422:
                    # 设备已绑定的情况，尝试提取设备ID
                    self.logger.warning(f"设备已绑定，尝试提取设备ID并直接使用")
                    try:
                        # 尝试从响应中提取设备ID和错误信息
                        result = response.json()
                        device_id = None
                        error_msg = result.get("message", "未知错误")
                        
                        # 尝试多种可能的数据结构提取设备ID
                        if "data" in result and isinstance(result["data"], dict) and "id" in result["data"]:
                            device_id = result["data"]["id"]
                        elif "data" in result and isinstance(result["data"], dict) and "device_id" in result["data"]:
                            device_id = result["data"]["device_id"]
                        elif "device_id" in result:
                            device_id = result["device_id"]
                        
                        # 从错误消息中尝试提取设备ID
                        if not device_id and "设备已绑定，设备ID:" in error_msg:
                            id_str = error_msg.split("设备ID:")[1].strip()
                            if "," in id_str:
                                id_str = id_str.split(",")[0].strip()
                            try:
                                device_id = int(id_str)
                                self.logger.info(f"从错误消息中提取到设备ID: {device_id}")
                            except:
                                self.logger.warning(f"无法从错误消息中解析设备ID: {id_str}")
                        
                        # 再次检查运行状态
                        if not self.running:
                            self.logger.debug("系统停止中，取消设备ID处理")
                            return False
                        
                        if device_id:
                            self.logger.info(f"成功提取设备ID: {device_id}")
                            # 创建格式化的设备ID
                            formatted_device_id = f"SD-X1-{str(device_id).zfill(4)}"
                            self.logger.info(f"格式化设备ID: {device_id} -> {formatted_device_id}")
                            
                            # 保存设备ID
                            self.device_id = formatted_device_id
                            
                            # 使用设备ID和默认凭证直接尝试连接
                            # 这里假设设备已绑定，可以使用默认凭证规则
                            self.client_id = f"GID-SD-X1@@@{formatted_device_id}"
                            self.username = f"device_{device_id}"  
                            self.password = f"device_{device_id}_token"  # 使用一个简单规则生成密码
                            self.broker = self.mqtt_config.get('default_broker', 'mqtt.aixxc.com')
                            self.port = self.mqtt_config.get('default_port', 1883)
                            
                            self.logger.info(f"使用设备ID {formatted_device_id} 设置默认MQTT凭证")
                            
                            # 再次检查运行状态
                            if not self.running:
                                self.logger.debug("系统停止中，取消连接操作")
                                return False
                            
                            # 保存凭证到本地
                            self._save_credentials()
                            
                            # 在停止中不启动新线程，避免停止卡住
                            if self.running:
                                connection_thread = threading.Thread(target=self._reset_mqtt_client, daemon=True)
                                connection_thread.start()
                                return True
                            else:
                                self.logger.info("系统停止中，跳过连接初始化")
                                return False
                        else:
                            self.logger.warning(f"无法从响应中提取设备ID: {error_msg}")
                    except Exception as e:
                        self.logger.error(f"处理422错误响应时发生异常: {e}", exc_info=True)
                    
                    # 如果无法提取设备ID，则安排普通重试
                    if self.running:
                        self._schedule_credential_retry()
                    return False
                else:
                    self.logger.error(f"设备绑定请求HTTP错误: {response.status_code}, {response.text}")
                
                # 请求失败，安排重试（仅在系统仍在运行时）
                if self.running:
                    self._schedule_credential_retry()
                return False
                
            except requests.Timeout:
                # 请求超时，这是特殊处理以确保快速响应
                if self.running:
                    self._schedule_credential_retry()
                return False
            except Exception as e:
                self.logger.error(f"设备绑定请求异常: {str(e)}")
                # 请求异常，安排重试（仅在系统仍在运行时）
                if self.running:
                    self._schedule_credential_retry()
                return False
    
    def on_disconnect(self, client, userdata, rc, properties=None):
        """断开连接回调函数"""
        # 记录断开连接原因
        reason = f"rc={rc}"
        
        # 更新连接状态
        self.connected = False
        self.connection_state = ConnectionState.DISCONNECTED
            
        if rc != 0:
            self.logger.error(f"MQTT连接意外断开({reason})，将自动重连")
            
            # 触发自动重连（仅在系统运行中且未调度重连时）
            if self.running and not self._reconnect_scheduled:
                self._schedule_reconnect()
        else:
            # 正常断开
            self.logger.debug(f"MQTT断开连接({reason})，不再触发重连")

    def on_connect(self, client, userdata, flags, rc, properties=None):
        """连接回调函数"""
        if self.client is None:
            self.logger.error("MQTT client对象未初始化，无法订阅主题")
            return

        # 记录不同返回码的含义
        rc_meanings = {
            0: "连接成功",
            1: "连接被拒绝 - 协议版本不正确",
            2: "连接被拒绝 - 无效的客户端标识符",
            3: "连接被拒绝 - 服务器不可用",
            4: "连接被拒绝 - 用户名或密码错误",
            5: "连接被拒绝 - 未授权"
        }
        
        if rc in rc_meanings:
            self.logger.info(f"MQTT连接结果: {rc_meanings[rc]}")
        else:
            self.logger.info(f"MQTT连接结果: 未知返回码 {rc}")
            
        if rc == 0:
            # 显式检查是否真正连接成功
            actual_connected = True
            if hasattr(self.client, 'is_connected'):
                actual_connected = self.client.is_connected()
                if actual_connected != True:
                    self.logger.warning(f"MQTT连接状态不一致: 回调成功但实际未连接")
                    
            if actual_connected:
                self.logger.info(f"MQTT连接已确认成功: {self.broker}:{self.port}")
                self.connected = True
                self.connection_state = ConnectionState.CONNECTED
            else:
                self.logger.warning(f"MQTT连接状态异常: 回调成功但实际连接检查失败")
                self.connected = False
                self.connection_state = ConnectionState.DISCONNECTED
                
            self.reconnect_count = 0  # 重置重连计数
            
            # 获取设备ID（确保已经从服务端获取）
            if not self.device_id:
                self.logger.warning("已连接MQTT但设备ID未设置，无法订阅主题")
                return
                
            device_id = self.device_id
            
            # 收集所有要订阅的主题
            topics = [
                (self.mqtt_config['subscribe_topic'].format(device_id=device_id), 1),
                # 错误回复主题已不再使用，错误通过自检主题上报
                # (self.mqtt_config['error_reply_topic'].format(device_id=device_id), 1),
                (self.mqtt_config['co2_reply_topic'].format(device_id=device_id), 1),
                (self.mqtt_config['check_reply_topic'].format(device_id=device_id), 1),
                (self.mqtt_config['devopen_topic'].format(device_id=device_id), 1),
                (self.mqtt_config['devopen_reply_topic'].format(device_id=device_id), 1),
                (self.mqtt_config['devclose_topic'].format(device_id=device_id), 1),
                (self.mqtt_config['devclose_reply_topic'].format(device_id=device_id), 1),
                (self.mqtt_config['devrestart_topic'].format(device_id=device_id), 1),
                (self.mqtt_config['devrestart_reply_topic'].format(device_id=device_id), 1),
                (self.mqtt_config['swsensor_topic'].format(device_id=device_id), 1)
            ]
            
            # 添加蚊子检测回复主题（如果配置中存在）
            if 'mosquito_reply_topic' in self.mqtt_config:
                mosquito_reply_topic = self.mqtt_config['mosquito_reply_topic'].format(device_id=device_id)
                topics.append((mosquito_reply_topic, 1))
            
            # 一次性订阅所有主题
            self.client.subscribe(topics)
            self.logger.info(f"已订阅{len(topics)}个主题")
            
            # 在成功连接并订阅主题后，首先上报设备开机状态
            # 只有当设备处于开机状态且不是从关机恢复时才发送
            if hasattr(self, 'device_status') and self.device_status.get('power_status', 1) == 1:
                try:
                    # 上报开机状态（仅在连接建立时上报一次）
                    success = self.report_power_on_status()
                    if success:
                        self.logger.info("设备开机状态上报成功")
                except Exception as e:
                    self.logger.error(f"上报设备开机状态时发生异常: {e}", exc_info=True)
            
            # 不在这里处理队列数据，统一由_check_connection_status方法处理
            # 这样可以避免重复处理队列数据
            self.logger.info("连接已建立，状态变化将由_check_connection_status方法处理")
        else:
            self.connected = False
            self.connection_state = ConnectionState.DISCONNECTED
            self.logger.error(f"连接MQTT服务器失败，返回码: {rc}")
            # 触发重连（加保护）
            if not self._reconnect_scheduled and self.running:
                self._schedule_reconnect()
                
    def on_message(self, client, userdata, msg):
        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')

            # 添加详细日志记录所有接收到的消息
            self.logger.info(f"收到MQTT消息: topic={topic}, payload={payload}")

            # 尝试解析JSON数据
            try:
                data = json.loads(payload)
            except json.JSONDecodeError:
                self.logger.warning(f"收到非JSON格式消息: topic={topic}, payload={payload[:50]}...")
                return
            
            # 确保设备ID已设置
            if not self.device_id:
                self.logger.warning("设备ID未设置，无法处理消息")
                return
                
            device_id = self.device_id
            
            # 处理传感器数据回复
            if topic == self.mqtt_config['subscribe_topic'].format(device_id=device_id):
                self.handle_sensor_data_reply(data, device_id)
                
            # 错误回复主题已不再使用，错误通过自检主题上报
            # elif topic == self.mqtt_config['error_reply_topic'].format(device_id=device_id):
            #     self.handle_error_reply(data, device_id)
                
            # 处理CO2控制器状态回复
            elif topic == self.mqtt_config['co2_reply_topic'].format(device_id=device_id):
                self.handle_co2_reply(data, device_id)
            
            # 处理设备自检回复
            elif topic == self.mqtt_config['check_reply_topic'].format(device_id=device_id):
                self.handle_device_check_reply(data, device_id)
                
            # 处理设备开机指令
            elif topic == self.mqtt_config['devopen_topic'].format(device_id=device_id):
                self.handle_power_on_command(data, device_id)
                
            # 处理设备关机指令
            elif topic == self.mqtt_config['devclose_topic'].format(device_id=device_id):
                self.handle_power_off_command(data, device_id)
                
            # 处理设备重启指令
            elif topic == self.mqtt_config['devrestart_topic'].format(device_id=device_id):
                self.handle_restart_command(data, device_id)
                
            # 处理组件动态开关指令
            elif topic == self.mqtt_config['swsensor_topic'].format(device_id=device_id):
                self.handle_swsensor_command(data, device_id)
                
            # 处理设备开机回复
            elif topic == self.mqtt_config['devopen_reply_topic'].format(device_id=device_id):
                self.handle_power_on_reply(data, device_id)
                
            # 处理设备关机回复
            elif topic == self.mqtt_config['devclose_reply_topic'].format(device_id=device_id):
                self.handle_power_off_reply(data, device_id)
                
            # 处理设备重启回复
            elif topic == self.mqtt_config['devrestart_reply_topic'].format(device_id=device_id):
                self.handle_restart_reply(data, device_id)
            
            # 处理蚊子检测回复
            elif 'mosquito_reply_topic' in self.mqtt_config and topic == self.mqtt_config['mosquito_reply_topic'].format(device_id=device_id):
                self.handle_mosquito_reply(data, device_id)
                
            else:
                self.logger.info(f"收到未处理的消息: topic={topic}")
                
        except Exception as e:
            self.logger.error(f"处理MQTT消息时发生错误: {e}", exc_info=True)
            
    def disconnect(self):
        """断开与MQTT服务器的连接，使用非阻塞方式"""
        # 检查客户端对象是否存在且是否已连接
        if not self.client:
            self.logger.debug("MQTT客户端不存在，无需断开连接")
            return
        
        if not self.connected:
            self.logger.debug("MQTT未连接，无需断开连接")
            self.connection_state = ConnectionState.DISCONNECTED
            return
            
        # 立即标记为已断开，确保其他代码不会再尝试使用此连接
        self.connected = False
        self.connection_state = ConnectionState.DISCONNECTED
        
        # 直接在当前线程中快速执行断开操作，设置极短的超时
        try:
            # 停止循环先，兼容不同版本Paho-MQTT
            try:
                try:
                    # 先尝试新版本API
                    self.client.loop_stop(force=True)
                    self.logger.info("已停止MQTT客户端循环(force=True)")
                except (TypeError, AttributeError) as e:
                    self.logger.info(f"使用兼容模式停止MQTT客户端循环: {str(e)}")
                    self.client.loop_stop()
                    self.logger.info("已停止MQTT客户端循环(兼容模式)")
            except Exception as e:
                self.logger.warning(f"停止MQTT客户端循环时出错: {str(e)}")
            
            # 使用超短超时进行断开连接
            disconnect_future = None
            try:
                disconnect_future = self.client.disconnect()
            except Exception as e:
                self.logger.warning(f"断开MQTT连接时发生异常: {e}")
            
            # 如果有返回future且支持wait_for_completion（某些MQTT客户端的实现），等待极短时间
            if disconnect_future and hasattr(disconnect_future, 'wait_for_completion'):
                try:
                    # 只等待0.5秒
                    disconnect_future.wait_for_completion(timeout=0.5)
                    self.logger.info("MQTT连接已断开")
                except Exception as e:
                    self.logger.warning(f"等待MQTT断开连接完成时超时: {e}")
            else:
                # 如果不支持异步断开，直接标记为已完成
                self.logger.info("MQTT连接断开请求已发送")
                
        except Exception as e:
            self.logger.error(f"断开MQTT连接时发生错误: {e}")
            # 即使发生错误，也确保我们认为连接已断开
            self.connection_state = ConnectionState.DISCONNECTED
    
    def _process_queued_data(self):
        """处理队列中缓存的数据"""
        # 强制检查连接状态，确保状态一致
        self._check_connection_status()
        
        if not self.connected:
            self.logger.info("MQTT未连接，暂不处理队列数据")
            return
            
        self.logger.info("开始处理队列中缓存的数据...")
        # 添加队列状态日志
        self.logger.info(f"队列状态：sensor={self.sensor_data_queue.qsize()}, check={self.check_data_queue.qsize()}, co2={self.co2_status_queue.qsize()}")
        
        # 获取批处理大小
        batch_size = self.mqtt_config.get('batch_size', 5)
        
        # 处理传感器数据队列
        sensor_data_count = 0

        while not self.sensor_data_queue.empty() and sensor_data_count < batch_size * 2:  # 每次最多处理批处理大小的2倍
            try:
                data_item = self.sensor_data_queue.get_nowait()
                success = self.publish_sensor_data(data_item)
                if success:
                    sensor_data_count += 1
                self.sensor_data_queue.task_done()
            except queue.Empty:
                break
            except Exception as e:
                self.logger.error(f"处理缓存的传感器数据时发生错误: {e}", exc_info=True)
                break
        
        if sensor_data_count > 0:
            self.logger.info(f"已处理 {sensor_data_count} 条缓存的传感器数据")
        
        # 处理错误数据队列
        error_data_count = 0
        while not self.error_data_queue.empty() and error_data_count < batch_size:  # 每次最多处理批处理大小的条数
            try:
                error_item = self.error_data_queue.get_nowait()
                success = self.publish_error_data(error_item)
                if success:
                    error_data_count += 1
                    # 增加错误数据处理计数
                    self.error_data_processed_count += 1
                self.error_data_queue.task_done()
            except queue.Empty:
                break
            except Exception as e:
                self.logger.error(f"处理缓存的错误数据时发生错误: {e}", exc_info=True)
                break
        
        if error_data_count > 0:
            self.logger.info(f"已处理 {error_data_count} 条缓存的错误数据")
        
        # 处理CO2状态队列
        co2_status_count = 0
        processed_co2_timestamps = set()  # 用于记录已处理过的时间戳
        
        while not self.co2_status_queue.empty() and co2_status_count < batch_size:  # 每次最多处理批处理大小的条数
            try:
                status_item = self.co2_status_queue.get_nowait()
                
                # 获取时间戳，统一使用time字段
                timestamp = status_item.get("time", status_item.get("timestamp"))
                
                # 检查是否已处理过该时间戳
                if timestamp in processed_co2_timestamps:
                    self.logger.debug(f"跳过重复的队列CO2状态数据，时间戳: {timestamp}")
                    self.co2_status_queue.task_done()
                    continue
                
                processed_co2_timestamps.add(timestamp)
                
                success = self.publish_co2_status(status_item)
                if success:
                    co2_status_count += 1
                self.co2_status_queue.task_done()
            except queue.Empty:
                break
            except Exception as e:
                self.logger.error(f"处理缓存的CO2状态数据时发生错误: {e}", exc_info=True)
                break
        
        if co2_status_count > 0:
            self.logger.info(f"已处理 {co2_status_count} 条缓存的CO2状态数据")
        
        # 处理设备自检队列
        check_data_count = 0
        while not self.check_data_queue.empty() and check_data_count < batch_size:  # 每次最多处理批处理大小的条数
            try:
                check_item = self.check_data_queue.get_nowait()
                success = self.publish_device_check(check_item)
                if success:
                    check_data_count += 1
                self.check_data_queue.task_done()
            except queue.Empty:
                break
            except Exception as e:
                self.logger.error(f"处理缓存的设备自检数据时发生错误: {e}", exc_info=True)
                break
        
        if check_data_count > 0:
            self.logger.info(f"已处理 {check_data_count} 条缓存的设备自检数据")
            
        # 蚊虫检测数据不再使用队列处理，直接通过文件监控器处理
    
    def upload_loop(self):
        """数据上传循环，定期检查并上传数据"""
        last_data_upload_time = 0
        last_error_check_time = 0
        last_device_check_time = 0
        last_queue_check_time = 0
        last_archive_time = 0  # 添加归档时间记录
        last_cleanup_time = 0  # 添加清理时间记录
        first_run = True
        
        # 从配置中读取队列状态记录间隔
        queue_check_interval = self.mqtt_config.get('queue_check_interval', 300)  # 默认5分钟记录一次
        log_queue_status = self.mqtt_config.get('log_queue_status', True)  # 是否记录队列状态
        archive_interval = self.mqtt_config.get('archive_interval', 600)  # 默认10分钟归档一次
        
        while self.running:
            try:
                # 同步连接状态，确保状态一致性
                self.sync_connection_state()
                
                # 检查连接状态
                self._check_connection_status()
                
                # 定期记录队列状态（无论是否连接）
                current_time = time.time()
                if log_queue_status and (current_time - last_queue_check_time >= queue_check_interval):
                    self.logger.info(f"队列状态：sensor={self.sensor_data_queue.qsize()}, "
                                    f"co2={self.co2_status_queue.qsize()}, "
                                    f"check={self.check_data_queue.qsize()}")
                    last_queue_check_time = current_time
                
                # 如果未连接，等待一段时间后再检查
                if not self.connected:
                    self.logger.debug("MQTT未连接，等待连接恢复后再上传数据")
                    # 短暂等待后继续循环
                    for i in range(5):
                        if not self.running:
                            return
                        time.sleep(1)
                    continue
                
                current_time = time.time()
                data_interval = self.mqtt_config['publish_interval']
                error_check_interval = self.mqtt_config.get('error_check_interval', 60)  # 从配置文件读取错误检查间隔
                device_check_interval = self.config.get('device_health', {}).get('check_interval', 1800)  # 从配置读取设备自检间隔

                # 首次运行时记录配置信息
                if first_run:
                    self.logger.info(f"上传循环配置: 传感器数据间隔={data_interval}秒, 设备自检间隔={device_check_interval}秒, 错误检查间隔={error_check_interval}秒")
                
                # 首次运行时，等待所有组件初始化完成后上传初始数据
                if first_run:
                    # 等待最多30秒，让其他组件有时间初始化
                    wait_start = time.time()
                    while time.time() - wait_start < 30 and not all(self.components_ready.values()):
                        # 每秒检查一次组件状态和停止信号
                        if not self.running:
                            self.logger.info("检测到停止信号，退出数据上传初始化等待")
                            return
                        time.sleep(1)
                        self.logger.debug(f"等待组件初始化: {self.components_ready}")
                    
                    if not all(self.components_ready.values()):
                        self.logger.warning(f"部分组件未就绪，但将继续上传数据: {self.components_ready}")
                    else:
                        self.logger.info("所有组件已就绪，开始数据上传")
                    
                    # 在首次运行时统一上报初始数据
                    self.logger.info("开始上报初始数据")
                    
                    # 清除所有immediate上传标记，避免后续重复上传
                    with self.immediate_upload_lock:
                        # 上传传感器数据并清除标记
                        if self.components_ready["sensor_collector"]:
                            success = self.upload_sensor_data(is_scheduled=True)
                            self.immediate_upload_flags['sensor_data'] = False
                            # 更新最后上传时间，避免同一循环中重复上传
                            if success:
                                last_data_upload_time = current_time
                                self.logger.info("初始传感器数据上传成功")
                            else:
                                self.logger.warning("初始传感器数据上传失败，将在下次检查时重试")
                        
                        # 上传设备自检数据并清除标记
                        if self.components_ready["device_health"]:
                            success = self.upload_device_check(is_scheduled=True)
                            self.immediate_upload_flags['device_check'] = False
                            # 更新最后设备自检时间，避免同一循环中重复上传
                            if success:
                                last_device_check_time = current_time
                                self.logger.info("初始设备自检数据上传成功")
                            else:
                                self.logger.warning("初始设备自检数据上传失败，将在下次检查时重试")
                        
                        # 上传CO2状态数据并清除标记
                        if self.components_ready["co2_controller"]:
                            # 重置上传成功标记
                            self.co2_status_upload_success = False
                            
                            success = self.upload_co2_status(is_scheduled=True)
                            self.immediate_upload_flags['co2_status'] = False
                            
                            # 等待响应的时间，统一为5秒
                            if success:
                                confirmation_timeout = 5.0
                                wait_start = time.time()
                                self.logger.debug(f"等待CO2状态上传确认，超时时间: {confirmation_timeout}秒")
                                while time.time() - wait_start < confirmation_timeout and not self.co2_status_upload_success:
                                    time.sleep(0.1)

                                if self.co2_status_upload_success:
                                    self.logger.info("初始CO2状态数据上传成功")
                                else:
                                    self.logger.warning("初始CO2状态数据上传失败，5秒内未收到确认")
                            else:
                                self.logger.warning("初始CO2状态数据上传失败，发送失败")
                    
                    first_run = False
                
                # 检查是否有错误需要上报
                if current_time - last_error_check_time >= error_check_interval:
                    self.upload_errors()
                    # 上传CO2控制器错误
                    if self.components_ready["co2_controller"]:
                        self.upload_co2_errors()
                    else:
                        self.logger.debug("CO2控制器未就绪，跳过错误上传")
                    last_error_check_time = current_time
                
                # CO2状态上传已改为状态变化时立即上报，移除定时检查
                
                # 检查是否有设备自检数据需要上传
                if current_time - last_device_check_time >= device_check_interval:
                    self.logger.debug(f"触发设备自检定时上传检查: 距离上次上传{current_time - last_device_check_time:.1f}秒，间隔要求{device_check_interval}秒")
                    if self.components_ready["device_health"]:
                        # 重置上传成功标志
                        self.device_check_upload_success = False

                        # 调用上传方法
                        success = self.upload_device_check(is_scheduled=True)  # 传入参数表示这是定时上传
                        
                        # 清除immediate上传标记，避免重复上传
                        with self.immediate_upload_lock:
                            self.immediate_upload_flags['device_check'] = False
                        
                        # 检查上传是否成功
                        # 注意：这里的success只表示消息发送成功，真正的上传成功由handle_device_check_reply设置
                        if success:
                            # 检查是否实际发送了数据
                            had_data = getattr(self, '_last_check_upload_had_data', True)

                            if had_data:
                                # 如果发送了数据，等待确认
                                confirmation_timeout = 5.0
                                wait_start = time.time()
                                self.logger.debug(f"等待设备自检数据上传确认，超时时间: {confirmation_timeout}秒")
                                while time.time() - wait_start < confirmation_timeout and not self.device_check_upload_success:
                                    time.sleep(0.1)

                                if self.device_check_upload_success:
                                    last_device_check_time = current_time
                                    self.logger.info("设备自检数据定时上传成功，更新上传时间")
                                else:
                                    self.logger.warning("设备自检数据定时上传失败，5秒内未收到确认")
                            else:
                                # 如果没有数据，直接更新时间
                                last_device_check_time = current_time
                                self.logger.debug("设备自检数据定时检查完成（无数据），更新上传时间")
                        else:
                            self.logger.warning("设备自检数据定时上传失败，将在下次检查时重试")
                    else:
                        self.logger.debug("设备自检系统未就绪，跳过自检数据上传")
                        # 不更新last_device_check_time，以便在组件就绪后立即尝试上传

                # CO2状态数据仅在状态变化时上传，不需要定时检查

                # 检查是否需要上传传感器数据（定时上传）
                time_since_last_upload = current_time - last_data_upload_time
                if time_since_last_upload >= data_interval:
                    self.logger.info(f"触发传感器数据定时上传: 距离上次上传{time_since_last_upload:.1f}秒，间隔要求{data_interval}秒")
                    if self.components_ready["sensor_collector"]:
                        # 重置上传成功标志
                        self.sensor_data_upload_success = False
                        
                        # 调用上传方法
                        success = self.upload_sensor_data(is_scheduled=True)  # 传入参数表示这是定时上传
                        
                        # 清除immediate上传标记，避免重复上传
                        with self.immediate_upload_lock:
                            self.immediate_upload_flags['sensor_data'] = False
                        
                        # 检查上传是否成功
                        # 注意：这里的success只表示消息发送成功，真正的上传成功由handle_sensor_data_reply设置
                        if success:
                            # 等待响应的时间，从配置文件读取超时时间
                            confirmation_timeout = 5.0
                            wait_start = time.time()
                            self.logger.debug(f"等待传感器数据上传确认，超时时间: {confirmation_timeout}秒")
                            while time.time() - wait_start < confirmation_timeout and not self.sensor_data_upload_success:
                                time.sleep(0.1)
                            
                            if self.sensor_data_upload_success:
                                last_data_upload_time = current_time  # 只有成功才更新时间
                                self.logger.info("传感器数据定时上传成功，更新上传时间")
                            else:
                                # 修改：即使未收到确认，也更新时间，避免频繁重试
                                last_data_upload_time = current_time
                                self.logger.warning("传感器数据定时上传失败，5秒内未收到确认，但已更新上传时间")
                        else:
                            self.logger.warning("传感器数据定时上传失败，将在下次检查时重试")
                    else:
                        self.logger.info("传感器收集器未就绪，跳过数据上传")
                        # 不更新last_data_upload_time，以便在组件就绪后立即尝试上传
                
                # 定期清理过期的上传记录
                if current_time - last_cleanup_time >= 3600:  # 每小时清理一次
                    self._cleanup_old_upload_records()
                    last_cleanup_time = current_time

                # 检查是否有待归档的传感器数据
                if getattr(self, 'sensor_archive_pending', False):
                    # 添加最小间隔，避免频繁执行
                    if not hasattr(self, 'last_archive_attempt'):
                        self.last_archive_attempt = 0

                    if current_time - self.last_archive_attempt >= 5:  # 最少5秒间隔
                        try:
                            # 先强制刷新缓存，确保数据在文件中
                            self._force_flush_sensor_cache()
                            time.sleep(0.1)  # 等待写入完成

                            # 执行归档
                            self._archive_uploaded_sensor_data()
                            self.sensor_archive_pending = False
                            self.last_archive_attempt = current_time
                            self.logger.info("已完成延迟归档传感器数据")
                        except Exception as e:
                            self.logger.error(f"延迟归档传感器数据失败: {e}", exc_info=True)
                            self.sensor_archive_pending = False  # 清除标记，避免无限重试
                            self.last_archive_attempt = current_time

                # 定期检查是否有未归档的数据
                if self.connected and current_time - last_archive_time >= 3600:  # 每小时检查一次未归档数据
                    try:
                        # 检查是否有未归档的数据
                        self.logger.info("定期检查未归档数据")
                        self._archive_uploaded_errors()
                        self._archive_uploaded_co2_errors()
                    except Exception as e:
                        self.logger.error(f"定期检查未归档数据时发生错误: {e}", exc_info=True)
                    last_archive_time = current_time
                    
                # 处理立即上传标记（增加日志，帮助调试重复上传问题）
                with self.immediate_upload_lock:
                    if self.immediate_upload_flags['co2_status']:
                        self.logger.info("检测到CO2状态立即上传标记")  # 改为INFO级别
                        if self.components_ready["co2_controller"]:
                            success = self.upload_co2_status()  # 这里不传is_scheduled参数，因为是立即上传
                            if success:
                                self.logger.info("已通过立即上传标记上传CO2状态数据")
                            else:
                                self.logger.warning("通过立即上传标记上传CO2状态数据失败")
                        self.immediate_upload_flags['co2_status'] = False

                    if self.immediate_upload_flags['device_check']:
                        self.logger.info("检测到设备自检立即上传标记")  # 改为INFO级别
                        if self.components_ready["device_health"]:
                            success = self.upload_device_check()  # 这里不传is_scheduled参数，因为是立即上传
                            if success:
                                self.logger.info("已通过立即上传标记上传设备自检数据")
                            else:
                                self.logger.warning("通过立即上传标记上传设备自检数据失败（可能没有数据）")
                        else:
                            self.logger.warning("设备自检系统未就绪，跳过立即上传")
                        self.immediate_upload_flags['device_check'] = False

                    if self.immediate_upload_flags['sensor_data']:
                        self.logger.info("检测到传感器数据立即上传标记")  # 改为INFO级别
                        if self.components_ready["sensor_collector"]:
                            success = self.upload_sensor_data()  # 这里不传is_scheduled参数，因为是立即上传
                            if success:
                                self.logger.info("已通过立即上传标记上传传感器数据")
                            else:
                                self.logger.warning("通过立即上传标记上传传感器数据失败")
                        self.immediate_upload_flags['sensor_data'] = False
                
                # 缩短检查间隔，提高响应停止信号的能力，同时更频繁检查连接状态
                for i in range(5):  # 每秒检查一次是否需要停止
                    if not self.running:
                        return
                    
                    # 每秒检查一次连接状态
                    if i % 2 == 0:  # 每2秒检查一次连接状态
                        self._check_connection_status()
                        
                    time.sleep(1)
                
                # 优先上传队列数据
                while not self.sensor_data_queue.empty():
                    try:
                        data = self.sensor_data_queue.get_nowait()
                    except Exception:
                        break
                    success = self.publish_sensor_data(data)
                    if success:
                        self._archive_uploaded_sensor_data_by_timestamp(data.get('time') or data.get('timestamp'))
                    else:
                        # 上传失败，重新入队
                        try:
                            self.sensor_data_queue.put_nowait(data)
                        except Exception:
                            self.logger.error("传感器数据队列已满，数据丢失")
                        break
                
            except Exception as e:
                self.logger.error(f"上传循环异常: {e}")
                time.sleep(5)
    
    # 回调处理方法 - 这些方法将从原代码复制过来
    def handle_sensor_data_reply(self, data, device_id):
        """处理传感器数据回复

        Args:
            data: 回复数据，格式为 {"devid":"XC-MQ-AI0021","code":200,"msg":"success","data":{},"dir":"down"}
            device_id: 设备ID
        """
        try:
            # 添加调用计数来追踪重复回复
            if not hasattr(self, '_sensor_reply_count'):
                self._sensor_reply_count = 0
            self._sensor_reply_count += 1

            self.logger.info(f"收到传感器数据回复: {data}")

            # 验证设备ID - 如果ID匹配，则认为是有效回复
            if "devid" in data:
                received_devid = str(data["devid"])
                expected_devid = str(self.device_id)

                if received_devid != expected_devid:
                    self.logger.warning(f"传感器数据回复设备ID不匹配: 期望={expected_devid}, 实际={received_devid}")
                    return False

                # 设备ID匹配，认为上传成功（不检查code字段）
                self.logger.info(f"传感器数据上传成功确认（设备ID匹配）")

                # 记录平台响应信息（仅用于调试，不影响成功判断）
                if "code" in data:
                    code = data.get("code", "未知")
                    msg = data.get("msg", "无消息")
                    self.logger.debug(f"平台响应详情: 代码={code}, 消息={msg}")

                # 标记传感器数据上传成功，用于更新定时上传时间
                self.sensor_data_upload_success = True
                self.logger.debug(f"传感器数据上传已确认: 设备ID={received_devid}")

                # 防重复处理：只处理第一次回复
                if self._sensor_reply_count == 1:
                    # 收到回复后立即归档，不再使用延迟归档
                    try:
                        # 先强制刷新缓存，确保数据在文件中
                        self._force_flush_sensor_cache()
                        time.sleep(0.1)  # 等待写入完成

                        # 执行归档
                        self._archive_uploaded_sensor_data()
                        self.logger.info("传感器数据回复确认，已立即归档")
                    except Exception as e:
                        self.logger.error(f"立即归档传感器数据失败: {e}", exc_info=True)
                        # 如果立即归档失败，回退到延迟归档机制
                        self.sensor_archive_pending = True
                        self.logger.info("回退到延迟归档机制")
                else:
                    self.logger.debug(f"重复回复，忽略处理")

                return True
            else:
                self.logger.warning(f"传感器数据回复缺少设备ID")
                return False
                
        except Exception as e:
            self.logger.error(f"处理传感器数据回复时发生错误: {e}", exc_info=True)
            return False
        
    def handle_error_reply(self, data, device_id):
        """处理错误数据回复（已弃用）
        
        Args:
            data: 回复数据
            device_id: 设备ID
            
        Note:
            错误上报已重定向到设备自检上报，此方法仅作兼容性保留
        """
        self.logger.info("收到错误数据回复，但错误上报已重定向至设备自检上报")
        # 不再处理错误回复，所有错误均通过设备自检上报处理
        
    def handle_co2_reply(self, data, device_id):
        """处理CO2控制器状态回复"""
        try:
            self.logger.info(f"收到CO2状态回复: {data}")

            # 验证设备ID
            if "devid" not in data or str(data["devid"]) != str(self.device_id):
                self.logger.warning(f"CO2状态回复设备ID不匹配，期望: {self.device_id}, 收到: {data.get('devid')}")
                return False

            # 设备ID匹配，认为上传成功（不检查code字段）
            self.logger.info(f"CO2状态数据上传成功确认（设备ID匹配）")

            # 记录平台响应信息（仅用于调试，不影响成功判断）
            if "code" in data:
                code = data.get("code", "未知")
                msg = data.get("msg", "无消息")
                self.logger.debug(f"平台响应详情: 代码={code}, 消息={msg}")

            # 设备ID匹配，处理队列最前面的一条数据（先入先出）
            with self.co2_status_queue_lock:
                if not self.co2_status_queue.empty():
                    # 获取但不移除队列前面的数据，用于查找对应文件
                    item = self.co2_status_queue.queue[0]
                    timestamp = item.get("time", item.get("timestamp"))

                    # 从队列中移除这条数据
                    self.co2_status_queue.get_nowait()
                    self.logger.info(f"已从队列中移除一条CO2状态数据，队列剩余: {self.co2_status_queue.qsize()}")

                    # 归档对应的文件（设备ID匹配即认为成功）
                    if timestamp:
                        self._archive_co2_file_by_timestamp(timestamp)
                        self.logger.info(f"CO2状态数据文件已归档，时间戳: {timestamp}")

                    # 标记CO2状态数据上传成功，用于更新定时上传时间
                    self.co2_status_upload_success = True
                else:
                    self.logger.debug("收到CO2状态回复，但队列为空")

            # 标记CO2状态上传成功，确保下次定时检查正常进行
            self.co2_status_upload_success = True

            return True
                    
        except Exception as e:
            self.logger.error(f"处理CO2控制器状态回复时出错: {str(e)}", exc_info=True)
            return False
            
    def _archive_co2_file_by_timestamp(self, timestamp):
        """根据时间戳归档CO2状态文件
        
        Args:
            timestamp: 文件时间戳
        """
        try:
            # 确保目录存在
            devices_dir = os.path.join(self.data_dir, self.storage_config.get('devices_dir', 'devices'))
            uploaded_dir = os.path.join(devices_dir, 'uploaded')
            history_dir = os.path.join(devices_dir, 'history')
            
            for directory in [uploaded_dir, history_dir]:
                if not os.path.exists(directory):
                    os.makedirs(directory)
                    
            # 查找匹配的文件
            timestamp_str = str(timestamp)
            target_file = None
            
            for filename in os.listdir(uploaded_dir):
                if filename.startswith("co2_") and filename.endswith(".json"):
                    # 从文件名解析时间戳
                    try:
                        file_ts = int(filename[4:-5])  # 提取co2_{timestamp}.json中的timestamp部分
                        if file_ts == timestamp:
                            target_file = filename
                            break
                    except ValueError:
                        continue
            
            # 如果找到匹配的文件，移动到history目录
            if target_file:
                src_path = os.path.join(uploaded_dir, target_file)
                dst_path = os.path.join(history_dir, target_file)
                shutil.move(src_path, dst_path)
                self.logger.info(f"已归档CO2状态文件: {target_file}")
            else:
                self.logger.warning(f"未找到匹配的CO2状态文件，时间戳: {timestamp}")
                
        except Exception as e:
            self.logger.error(f"归档CO2状态文件时出错: {e}", exc_info=True)
        
    def handle_device_check_reply(self, data, device_id):
        """处理设备自检数据回复"""
        try:
            self.logger.info(f"收到设备自检回复: {data}")

            # 验证设备ID
            if "devid" not in data or str(data["devid"]) != str(self.device_id):
                self.logger.warning(f"设备自检回复设备ID不匹配，期望: {self.device_id}, 收到: {data.get('devid')}")
                return False

            # 设备ID匹配，认为上传成功（不检查code字段）
            self.logger.info(f"设备自检数据上传成功确认（设备ID匹配）")

            # 记录平台响应信息（仅用于调试，不影响成功判断）
            if "code" in data:
                code = data.get("code", "未知")
                msg = data.get("msg", "无消息")
                self.logger.debug(f"平台响应详情: 代码={code}, 消息={msg}")

            # 设备ID匹配，处理队列中最前面的一条数据（先入先出）
            with self.check_data_queue.mutex:
                if not self.check_data_queue.empty():
                    # 获取队列前面的数据，用于查找对应文件
                    item = self.check_data_queue.queue[0]
                    timestamp = item.get("time", item.get("timestamp"))

                    # 从队列中移除这条数据
                    self.check_data_queue.get_nowait()
                    self.logger.info(f"已从队列中移除一条设备自检数据，队列剩余: {self.check_data_queue.qsize()}")

                    # 通知设备自检系统归档文件（设备ID匹配即认为成功）
                    if timestamp:
                        # 获取设备自检实例
                        import builtins
                        if hasattr(builtins, 'device_health_instance'):
                            device_health = getattr(builtins, 'device_health_instance')
                            if hasattr(device_health, 'archive_uploaded_checks'):
                                device_health.archive_uploaded_checks([timestamp])
                                self.logger.info(f"已归档设备自检数据，时间戳: {timestamp}")
                                
                                # 标记设备自检数据上传成功，用于更新定时上传时间
                                self.device_check_upload_success = True
                            else:
                                self.logger.warning("设备自检实例缺少archive_uploaded_checks方法")
                        else:
                            self.logger.warning("设备自检实例不可用")
                else:
                    self.logger.debug("收到设备自检回复，但队列为空")

            # 标记设备自检上传成功，确保下次定时检查正常进行
            self.device_check_upload_success = True

            return True
                
        except Exception as e:
            self.logger.error(f"处理设备自检数据回复时出错: {str(e)}", exc_info=True)
            return False
        
    def handle_power_on_command(self, data, device_id):
        """处理平台下发的开机命令

        Args:
            data: 指令数据
            device_id: 设备ID
        """
        try:
            # 先进行基本验证，避免记录无效消息
            self.logger.debug(f"收到开机相关消息，正在验证: dir={data.get('dir')}, devid={data.get('devid')}")

            # 验证设备ID
            if "devid" not in data or str(data["devid"]) != str(self.device_id):
                self.logger.warning(f"设备ID不匹配，期望: {self.device_id}, 收到: {data.get('devid')}")
                return False

            # 验证方向
            if "dir" not in data or data["dir"] != "down":
                if data.get("dir") == "up":
                    # 本地回环消息，直接忽略
                    self.logger.debug("忽略本地回环的开机消息")
                    return False
                self.logger.warning(f"消息方向不正确，期望: down, 收到: {data.get('dir')}")
                return False

            # 只有通过所有验证的消息才记录为"收到平台开机命令"
            self.logger.info(f"收到平台开机命令: {data}")
            
            # 提取消息ID用于回复跟踪
            message_id = data.get("message_id", f"unknown_{int(time.time())}")
            
            # 执行开机回调
            if self.device_callbacks["power_on"]:
                self.logger.info("执行设备开机回调")
                try:
                    self.device_callbacks["power_on"]()
                    self.device_status["power_status"] = 1
                    self.logger.info("设备开机成功")
                    
                    # 回复平台命令
                    self.reply_to_power_on_command(message_id)
                    return True
                except Exception as e:
                    self.logger.error(f"执行开机回调时发生错误: {e}", exc_info=True)
                    return False
            else:
                self.logger.warning("未设置开机回调，无法执行开机操作")
                return False
                
        except Exception as e:
            self.logger.error(f"处理平台开机命令时发生错误: {e}", exc_info=True)
            return False
        
    def handle_power_off_command(self, data, device_id):
        """处理平台下发的关机命令
        
        Args:
            data: 指令数据
            device_id: 设备ID
        """
        try:
            self.logger.info(f"收到平台关机命令: {data}")
            
            # 验证设备ID
            if "devid" not in data or str(data["devid"]) != str(self.device_id):
                self.logger.warning(f"设备ID不匹配，期望: {self.device_id}, 收到: {data.get('devid')}")
                return False
            
            # 验证方向
            if "dir" not in data or data["dir"] != "down":
                if data.get("dir") == "up":
                    # 本地回环消息，直接忽略
                    return False
                self.logger.warning(f"消息方向不正确，期望: down, 收到: {data.get('dir')}")
                return False
                
            # 提取消息ID用于回复跟踪
            message_id = data.get("message_id", f"unknown_{int(time.time())}")
            
            # 执行关机回调
            if self.device_callbacks["power_off"]:
                self.logger.info("执行设备关机回调")
                try:
                    self.device_callbacks["power_off"]()
                    self.device_status["power_status"] = 2
                    self.logger.info("设备关机成功")
                    
                    # 回复平台命令
                    self.reply_to_power_off_command(message_id)
                    return True
                except Exception as e:
                    self.logger.error(f"执行关机回调时发生错误: {e}", exc_info=True)
                    return False
            else:
                self.logger.warning("未设置关机回调，无法执行关机操作")
                return False
                
        except Exception as e:
            self.logger.error(f"处理平台关机命令时发生错误: {e}", exc_info=True)
            return False
        
    def handle_restart_command(self, data, device_id):
        """处理平台下发的重启命令
        
        Args:
            data: 指令数据
            device_id: 设备ID
        """
        try:
            self.logger.info(f"收到平台重启命令: {data}")
            
            # 验证设备ID
            if "devid" not in data or str(data["devid"]) != str(self.device_id):
                self.logger.warning(f"设备ID不匹配，期望: {self.device_id}, 收到: {data.get('devid')}")
                return False
            
            # 验证方向
            if "dir" not in data or data["dir"] != "down":
                if data.get("dir") == "up":
                    # 本地回环消息，直接忽略
                    return False
                self.logger.warning(f"消息方向不正确，期望: down, 收到: {data.get('dir')}")
                return False
                
            # 提取消息ID用于回复跟踪
            message_id = data.get("message_id", f"unknown_{int(time.time())}")
            
            # 执行重启回调
            if self.device_callbacks["restart"]:
                self.logger.info("执行设备重启回调")
                try:
                    # 先回复平台命令，因为重启后可能无法回复
                    self.reply_to_restart_command(message_id)
                    
                    # 执行重启回调
                    self.device_callbacks["restart"]()
                    self.logger.info("设备重启成功")
                    return True
                except Exception as e:
                    self.logger.error(f"执行重启回调时发生错误: {e}", exc_info=True)
                    return False
            else:
                self.logger.warning("未设置重启回调，无法执行重启操作")
                return False
                
        except Exception as e:
            self.logger.error(f"处理平台重启命令时发生错误: {e}", exc_info=True)
            return False
        
    def handle_power_on_reply(self, data, device_id):
        """处理平台对设备开机上报的回复
        
        Args:
            data: 回复数据
            device_id: 设备ID
        """
        try:
            self.logger.info(f"收到平台对开机状态上报的回复: {data}")
            
            # 验证设备ID
            if "devid" not in data or str(data["devid"]) != str(self.device_id):
                self.logger.warning(f"设备ID不匹配，期望: {self.device_id}, 收到: {data.get('devid')}")
                return False
                
            # 验证方向
            if "dir" not in data or data["dir"] != "down":
                self.logger.warning(f"消息方向不正确，期望: down, 收到: {data.get('dir')}")
                return False
            
            # 验证通过，此消息确实是平台对设备上报的回复
            self.logger.info("平台已确认设备开机状态上报")
            
            # 记录响应代码和消息，但不作为验证的必要条件
            if "code" in data:
                code = data.get("code", "未知")
                msg = data.get("msg", "无消息")
                self.logger.debug(f"平台响应: 代码={code}, 消息={msg}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"处理平台开机回复时发生错误: {e}", exc_info=True)
            return False
        
    def handle_power_off_reply(self, data, device_id):
        """处理平台对设备关机上报的回复
        
        Args:
            data: 回复数据
            device_id: 设备ID
        """
        try:
            self.logger.info(f"收到平台对关机状态上报的回复: {data}")
            
            # 验证设备ID
            if "devid" not in data or str(data["devid"]) != str(self.device_id):
                self.logger.warning(f"设备ID不匹配，期望: {self.device_id}, 收到: {data.get('devid')}")
                return False
                
            # 验证方向
            if "dir" not in data or data["dir"] != "down":
                self.logger.warning(f"消息方向不正确，期望: down, 收到: {data.get('dir')}")
                return False
            
            # 验证通过，此消息确实是平台对设备上报的回复
            self.logger.info("平台已确认设备关机状态上报")
            
            # 记录响应代码和消息，但不作为验证的必要条件
            if "code" in data:
                code = data.get("code", "未知")
                msg = data.get("msg", "无消息")
                self.logger.debug(f"平台响应: 代码={code}, 消息={msg}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"处理平台关机回复时发生错误: {e}", exc_info=True)
            return False
        
    def handle_restart_reply(self, data, device_id):
        """处理平台对设备重启上报的回复
        
        Args:
            data: 回复数据
            device_id: 设备ID
        """
        try:
            self.logger.info(f"收到平台对重启状态上报的回复: {data}")
            
            # 验证设备ID
            if "devid" not in data or str(data["devid"]) != str(self.device_id):
                self.logger.warning(f"设备ID不匹配，期望: {self.device_id}, 收到: {data.get('devid')}")
                return False
                
            # 验证方向
            if "dir" not in data or data["dir"] != "down":
                self.logger.warning(f"消息方向不正确，期望: down, 收到: {data.get('dir')}")
                return False
            
            # 验证通过，此消息确实是平台对设备上报的回复
            self.logger.info("平台已确认设备重启状态上报")
            
            # 记录响应代码和消息，但不作为验证的必要条件
            if "code" in data:
                code = data.get("code", "未知")
                msg = data.get("msg", "无消息")
                self.logger.debug(f"平台响应: 代码={code}, 消息={msg}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"处理平台重启回复时发生错误: {e}", exc_info=True)
            return False
        
    def handle_swsensor_command(self, data, device_id):
        """处理组件动态开关指令
        
        Args:
            data: 指令数据
            device_id: 设备ID
        """
        try:
            self.logger.info(f"收到组件动态开关指令: {data}")
            
            # 验证设备ID
            if "devid" in data and data["devid"] != self.device_id:
                self.logger.warning(f"组件动态开关指令设备ID不匹配: 期望={self.device_id}, 实际={data.get('devid')}")
                return False
            
            # 验证方向
            if "dir" in data and data["dir"] != "down":
                self.logger.warning(f"组件动态开关指令方向不正确: 期望=down, 实际={data.get('dir')}")
                return False
            
            # 获取消息ID，用于回复
            message_id = data.get("id", "")
            
            # 获取组件开关配置
            component_status = {}
            if "data" in data and isinstance(data["data"], dict):
                component_status = data["data"]
                
                # 执行组件切换回调
                if hasattr(self, '_handle_component_switch_callback') and self._handle_component_switch_callback:
                    self.logger.info(f"执行组件切换回调: {component_status}")
                    try:
                        result = self._handle_component_switch_callback(component_status)
                        self.logger.info(f"组件切换结果: {result}")
                        
                        # 回复组件切换结果
                        self._reply_swsensor_command(message_id, result)
                        return True
                    except Exception as e:
                        self.logger.error(f"执行组件切换回调时发生错误: {e}", exc_info=True)
                        # 回复失败
                        self._reply_swsensor_command(message_id, False)
                        return False
                else:
                    self.logger.warning("未设置组件切换回调，无法执行组件切换操作")
                    # 回复失败
                    self._reply_swsensor_command(message_id, False)
                    return False
            else:
                self.logger.warning("组件动态开关指令缺少data字段或格式不正确")
                # 回复失败
                self._reply_swsensor_command(message_id, False)
                return False
                
        except Exception as e:
            self.logger.error(f"处理组件动态开关指令时发生错误: {e}", exc_info=True)
            # 尝试回复失败
            try:
                message_id = data.get("id", "") if isinstance(data, dict) else ""
                self._reply_swsensor_command(message_id, False)
            except:
                pass
            return False

    def _reply_swsensor_command(self, message_id, success=True):
        """回复组件动态开关指令
        
        Args:
            message_id: 消息ID
            success: 是否成功
        """
        if not self.connected or not self.device_id:
            self.logger.warning("MQTT未连接或设备ID未设置，无法回复组件动态开关指令")
            return False
            
        try:
            # 准备消息数据
            message_data = {
                "devid": self.device_id,
                "code": 200 if success else 500,
                "msg": "success" if success else "failed",
                "data": {},
                "dir": "up"
            }
            
            # 如果有消息ID，添加到回复中
            if message_id:
                message_data["id"] = message_id
            
            # 从配置中获取主题
            topic = self.mqtt_config['swsensor_reply_topic'].format(device_id=self.device_id)
            
            # 将数据转换为JSON字符串
            payload = json.dumps(message_data)
            
            # 等待消息上传间隔
            self._wait_for_message_interval()

            # 发布消息
            result = self.client.publish(topic, payload, qos=1)

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"已回复组件动态开关指令，结果: {success}")
                return True
            else:
                self.logger.error(f"回复组件动态开关指令失败，错误码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"回复组件动态开关指令时发生异常: {e}", exc_info=True)
            return False

    def publish_sensor_data(self, data_item):
        """发布传感器数据

        Args:
            data_item: 传感器数据

        Returns:
            bool: True表示数据已成功发送到MQTT服务器或已加入本地队列等待发送
                  False表示数据发送失败且未成功加入队列（可能丢失）

        Note:
            此方法返回True仅表示数据发送操作成功，不代表平台已确认接收。
            平台确认接收是通过/xxc/once/sensor/{device_id}/reply回复处理的。
        """
        if not self.connected:
            # 如果未连接，将数据加入队列等待处理
            try:
                self.sensor_data_queue.put_nowait(data_item)
                return True
            except queue.Full:
                self.logger.error(f"传感器数据队列已满，数据丢失: 时间戳={data_item.get('time', data_item.get('timestamp'))}")
                return False
            
        try:
            # 确保设备ID存在
            if "devid" not in data_item or not data_item["devid"]:
                data_item["devid"] = self.device_id
            
            # 从配置中获取主题
            topic = self.mqtt_config['publish_topic'].format(device_id=data_item.get("devid", ""))
            
            # 生成消息ID，统一使用time字段
            message_id = f"sensor_{data_item.get('time', data_item.get('timestamp', int(time.time())))}_{random.randint(1000, 9999)}"
            
            # 检查是否是重复消息
            if self._is_duplicate_message(message_id):
                self.logger.debug(f"传感器数据是重复消息，已忽略: {message_id}")
                return True
                
            # 标记为已发送
            self._mark_message_sent(message_id)
            
            # 格式化数据为规定的结构
            formatted_data = {
                "devid": data_item.get("devid", self.device_id),
                "time": data_item.get("time", int(time.time())),
                "ver": data_item.get("ver", "2.0"),
                "dir": "up"
            }
            
            # 处理传感器数据字段
            if "data" in data_item:
                formatted_data["data"] = data_item["data"]
            else:
                # 如果没有data字段，从数据项中提取传感器数据
                formatted_data["data"] = {}
                
                # 温湿度CO2
                if "temperature" in data_item:
                    formatted_data["data"]["temp"] = data_item["temperature"]
                if "temp" in data_item:
                    formatted_data["data"]["temp"] = data_item["temp"]
                if "humidity" in data_item:
                    formatted_data["data"]["hum"] = data_item["humidity"]
                if "hum" in data_item:
                    formatted_data["data"]["hum"] = data_item["hum"]
                if "co2" in data_item:
                    formatted_data["data"]["co2"] = data_item["co2"]
                
                # GPS数据
                if "longitude" in data_item and "latitude" in data_item:
                    formatted_data["data"]["gps"] = f"{data_item['longitude']},{data_item['latitude']}"
                if "gps" in data_item:
                    formatted_data["data"]["gps"] = data_item["gps"]
                
                # 风速
                if "wind_speed" in data_item:
                    formatted_data["data"]["ws"] = data_item["wind_speed"]
                if "ws" in data_item:
                    formatted_data["data"]["ws"] = data_item["ws"]
            
            # 将数据转换为JSON字符串
            payload = json.dumps(formatted_data)
            
            # 等待消息上传间隔
            self._wait_for_message_interval()

            # 发布消息
            result = self.client.publish(topic, payload, qos=1)

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                # 发布成功，等待平台回复确认
                return True
            else:
                self.logger.error(f"发布传感器数据失败，错误码: {result.rc}")
                
                # 发布失败，放入队列稍后重试
                try:
                    self.sensor_data_queue.put_nowait(data_item)
                    self.logger.debug(f"传感器数据发布失败，已加入队列")
                except queue.Full:
                    self.logger.error("传感器数据队列已满，数据丢失")
                    
                return False
                
        except Exception as e:
            self.logger.error(f"发布传感器数据时发生异常: {e}", exc_info=True)
            
            # 发生异常，放入队列稍后重试
            try:
                self.sensor_data_queue.put_nowait(data_item)
                self.logger.debug(f"传感器数据发布异常，已加入队列")
            except queue.Full:
                self.logger.error("传感器数据队列已满，数据丢失")
                
            return False

    def publish_error_data(self, error_item):
        """发布错误数据（重定向到设备自检上报）

        Args:
            error_item: 错误数据

        Returns:
            bool: 是否发布成功
        """
        # 提取错误信息
        sensor_type = error_item.get("sensor_type", "unknown")
        error_type = error_item.get("error_type", "未知错误")
        error_message = error_item.get("error_message", "未知错误")

        # 映射传感器类型到组件名
        component_map = {
            'cth': ['co2', 'temp', 'hum'],
            'gps': ['gps'],
            'ws': ['ws'],
            'co2_controller': ['co2device']
        }

        components = component_map.get(sensor_type, [sensor_type])
        full_message = f"{sensor_type}传感器{error_type}: {error_message}"

        # 判断是否为首次永久故障（立即上报）还是重复故障（使用退避）
        is_first_permanent_failure = "传感器永久故障" in error_type

        # 获取设备自检系统实例
        try:
            import builtins
            if hasattr(builtins, 'device_health_instance'):
                device_health = getattr(builtins, 'device_health_instance')

                for component in components:
                    if is_first_permanent_failure:
                        # 首次永久故障，立即上报
                        self.logger.info(f"首次永久故障，立即上报: component={component}, message={full_message}")
                        device_health.report_component_status(component, device_health.STATUS_ERROR, full_message)
                    else:
                        # 其他错误，使用退避策略
                        self.logger.debug(f"使用退避策略上报: component={component}, message={full_message}")
                        device_health.report_component_status_with_backoff(component, device_health.STATUS_ERROR, full_message)

                return True
            else:
                self.logger.warning("设备自检实例不可用，无法重定向错误上报")
                return False
        except Exception as e:
            self.logger.error(f"重定向错误上报时发生错误: {e}", exc_info=True)
            return False

    def upload_errors(self):
        """上传传感器错误数据（重定向到设备自检上报）
        
        此方法仅作兼容性保留，不再单独上传错误信息。
        所有错误均通过设备自检上报处理。
        """
        # 错误上报已重定向到设备自检上报，无需单独处理
        self.logger.debug("错误上报已重定向至设备自检上报，此方法不再执行实际操作")
        return True

    def upload_co2_status(self, is_scheduled=False):
        """上传CO2控制器状态数据，带时间间隔限制
        
        Args:
            is_scheduled: 是否是定时上传调用，如果是则忽略时间间隔限制
            
        Returns:
            bool: 上传是否成功
        """
        try:
            # 移除时间间隔限制，允许立即上传
            
            # 获取设备ID
            device_id = self.device_id
            if not device_id:
                self.logger.warning("设备ID未设置，无法上传CO2状态数据")
                # 标记为未就绪以避免频繁尝试上传
                if not self.components_ready.get("co2_controller", False):
                    self.logger.debug("CO2控制器未就绪，跳过上传")
                return False

            # 确保pending目录存在
            devices_dir = os.path.join(self.data_dir, self.storage_config.get('devices_dir', 'devices'))
            pending_dir = os.path.join(devices_dir, 'pending')
            if not os.path.exists(pending_dir):
                os.makedirs(pending_dir)
                self.logger.info(f"创建目录: {pending_dir}")
            
            # 检查是否有CO2状态数据文件
            if not os.path.isdir(pending_dir):
                self.logger.debug(f"CO2状态pending目录不存在: {pending_dir}")
                return False
                
            # 获取所有未上传的CO2状态文件
            co2_files = []
            for filename in os.listdir(pending_dir):
                if filename.startswith("co2_") and filename.endswith(".json"):
                    file_path = os.path.join(pending_dir, filename)
                    try:
                        # 获取文件修改时间和从文件名解析的时间戳
                        file_mtime = os.path.getmtime(file_path)
                        # 从文件名解析时间戳
                        ts = int(filename[4:-5])  # 提取co2_{timestamp}.json中的timestamp部分
                        co2_files.append((ts, file_mtime, file_path, filename))
                    except (ValueError, OSError) as e:
                        self.logger.warning(f"处理CO2状态文件时出错 {filename}: {e}")

            # 按时间戳排序
            co2_files.sort()  # 按时间戳升序排序（先上传旧数据）
            
            # 如果没有CO2状态数据，直接返回
            if not co2_files:
                self.logger.debug("没有CO2状态数据需要上传")
                return False
                
            # 确保uploaded目录存在
            uploaded_dir = os.path.join(devices_dir, 'uploaded')
            if not os.path.exists(uploaded_dir):
                os.makedirs(uploaded_dir)
            
            # 获取配置的最大批处理大小
            batch_size = self.mqtt_config.get('co2_batch_size', 5)
            uploaded_count = 0
            
            # 加载并上传CO2状态数据，每次最多处理batch_size个文件
            for _, _, file_path, filename in co2_files[:batch_size]:
                try:
                    # 读取文件内容
                    with open(file_path, 'r', encoding='utf-8') as f:
                        status_item = json.load(f)
                    
                    # 检查设备ID
                    if "devid" not in status_item or not status_item["devid"]:
                        status_item["devid"] = device_id
                    
                    # 发送到MQTT服务器
                    success = self.publish_co2_status(status_item)
                    
                    if success:
                        # 移动到uploaded目录
                        uploaded_path = os.path.join(uploaded_dir, filename)
                        shutil.move(file_path, uploaded_path)
                        self.logger.info(f"已将CO2状态数据文件移动到上传目录: {filename}")
                        uploaded_count += 1
                    else:
                        self.logger.warning(f"发送CO2状态数据失败，文件保留在pending目录: {filename}")
                    
                except Exception as e:
                    self.logger.error(f"处理CO2状态数据文件 {filename} 时出错: {e}")
            
            if uploaded_count > 0:
                self.logger.info(f"已上传 {uploaded_count} 个CO2状态数据文件")
                return True
            else:
                self.logger.warning("没有成功上传任何CO2状态数据文件")
                return False
                
        except Exception as e:
            self.logger.error(f"上传CO2状态数据失败: {e}", exc_info=True)
            return False

    def publish_co2_status(self, status_item):
        """发布CO2控制器状态数据
        
        Args:
            status_item: CO2状态数据
            
        Returns:
            bool: True表示数据已成功发送到MQTT服务器或已加入本地队列等待发送
                  False表示数据发送失败且未成功加入队列（可能丢失）
        """
        if not self.connected:
            # 如果未连接，将数据加入队列等待处理
            try:
                self.co2_status_queue.put_nowait(status_item)
                self.logger.debug(f"已将CO2状态数据添加到队列，时间戳: {status_item.get('time', status_item.get('timestamp', ''))}")
                return True
            except queue.Full:
                self.logger.error("CO2状态队列已满，数据丢失")
                return False
            
        try:
            # 确保设备ID存在
            if "devid" not in status_item or not status_item["devid"]:
                status_item["devid"] = self.device_id
                
            # 确保状态数据符合格式要求
            if "data" not in status_item:
                # 如果没有按照新格式提供数据，尝试转换
                self.logger.warning("CO2状态数据格式不正确，尝试转换为新格式")
                new_status_item = {
                    "data": {
                        "status": status_item.get("state", 0),
                        "round": status_item.get("round", 0),
                        "phase_remaining": status_item.get("phase_remaining", 0)
                    },
                    "time": status_item.get("time", int(time.time())),
                    "devid": status_item.get("devid", self.device_id),
                    "ver": "2.0"
                }
                status_item = new_status_item
            
            # 添加版本号和方向
            if "ver" not in status_item:
                status_item["ver"] = self.mqtt_config.get('version', '2.0')
            if "dir" not in status_item:
                status_item["dir"] = "up"
                
            # 从配置中获取主题
            topic = self.mqtt_config['co2_status_topic'].format(device_id=status_item.get("devid", ""))
            
            # 生成消息ID，统一使用time字段
            timestamp = status_item.get("time", status_item.get("timestamp", int(time.time())))
            message_id = f"co2_{timestamp}_{random.randint(1000, 9999)}"
            
            # 检查是否是重复消息
            if self._is_duplicate_message(message_id):
                self.logger.debug(f"CO2状态数据是重复消息，已忽略: {message_id}")
                return True
                
            # 标记为已发送
            self._mark_message_sent(message_id)
            
            # 创建要发送的数据副本，移除内部使用的字段
            publish_data = status_item.copy()
            if "uploaded" in publish_data:
                del publish_data["uploaded"]
            
            # 将数据转换为JSON字符串
            payload = json.dumps(publish_data)
            
            # 发布消息
            result = self.client.publish(topic, payload, qos=1)

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"已发送CO2状态数据到MQTT服务器(等待平台确认): 时间戳={timestamp}")
                
                # 将数据添加到队列，用于处理平台回复
                self.co2_status_queue.put(status_item)
                
                return True
            else:
                self.logger.error(f"发布CO2状态数据失败，错误码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布CO2状态数据时发生异常: {e}", exc_info=True)
            return False
    
    def publish_co2_error(self, error_data):
        """发布CO2控制器错误数据（重定向到设备自检上报）

        Args:
            error_data: CO2控制器错误数据

        Returns:
            bool: 是否发布成功
        """
        self.logger.info("CO2控制器错误上报已重定向至设备自检上报")

        # 提取错误信息
        error_type = error_data.get("error_type", "未知错误类型")
        error_message = error_data.get("error_message", "未知错误")
        component = "co2device"  # CO2控制器对应的组件名

        # 构造完整的错误消息
        full_message = f"{error_type}: {error_message}"

        # 获取设备自检系统实例
        try:
            # 通过builtins获取设备自检实例
            import builtins
            if hasattr(builtins, 'device_health_instance'):
                device_health = getattr(builtins, 'device_health_instance')
                # 通过设备自检实例的report_component_status方法上报错误
                device_health.report_component_status(component,
                                                    device_health.STATUS_ERROR,
                                                    full_message)
                self.logger.info(f"已将CO2控制器错误重定向至设备自检上报: component={component}, message={full_message}")
                return True
            else:
                self.logger.warning("设备自检实例不可用，无法重定向CO2控制器错误上报")
                return False
        except Exception as e:
            self.logger.error(f"重定向CO2控制器错误上报时发生错误: {e}", exc_info=True)
            return False

    def upload_co2_errors(self):
        """上传CO2控制器错误数据（重定向到设备自检上报）

        此方法仅作兼容性保留，不再单独上传CO2错误信息。
        所有错误均通过设备自检上报处理。
        """
        # CO2错误上报已重定向到设备自检上报，无需单独处理
        self.logger.debug("CO2错误上报已重定向至设备自检上报，此方法不再执行实际操作")
        return True

    def upload_device_check(self, is_scheduled=False):
        """上传设备自检数据，带时间间隔限制
        
        Args:
            is_scheduled: 是否是定时上传调用，如果是则忽略时间间隔限制
            
        Returns:
            bool: 上传是否成功
        """
        try:
            # 添加时间间隔限制，避免短时间内重复上传（定时上传除外）
            current_time = time.time()
            if not hasattr(self, '_last_check_upload_time'):
                self._last_check_upload_time = 0
                
            # 如果不是定时上传且距离上次上传不足30秒，则检查是否由立即上传标记触发
            if not is_scheduled and current_time - self._last_check_upload_time < 30:
                # 检查是否由立即上传标记触发
                is_immediate = False
                with self.immediate_upload_lock:
                    is_immediate = self.immediate_upload_flags['device_check']
                
                if not is_immediate:
                    self.logger.info(f"距离上次设备自检上传仅{current_time - self._last_check_upload_time:.1f}秒，跳过本次上传")
                    return True
                else:
                    self.logger.debug("由立即上传标记触发，忽略时间间隔限制")
            
            # 更新上传时间
            self._last_check_upload_time = current_time
            
            # 获取设备ID
            device_id = self.device_id
            if not device_id:
                self.logger.warning("设备ID未设置，无法上传自检数据")
                return False

            # 确保pending目录存在
            check_dir = os.path.join(self.data_dir, self.storage_config.get('check_dir', 'check'))
            pending_dir = os.path.join(check_dir, 'pending')
            if not os.path.exists(pending_dir):
                os.makedirs(pending_dir)
                self.logger.info(f"创建目录: {pending_dir}")
            
            # 检查是否有自检数据文件
            if not os.path.isdir(pending_dir):
                self.logger.debug(f"设备自检pending目录不存在: {pending_dir}")
                return False
                
            # 获取所有未上传的自检数据文件
            check_files = []
            for filename in os.listdir(pending_dir):
                if filename.startswith("check_") and filename.endswith(".json"):
                    file_path = os.path.join(pending_dir, filename)
                    try:
                        # 获取文件修改时间和从文件名解析的时间戳
                        file_mtime = os.path.getmtime(file_path)
                        # 从文件名解析时间戳
                        ts = int(filename[6:-5])  # 提取check_{timestamp}.json中的timestamp部分
                        check_files.append((ts, file_mtime, file_path, filename))
                    except (ValueError, OSError) as e:
                        self.logger.warning(f"处理自检文件时出错 {filename}: {e}")

            # 按时间戳排序
            check_files.sort()  # 按时间戳升序排序（先上传旧数据）
            
            # 如果没有自检数据，视为成功（避免无限重试）
            if not check_files:
                self.logger.debug("没有设备自检数据需要上传，视为成功")
                # 设置标记表示没有实际发送数据
                self._last_check_upload_had_data = False
                return True
                
            # 确保uploaded目录存在
            uploaded_dir = os.path.join(check_dir, 'uploaded')
            if not os.path.exists(uploaded_dir):
                os.makedirs(uploaded_dir)
            
            # 获取配置的最大批处理大小
            batch_size = self.mqtt_config.get('check_batch_size', 5)
            uploaded_count = 0
            
            # 加载并上传自检数据，每次最多处理batch_size个文件
            for _, _, file_path, filename in check_files[:batch_size]:
                try:
                    # 读取文件内容
                    with open(file_path, 'r', encoding='utf-8') as f:
                        check_item = json.load(f)
                    
                    # 检查设备ID
                    if "devid" not in check_item or not check_item["devid"]:
                        check_item["devid"] = device_id
                    
                    # 发送到MQTT服务器
                    success = self.publish_device_check(check_item)
                    
                    if success:
                        # 移动到uploaded目录
                        uploaded_path = os.path.join(uploaded_dir, filename)
                        shutil.move(file_path, uploaded_path)
                        self.logger.info(f"已将自检数据文件移动到上传目录: {filename}")
                        uploaded_count += 1
                    else:
                        self.logger.warning(f"发送自检数据失败，文件保留在pending目录: {filename}")
                    
                except Exception as e:
                    self.logger.error(f"处理自检数据文件 {filename} 时出错: {e}")
            
            if uploaded_count > 0:
                self.logger.info(f"已上传 {uploaded_count} 个设备自检数据文件")
                # 设置标记表示实际发送了数据
                self._last_check_upload_had_data = True
                return True
            else:
                self.logger.warning("没有成功上传任何设备自检数据文件")
                # 设置标记表示没有实际发送数据
                self._last_check_upload_had_data = False
                return False
        except Exception as e:
            self.logger.error(f"上传设备自检数据时出错: {e}", exc_info=True)
            return False

    def publish_power_on(self):
        """
        此方法已弃用，请使用 report_power_on_status 方法
        为保持向后兼容保留此方法
        """
        self.logger.warning("调用已弃用的 publish_power_on 方法，请改用 report_power_on_status")
        return self.report_power_on_status()
        
    def publish_power_off(self):
        """
        此方法已弃用，请使用 report_power_off_status 方法
        为保持向后兼容保留此方法
        """
        self.logger.warning("调用已弃用的 publish_power_off 方法，请改用 report_power_off_status")
        return self.report_power_off_status()
        
    def publish_restart(self):
        """
        此方法已弃用，请使用 report_restart_status 方法
        为保持向后兼容保留此方法
        """
        self.logger.warning("调用已弃用的 publish_restart 方法，请改用 report_restart_status")
        return self.report_restart_status()
        
    # 辅助方法 - 这些方法将从原代码复制过来
    def _add_error_entry(self, error_entry):
        """添加错误记录到错误日志文件
        
        Args:
            error_entry: 错误记录
            
        Returns:
            bool: 是否添加成功
        """
        try:
            # 读取现有错误数据
            error_data = []
            try:
                with open(self.sensor_errors_file, 'r', encoding='utf-8') as f:
                    error_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                self.logger.warning(f"无法读取错误数据文件或文件为空，将创建新文件")
                error_data = []
            
            # 添加新错误
            error_data.append(error_entry)
            
            # 写回文件
            with open(self.sensor_errors_file, 'w', encoding='utf-8') as f:
                json.dump(error_data, f, ensure_ascii=False)
                
            self.logger.debug(f"已添加错误记录到错误日志文件: {error_entry.get('timestamp', error_entry.get('time', ''))}")
            return True
        except Exception as e:
            self.logger.error(f"添加错误记录到错误日志文件时发生错误: {e}", exc_info=True)
            return False
        
    def _add_co2_error_entry(self, error_entry):
        """添加CO2控制器错误记录
        
        Args:
            error_entry: 错误记录
            
        Returns:
            bool: 是否添加成功
        """
        try:
            # 读取现有CO2错误数据
            co2_errors_data = []
            try:
                with open(self.co2_errors_file, 'r', encoding='utf-8') as f:
                    co2_errors_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                self.logger.warning(f"无法读取CO2错误数据文件或文件为空，将创建新文件")
                co2_errors_data = []
            
            # 添加新错误
            co2_errors_data.append(error_entry)
            
            # 写回文件
            with open(self.co2_errors_file, 'w', encoding='utf-8') as f:
                json.dump(co2_errors_data, f, ensure_ascii=False)
                
            self.logger.debug(f"已添加CO2控制器错误记录: {error_entry.get('time', '')}")
            return True
        except Exception as e:
            self.logger.error(f"添加CO2控制器错误记录时发生错误: {e}", exc_info=True)
            return False
        
    def _is_duplicate_message(self, message_id, expiry=None):
        """检查消息是否重复
        
        Args:
            message_id: 消息ID
            expiry: 过期时间（秒），如果为None则使用配置中的值
            
        Returns:
            bool: 是否是重复消息
        """
        with self.sent_messages_lock:
            # 清理过期的消息ID
            current_time = time.time()
            expired_ids = []
            
            # 使用配置的过期时间
            if expiry is None:
                expiry = self.message_expiry
                
            for msg_id, timestamp in self.sent_messages.items():
                if current_time - timestamp > expiry:
                    expired_ids.append(msg_id)
                    
            # 删除过期的消息ID
            for msg_id in expired_ids:
                del self.sent_messages[msg_id]
                
            # 如果缓存过大，删除最旧的消息ID
            if len(self.sent_messages) > self.max_message_cache:
                sorted_ids = sorted(self.sent_messages.items(), key=lambda x: x[1])
                oldest_ids = sorted_ids[:len(self.sent_messages) - self.max_message_cache]
                for msg_id, _ in oldest_ids:
                    del self.sent_messages[msg_id]
            
            # 检查消息ID是否已存在
            return message_id in self.sent_messages
        
    def _mark_message_sent(self, message_id):
        """标记消息为已发送
        
        Args:
            message_id: 消息ID
        """
        with self.sent_messages_lock:
            self.sent_messages[message_id] = time.time()

    def mark_data_as_uploaded(self, timestamps, is_queued=False):
        """将本地文件中指定时间戳的数据标记为已上传，并立即写回磁盘"""
        if not timestamps:
            return
        try:
            if os.path.exists(self.sensor_data_file):
                with open(self.sensor_data_file, 'r', encoding='utf-8') as f:
                    data_list = json.load(f)
                updated = False
                for item in data_list:
                    ts = item.get('time') or item.get('timestamp')
                    if ts in timestamps and not item.get('uploaded', False):
                        item['uploaded'] = True
                        updated = True
                if updated:
                    with open(self.sensor_data_file, 'w', encoding='utf-8') as f:
                        json.dump(data_list, f, indent=2)

        except Exception as e:
            self.logger.error(f"标记本地数据为已上传时异常: {e}")

    def mark_co2_status_as_uploaded(self, timestamps):
        """标记CO2状态数据为已上传

        Args:
            timestamps: 需要标记的时间戳列表
        """
        if not timestamps:
            return

        # CO2状态数据现在使用独立文件存储，不需要标记uploaded状态
        # 文件上传后直接移动到uploaded目录
        self.logger.debug(f"独立文件模式：跳过标记 {len(timestamps)} 条CO2状态数据")
        return
    
    def mark_errors_as_uploaded(self, timestamps):
        """将错误标记为已上传
        
        Args:
            timestamps: 需要标记的时间戳列表
        """
        if not timestamps:
            return
            
        try:
            # 读取错误数据
            error_data = []
            try:
                with open(self.sensor_errors_file, 'r', encoding='utf-8') as f:
                    error_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError) as e:
                self.logger.error(f"读取错误数据文件失败: {e}")
                return
                
            # 标记数据为已上传
            modified = False
            for item in error_data:
                # 支持新旧格式的时间戳字段
                item_time = None
                if "timestamp" in item:
                    item_time = item["timestamp"]
                elif "time" in item:
                    item_time = item["time"]
                    
                if item_time is not None and item_time in timestamps:
                    if not item.get("uploaded", False):
                        item["uploaded"] = True
                        modified = True
                        
            # 如果有修改，保存回文件
            if modified:
                with open(self.sensor_errors_file, 'w', encoding='utf-8') as f:
                    json.dump(error_data, f, ensure_ascii=False)
                self.logger.debug(f"已将 {len(timestamps)} 条错误数据标记为已上传")
                
                # 归档已上传的错误数据
                self._archive_uploaded_errors()
                
        except Exception as e:
            self.logger.error(f"标记CO2错误数据为已上传时发生错误: {e}", exc_info=True)
    
    def mark_co2_errors_as_uploaded(self, timestamps):
        """标记CO2错误数据为已上传
        
        Args:
            timestamps: 需要标记的时间戳列表
        """
        if not timestamps:
            return
            
        try:
            # 读取CO2错误数据
            co2_errors_data = []
            try:
                with open(self.co2_errors_file, 'r', encoding='utf-8') as f:
                    co2_errors_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError) as e:
                self.logger.error(f"读取CO2错误数据文件失败: {e}")
                return
                
            # 标记数据为已上传
            modified = False
            for item in co2_errors_data:
                # 支持新旧格式的时间戳字段
                item_time = None
                if "timestamp" in item:
                    item_time = item["timestamp"]
                elif "time" in item:
                    item_time = item["time"]
                    
                if item_time is not None and item_time in timestamps:
                    if not item.get("uploaded", False):
                        item["uploaded"] = True
                        modified = True
                        
            # 如果有修改，保存回文件
            if modified:
                with open(self.co2_errors_file, 'w', encoding='utf-8') as f:
                    json.dump(co2_errors_data, f, ensure_ascii=False)
                self.logger.debug(f"已将 {len(timestamps)} 条CO2错误数据标记为已上传")
                
                # 归档已上传的CO2错误数据
                self._archive_co2_errors(timestamps)
                
        except Exception as e:
            self.logger.error(f"标记CO2错误数据为已上传时发生错误: {e}", exc_info=True)
    
    def mark_device_checks_as_uploaded(self, timestamps, is_queued=False):
        """标记设备自检数据为已上传（独立文件模式下不需要操作）

        Args:
            timestamps: 时间戳列表
            is_queued: 是否只是加入队列
        """
        # 独立文件模式下，文件上传后直接移动到uploaded目录
        # 不需要在数组文件中标记uploaded状态
        self.logger.debug(f"独立文件模式：跳过标记 {len(timestamps)} 条设备自检数据")
        return
        
    def update_sensor_counts(self, processed_count, normal_count, error_count):
        """更新传感器数据计数
        
        Args:
            processed_count: 已处理的数据总数
            normal_count: 正常处理的数据数量
            error_count: 错误的数据数量
        """
        self.sensor_data_processed_count = processed_count
        self.sensor_data_success_count = normal_count
        self.sensor_data_error_count = error_count
        self.logger.debug(f"更新传感器计数 - 总数: {processed_count}, 正常: {normal_count}, 错误: {error_count}")
        
    def _archive_uploaded_errors(self):
        """归档已上传的错误数据"""
        try:
            # 读取现有错误数据
            error_data = []
            try:
                with open(self.sensor_errors_file, 'r', encoding='utf-8') as f:
                    error_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                self.logger.warning(f"无法读取错误数据文件或文件为空")
                error_data = []
                
            if not error_data:
                return
                
            # 分离已上传和未上传的数据
            uploaded_errors = [item for item in error_data if item.get("uploaded", False)]
            pending_errors = [item for item in error_data if not item.get("uploaded", False)]
            
            if not uploaded_errors:
                return
                
            # 读取归档数据
            archive_data = []
            try:
                with open(self.sensor_errors_archive_file, 'r', encoding='utf-8') as f:
                    archive_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                self.logger.warning(f"无法读取错误归档文件或文件为空，将创建新文件")
                archive_data = []
                
            # 添加已上传的数据到归档
            archive_data.extend(uploaded_errors)
            
            # 保存归档数据
            with open(self.sensor_errors_archive_file, 'w', encoding='utf-8') as f:
                json.dump(archive_data, f, ensure_ascii=False)
                
            # 保存未上传的数据
            with open(self.sensor_errors_file, 'w', encoding='utf-8') as f:
                json.dump(pending_errors, f, ensure_ascii=False)
                
            self.logger.info(f"已归档 {len(uploaded_errors)} 条错误数据")
            
        except Exception as e:
            self.logger.error(f"归档错误数据时发生错误: {e}", exc_info=True)
    
    def _archive_uploaded_co2_status(self):
        """归档已上传的CO2状态数据（独立文件模式下不需要操作）"""
        # CO2状态数据现在使用独立文件存储，上传后直接移动到uploaded目录
        # 不需要从数组文件中分离和归档数据
        self.logger.debug("独立文件模式：跳过CO2状态数据归档操作")
        return
    
    def _archive_uploaded_co2_errors(self):
        """归档已上传的CO2错误数据"""
        try:
            # 读取现有CO2错误数据
            co2_errors_data = []
            try:
                with open(self.co2_errors_file, 'r', encoding='utf-8') as f:
                    co2_errors_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                self.logger.warning(f"无法读取CO2错误数据文件或文件为空")
                co2_errors_data = []
                
            if not co2_errors_data:
                return
                
            # 分离已上传和未上传的数据
            uploaded_errors = [item for item in co2_errors_data if item.get("uploaded", False)]
            pending_errors = [item for item in co2_errors_data if not item.get("uploaded", False)]
            
            if not uploaded_errors:
                return
                
            # 读取归档数据
            archive_data = []
            try:
                with open(self.co2_errors_archive_file, 'r', encoding='utf-8') as f:
                    archive_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                self.logger.warning(f"无法读取CO2错误归档文件或文件为空，将创建新文件")
                archive_data = []
                
            # 添加已上传的数据到归档
            archive_data.extend(uploaded_errors)
            
            # 保存归档数据
            with open(self.co2_errors_archive_file, 'w', encoding='utf-8') as f:
                json.dump(archive_data, f, ensure_ascii=False)
                
            # 保存未上传的数据
            with open(self.co2_errors_file, 'w', encoding='utf-8') as f:
                json.dump(pending_errors, f, ensure_ascii=False)
                
            self.logger.info(f"已归档 {len(uploaded_errors)} 条CO2错误数据")
            
        except Exception as e:
            self.logger.error(f"归档CO2错误数据时发生错误: {e}", exc_info=True)
        
    # 组件管理方法
    def set_component_ready(self, component_name, is_ready=True):
        """设置组件就绪状态"""
        if component_name in self.components_ready:
            self.components_ready[component_name] = is_ready
            self.logger.info(f"组件 {component_name} 状态更新为: {'就绪' if is_ready else '未就绪'}")
        else:
            self.logger.warning(f"未知组件名称: {component_name}")

    # 立即上报标记方法
    def mark_co2_status_for_immediate_upload(self):
        """标记CO2状态需要立即上报，移除时间间隔限制"""
        with self.immediate_upload_lock:
            # 直接标记需要立即上报，移除时间间隔限制
            self.immediate_upload_flags['co2_status'] = True
            self.logger.debug("已标记CO2状态需要立即上报")

    def mark_device_check_for_immediate_upload(self):
        """标记设备自检需要立即上报，时间戳问题已修复，恢复文件检查逻辑"""
        with self.immediate_upload_lock:
            current_time = time.time()
            if not hasattr(self, '_last_check_mark_time'):
                self._last_check_mark_time = 0

            # 检查是否在短时间内重复标记
            if current_time - self._last_check_mark_time < 10:  # 10秒防重复
                self.logger.debug(f"设备自检标记间隔过短({current_time - self._last_check_mark_time:.1f}秒)，忽略重复标记")
                return

            # 检查是否已经有pending的自检数据文件
            try:
                check_dir = os.path.join(self.data_dir, self.storage_config.get('check_dir', 'check'))
                pending_dir = os.path.join(check_dir, 'pending')
                if os.path.exists(pending_dir):
                    check_files = [f for f in os.listdir(pending_dir) if f.startswith("check_") and f.endswith(".json")]
                    if not check_files:
                        self.logger.debug("没有pending的自检数据文件，跳过立即上传标记")
                        self._last_check_mark_time = current_time  # 更新时间，避免频繁检查
                        return
                else:
                    self.logger.debug(f"pending目录不存在: {pending_dir}")
                    self._last_check_mark_time = current_time
                    return
            except Exception as e:
                self.logger.warning(f"检查pending自检数据文件时出错: {e}")

            self.immediate_upload_flags['device_check'] = True
            self._last_check_mark_time = current_time
            self.logger.info("已标记设备自检需要立即上报")

    def mark_sensor_data_for_immediate_upload(self):
        """标记传感器数据需要立即上报，带时间戳防重复"""
        with self.immediate_upload_lock:
            # 检查之前是否已经标记，避免重复标记
            if self.immediate_upload_flags['sensor_data']:
                # 检查上次标记时间，如果间隔小于5秒，则忽略
                current_time = time.time()
                if not hasattr(self, '_last_sensor_mark_time'):
                    self._last_sensor_mark_time = 0
                
                if current_time - self._last_sensor_mark_time < 5:
                    self.logger.debug("传感器数据已被标记且间隔小于5秒，忽略重复标记")
                    return
                self.logger.debug("传感器数据已被标记但间隔超过5秒，更新标记时间")
                
            self.immediate_upload_flags['sensor_data'] = True
            self._last_sensor_mark_time = time.time()
            self.logger.debug("已标记传感器数据需要立即上报")

    def _get_latest_sensor_data_from_cache(self):
        """从传感器收集器缓存中获取最新数据"""
        try:
            import builtins
            if hasattr(builtins, 'sensor_collector_instance'):
                sensor_collector = getattr(builtins, 'sensor_collector_instance')

                # 获取缓存中的最新数据
                with sensor_collector.file_operation_lock:
                    if sensor_collector.sensor_data_cache:
                        # 获取最新的一条数据
                        latest_data = sensor_collector.sensor_data_cache[-1].copy()
                        self.logger.debug(f"从缓存获取到最新传感器数据: 时间戳={latest_data.get('time')}")
                        return latest_data
                    else:
                        self.logger.debug("传感器数据缓存为空")
                        return None
            else:
                self.logger.warning("传感器收集器实例不可用")
                return None
        except Exception as e:
            self.logger.error(f"从缓存获取传感器数据失败: {e}")
            return None

    def _upload_sensor_data_direct(self, sensor_data):
        """直接上传传感器数据（从缓存）"""
        try:
            if not sensor_data:
                self.logger.warning("传感器数据为空，无法上传")
                return False

            device_id = self.device_id
            if not device_id:
                self.logger.error("设备ID未设置，无法上传传感器数据")
                return False

            timestamp = sensor_data.get('time')

            # 确保设备ID正确
            if "devid" not in sensor_data or not sensor_data["devid"]:
                sensor_data["devid"] = device_id

            # 添加版本号和方向
            if "ver" not in sensor_data:
                sensor_data["ver"] = self.mqtt_config.get('version', '2.0')
            if "dir" not in sensor_data:
                sensor_data["dir"] = "up"

            # 强制检查连接状态
            self._check_connection_status()

            # 发布数据
            success = self.publish_sensor_data(sensor_data)

            if success:
                self.logger.info(f"传感器数据上传成功(从缓存): 时间戳={timestamp}")

                # 记录已上传的时间戳，防止重复上传
                with self.uploaded_timestamps_lock:
                    self.uploaded_sensor_timestamps.add(timestamp)

                # 标记缓存中的数据为已上传
                try:
                    import builtins
                    if hasattr(builtins, 'sensor_collector_instance'):
                        sensor_collector = getattr(builtins, 'sensor_collector_instance')
                        with sensor_collector.file_operation_lock:
                            for item in sensor_collector.sensor_data_cache:
                                if item.get('time') == timestamp:
                                    item['uploaded'] = True
                                    break
                        self.logger.debug(f"已标记缓存数据为已上传: 时间戳={timestamp}")
                except Exception as e:
                    self.logger.error(f"标记缓存数据为已上传失败: {e}")
            else:
                self.logger.warning(f"传感器数据上传失败(从缓存): 时间戳={timestamp}")

            return success

        except Exception as e:
            self.logger.error(f"直接上传传感器数据失败: {e}", exc_info=True)
            return False

    def upload_sensor_data(self, is_scheduled=False):
        """上传传感器数据，优先从缓存获取，回退到文件读取，带时间间隔限制
        
        Args:
            is_scheduled: 是否是定时上传调用，如果是则忽略时间间隔限制
        
        Returns:
            bool: 上传是否成功
        """
        try:
            # 记录调用堆栈，帮助追踪重复上传问题
            import inspect
            caller_frame = inspect.currentframe().f_back
            caller_info = f"{caller_frame.f_code.co_name}:{caller_frame.f_lineno}" if caller_frame else "未知位置"
            self.logger.debug(f"传感器数据上传被调用，调用来源: {caller_info}, 定时上传={is_scheduled}")
            
            # 添加时间间隔限制，避免短时间内重复上传（定时上传除外）
            current_time = time.time()
            if not hasattr(self, '_last_sensor_upload_time'):
                self._last_sensor_upload_time = 0
                
            # 如果不是定时上传且距离上次上传不足30秒，则检查是否由立即上传标记触发
            if not is_scheduled and current_time - self._last_sensor_upload_time < 30:
                # 检查是否由立即上传标记触发
                is_immediate = False
                with self.immediate_upload_lock:
                    is_immediate = self.immediate_upload_flags['sensor_data']
                
                if not is_immediate:
                    self.logger.info(f"距离上次传感器数据上传仅{current_time - self._last_sensor_upload_time:.1f}秒，跳过本次上传")
                    return True
                else:
                    self.logger.debug("由立即上传标记触发，忽略时间间隔限制")
            
            # 更新上传时间
            self._last_sensor_upload_time = current_time
            
            # 重置传感器回复计数器，为新的上传做准备
            self._sensor_reply_count = 0

            # 方案B：优先从缓存获取最新数据
            latest_cache_data = self._get_latest_sensor_data_from_cache()
            if latest_cache_data:
                self.logger.debug("使用缓存数据进行传感器数据上传")
                return self._upload_sensor_data_direct(latest_cache_data)
            else:
                # 回退到从文件读取
                self.logger.debug("缓存无数据，回退到从文件读取传感器数据")
                return self._upload_sensor_data_from_file()

        except Exception as e:
            self.logger.error(f"上传传感器数据失败: {e}", exc_info=True)
            return False

    def _cleanup_old_upload_records(self):
        """清理过期的上传记录，避免内存无限增长"""
        try:
            current_time = int(time.time())
            # 清理24小时前的记录
            cutoff_time = current_time - 86400

            with self.uploaded_timestamps_lock:
                # 过滤掉过期的时间戳
                self.uploaded_sensor_timestamps = {
                    ts for ts in self.uploaded_sensor_timestamps
                    if ts > cutoff_time
                }

            self.logger.debug(f"已清理过期上传记录，当前记录数: {len(self.uploaded_sensor_timestamps)}")

        except Exception as e:
            self.logger.error(f"清理上传记录失败: {e}")

    def _force_flush_sensor_cache(self):
        """强制刷新传感器缓存到文件"""
        try:
            import builtins
            if hasattr(builtins, 'sensor_collector_instance'):
                sensor_collector = getattr(builtins, 'sensor_collector_instance')
                sensor_collector._flush_cache_to_disk()
                self.logger.debug("已强制刷新传感器缓存到文件")
        except Exception as e:
            self.logger.error(f"强制刷新缓存失败: {e}")

    def register_device_callbacks(self, power_on_cb=None, power_off_cb=None, restart_cb=None, component_switch_cb=None):
        """注册设备操作回调函数"""
        if power_on_cb is not None:
            self.device_callbacks["power_on"] = power_on_cb
            
        if power_off_cb is not None:
            self.device_callbacks["power_off"] = power_off_cb
            
        if restart_cb is not None:
            self.device_callbacks["restart"] = restart_cb
            
        if component_switch_cb is not None:
            self._handle_component_switch_callback = component_switch_cb
            
        self.logger.info("已注册设备操作回调函数")

    def _add_sensor_data_to_file(self, data_item):
        """将传感器数据保存到本地文件
        
        Args:
            data_item: 传感器数据
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 读取现有数据
            sensor_data = []
            try:
                with open(self.sensor_data_file, 'r', encoding='utf-8') as f:
                    sensor_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                self.logger.warning(f"无法读取传感器数据文件或文件为空，将创建新文件")
                sensor_data = []
            
            # 添加新数据
            sensor_data.append(data_item)
            
            # 写回文件
            with open(self.sensor_data_file, 'w', encoding='utf-8') as f:
                json.dump(sensor_data, f, ensure_ascii=False)
                
            self.logger.debug(f"已保存传感器数据到本地文件: {data_item.get('time', '')}")
            return True
        except Exception as e:
            self.logger.error(f"保存传感器数据到本地文件时发生错误: {e}", exc_info=True)
            return False
            
    def _add_co2_status_to_file(self, status_item):
        """将CO2状态数据保存为独立文件
        
        Args:
            status_item: CO2状态数据
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 准备保存的数据
            local_status_data = status_item.copy()
            # 统一使用time字段作为时间戳
            timestamp = local_status_data.get('time', int(time.time()))
            # 确保time字段存在
            local_status_data["time"] = timestamp
            # 添加上传标志
            local_status_data["uploaded"] = False
            
            # 确保pending目录存在
            devices_dir = os.path.join(self.data_dir, self.storage_config.get('devices_dir', 'devices'))
            pending_dir = os.path.join(devices_dir, 'pending')
            if not os.path.exists(pending_dir):
                os.makedirs(pending_dir)
                self.logger.info(f"创建目录: {pending_dir}")
            
            # 使用时间戳作为文件名
            file_path = os.path.join(pending_dir, f"co2_{timestamp}.json")
            
            # 检查是否已存在同名文件(防止重复)
            if os.path.exists(file_path):
                self.logger.warning(f"跳过重复CO2状态数据保存: 时间戳={timestamp} 文件已存在")
                return True
            
            # 保存为单独的JSON文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(local_status_data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug(f"已保存CO2状态数据到文件: {file_path}")
            return True
        except Exception as e:
            self.logger.error(f"保存CO2状态数据到文件时发生错误: {e}", exc_info=True)
            return False
            
    # _add_device_check_to_file 方法已移除，设备自检数据现在使用独立文件存储
            
    def _save_credentials(self):
        """保存凭证到本地文件"""
        if not self.client_id or not self.username or not self.password or not self.broker or not self.port:
            self.logger.warning("MQTT凭证不完整，无法保存")
            return False
            
        credentials = {
            "client_id": self.client_id,
            "username": self.username,
            "password": self.password,
            "broker": self.broker,
            "port": self.port,
            "device_id": self.device_id,
            "timestamp": int(time.time())
        }
        
        credentials_file = os.path.join(self.data_dir, 
                                       self.storage_config.get('credentials_file', 'credentials.json'))
        
        try:
            with open(credentials_file, 'w', encoding='utf-8') as f:
                json.dump(credentials, f)
            self.logger.info(f"凭证已保存到本地: {credentials_file}")
            return True
        except Exception as e:
            self.logger.error(f"保存凭证失败: {e}")
            return False
            
    def _load_credentials(self):
        """从本地文件加载凭证"""
        credentials_file = os.path.join(self.data_dir, 
                                       self.storage_config.get('credentials_file', 'credentials.json'))
        
        if not os.path.exists(credentials_file):
            self.logger.info("本地凭证文件不存在")
            return False
            
        try:
            with open(credentials_file, 'r', encoding='utf-8') as f:
                credentials = json.load(f)
                
            # 检查凭证是否过期
            credential_expiry_days = self.mqtt_config.get('credential_expiry_days', 30)  # 凭证过期天数，默认30天
            expiry_seconds = credential_expiry_days * 24 * 3600
            
            if time.time() - credentials.get("timestamp", 0) > expiry_seconds:
                self.logger.info(f"本地凭证已过期 (超过 {credential_expiry_days} 天)")
                return False
                
            # 检查凭证完整性
            required_fields = ["client_id", "username", "password", "broker", "port", "device_id"]
            if not all(credentials.get(field) for field in required_fields):
                self.logger.warning("本地凭证不完整")
                return False
                
            self.client_id = credentials.get("client_id")
            self.username = credentials.get("username")
            self.password = credentials.get("password")
            self.broker = credentials.get("broker")
            self.port = credentials.get("port")
            
            # 从client_id中提取设备ID
            # client_id格式为"GID-SD-X1@@@SD-X1-0003"，取@@@后面的部分作为设备ID
            original_device_id = credentials.get("device_id")
            if self.client_id and "@@@" in self.client_id:
                extracted_device_id = self.client_id.split("@@@")[1]
                self.device_id = extracted_device_id
                self.logger.info(f"从client_id提取设备ID: {self.device_id} (原始ID: {original_device_id})")
            else:
                # 如果client_id中没有@@@，则使用原始device_id
                self.device_id = original_device_id
                self.logger.info(f"使用原始设备ID: {self.device_id}")
            
            self.logger.info("成功加载本地凭证")
            return True
        except Exception as e:
            self.logger.error(f"加载凭证失败: {e}")
            return False

    def publish_device_check(self, check_item):
        """发布设备自检数据
        
        Args:
            check_item: 自检数据项
        
        Returns:
            bool: 是否发布成功
        """
        if not self.connected:
            # 如果未连接，将数据加入队列等待发送
            try:
                self.check_data_queue.put_nowait(check_item)
                self.logger.debug(f"已将自检数据添加到队列，时间戳: {check_item.get('time', check_item.get('timestamp', ''))}")
                return True
            except queue.Full:
                self.logger.error(f"自检数据队列已满，数据丢失: 时间戳={check_item.get('time', check_item.get('timestamp'))}")
                return False
            
        try:
            # 确保设备ID正确
            device_id = self.device_id
            if "devid" not in check_item or not check_item["devid"]:
                check_item["devid"] = device_id
                
            # 添加版本号和方向
            if "ver" not in check_item:
                check_item["ver"] = self.mqtt_config.get('version', '2.0')
            if "dir" not in check_item:
                check_item["dir"] = "up"
            
            # 从配置中获取主题
            topic = self.mqtt_config['check_topic'].format(device_id=check_item["devid"])
            
            # 生成消息ID，统一使用time字段
            timestamp = check_item.get("time", check_item.get("timestamp", int(time.time())))
            message_id = f"check_{timestamp}_{random.randint(1000, 9999)}"
            
            # 检查是否是重复消息
            if self._is_duplicate_message(message_id):
                self.logger.debug(f"自检数据是重复消息，已忽略: {message_id}")
                return True
                
            # 标记为已发送
            self._mark_message_sent(message_id)
            
            # 将数据转换为JSON字符串
            payload = json.dumps(check_item)
            
            # 等待消息上传间隔
            self._wait_for_message_interval()

            # 发布消息
            result = self.client.publish(topic, payload, qos=1)

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"已发送设备自检数据到MQTT服务器(等待平台确认): 时间戳={timestamp}")
                # 将数据添加到队列，用于处理平台回复
                # 注意：不要移动文件到uploaded目录，等待回复后再移动
                self.check_data_queue.put(check_item)
                
                # 设置超时等待回复
                # 这里不等待回复，而是在handle_device_check_reply中处理回复
                # 返回True表示消息已成功发送到服务器，但不代表服务器已确认
                return True
            else:
                self.logger.error(f"发布设备自检数据失败，错误码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布设备自检数据时出错: {e}", exc_info=True)
            return False

    def _upload_sensor_data_from_file(self):
        """从文件上传传感器数据，优先选择有效数据"""
        try:
            # 检查文件是否存在
            if not os.path.exists(self.sensor_data_file):
                return

            # 读取文件中的数据
            sensor_data_list = []
            try:
                with open(self.sensor_data_file, 'r', encoding='utf-8') as f:
                    sensor_data_list = json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                self.logger.error(f"读取传感器数据文件失败: {e}")
                return

            if not sensor_data_list:
                return

            # 应用去重逻辑
            sensor_data_list = self._deduplicate_data_by_timestamp(sensor_data_list, "传感器数据")

            # 获取设备ID
            device_id = self.device_id
            if not device_id:
                self.logger.warning("设备ID未设置，无法上传传感器数据")
                return

            # 筛选最近10分钟的有效数据，去重
            current_time = int(time.time())
            ten_minutes_ago = current_time - 600

            # 使用字典去重，保留最新的数据
            unique_data = {}
            for item in sensor_data_list:
                timestamp = item.get('time', 0)
                uploaded = item.get('uploaded', False)
                error = item.get('error', False)

                # 检查是否已在全局上传记录中
                with self.uploaded_timestamps_lock:
                    already_uploaded_globally = timestamp in self.uploaded_sensor_timestamps

                if (timestamp >= ten_minutes_ago and not uploaded and not error and not already_uploaded_globally):
                    if timestamp not in unique_data or timestamp > unique_data[timestamp].get('time', 0):
                        unique_data[timestamp] = item

            recent_data = list(unique_data.values())

            # 如果没有最近有效数据，选择最新的未上传有效数据
            if not recent_data:
                pending_unique = {}
                for item in sensor_data_list:
                    timestamp = item.get('time')
                    # 检查是否已在全局上传记录中
                    with self.uploaded_timestamps_lock:
                        already_uploaded_globally = timestamp in self.uploaded_sensor_timestamps

                    if (not item.get('uploaded', False) and not item.get('error', False) and not already_uploaded_globally):
                        if timestamp not in pending_unique or item.get('time', 0) > pending_unique[timestamp].get('time', 0):
                            pending_unique[timestamp] = item

                if pending_unique:
                    # 按时间排序，选择最新的
                    recent_data = [sorted(pending_unique.values(), key=lambda x: x.get('time', 0))[-1]]

            # 选择最新的一条数据上传
            if recent_data:
                latest_data = sorted(recent_data, key=lambda x: x.get('time', 0))[-1]
                timestamp = latest_data.get('time')

                # 确保设备ID正确
                if "devid" not in latest_data or not latest_data["devid"]:
                    latest_data["devid"] = device_id

                # 添加版本号和方向
                if "ver" not in latest_data:
                    latest_data["ver"] = self.mqtt_config.get('version', '2.0')
                if "dir" not in latest_data:
                    latest_data["dir"] = "up"

                # 强制检查连接状态
                self._check_connection_status()

                # 发布数据
                success = self.publish_sensor_data(latest_data)

                if success:
                    self.logger.info(f"传感器数据上传成功: 时间戳={timestamp}")
                    # 记录已上传的时间戳，防止重复上传
                    with self.uploaded_timestamps_lock:
                        self.uploaded_sensor_timestamps.add(timestamp)
                else:
                    self.logger.warning(f"传感器数据上传失败: 时间戳={timestamp}")

                return success
            else:
                return False

        except Exception as e:
            self.logger.error(f"上传传感器数据失败: {e}", exc_info=True)
            return False

    def _archive_co2_errors(self, timestamps):
        """将已上传的CO2控制器错误归档到历史文件
        
        Args:
            timestamps: 需要归档的时间戳列表
        """
        if not timestamps:
            return
            
        try:
            # 读取现有CO2错误数据
            co2_errors_data = []
            try:
                with open(self.co2_errors_file, 'r', encoding='utf-8') as f:
                    co2_errors_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                self.logger.warning(f"无法读取CO2错误数据文件或文件为空")
                co2_errors_data = []
                
            if not co2_errors_data:
                return
                
            # 分离要归档的数据和保留的数据
            to_archive = []
            to_keep = []
            
            for item in co2_errors_data:
                # 支持新旧格式的时间戳字段
                item_time = None
                if "timestamp" in item:
                    item_time = item["timestamp"]
                elif "time" in item:
                    item_time = item["time"]
                    
                if item_time is not None and item_time in timestamps:
                    to_archive.append(item)
                else:
                    to_keep.append(item)
            
            if not to_archive:
                return
                
            # 读取归档数据
            archive_data = []
            try:
                with open(self.co2_errors_archive_file, 'r', encoding='utf-8') as f:
                    archive_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                self.logger.warning(f"无法读取CO2错误归档文件或文件为空，将创建新文件")
                archive_data = []
                
            # 添加要归档的数据
            archive_data.extend(to_archive)
            
            # 保存归档数据
            with open(self.co2_errors_archive_file, 'w', encoding='utf-8') as f:
                json.dump(archive_data, f, ensure_ascii=False)
                
            # 保存保留的数据
            with open(self.co2_errors_file, 'w', encoding='utf-8') as f:
                json.dump(to_keep, f, ensure_ascii=False)
                
            self.logger.info(f"已归档 {len(to_archive)} 条CO2错误数据")
            
        except Exception as e:
            self.logger.error(f"归档CO2错误数据时发生错误: {e}", exc_info=True)

    def _archive_uploaded_sensor_data(self):
        try:
            # 读取现有传感器数据
            sensor_data = []
            try:
                with open(self.sensor_data_file, 'r', encoding='utf-8') as f:
                    sensor_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                self.logger.warning(f"无法读取传感器数据文件或文件为空")
                sensor_data = []

            if not sensor_data:
                return

            # 分离已上传和未上传的数据
            uploaded_data = [item for item in sensor_data if item.get("uploaded", False)]
            pending_data = [item for item in sensor_data if not item.get("uploaded", False)]

            if not uploaded_data:
                return
                
            # 读取归档数据
            archive_data = []
            try:
                with open(self.sensor_data_archive_file, 'r', encoding='utf-8') as f:
                    archive_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                self.logger.warning(f"无法读取传感器数据归档文件或文件为空，将创建新文件")
                archive_data = []
                
            # 添加已上传的数据到归档
            archive_data.extend(uploaded_data)
            
            # 保存归档数据
            with open(self.sensor_data_archive_file, 'w', encoding='utf-8') as f:
                json.dump(archive_data, f, ensure_ascii=False)
                
            # 保存未上传的数据
            with open(self.sensor_data_file, 'w', encoding='utf-8') as f:
                json.dump(pending_data, f, ensure_ascii=False)
                
            self.logger.info(f"已归档 {len(uploaded_data)} 条传感器数据")



        except Exception as e:
            self.logger.error(f"归档传感器数据时发生错误: {e}", exc_info=True)

    def _mark_latest_sensor_data_as_uploaded(self):
        """标记最近上传的传感器数据为已上传"""
        try:
            # 读取传感器数据文件
            sensor_data = []
            try:
                with open(self.sensor_data_file, 'r', encoding='utf-8') as f:
                    sensor_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                self.logger.warning("无法读取传感器数据文件")
                return

            if not sensor_data:
                self.logger.warning("传感器数据文件为空")
                return

            # 找到最近的未上传数据（按时间戳排序）
            pending_data = [item for item in sensor_data if not item.get("uploaded", False)]
            if not pending_data:
                self.logger.info("没有待上传的传感器数据")
                return

            # 按时间戳排序，获取最新的一条
            latest_data = sorted(pending_data, key=lambda x: x.get('time', 0))[-1]
            latest_timestamp = latest_data.get('time')

            # 标记为已上传（添加重复处理防护）
            marked = False
            for item in sensor_data:
                if item.get('time') == latest_timestamp and not marked:
                    item['uploaded'] = True
                    self.logger.info(f"已标记传感器数据为已上传: 时间戳={latest_timestamp}")
                    marked = True  # 防止重复标记

            # 写回文件
            with open(self.sensor_data_file, 'w', encoding='utf-8') as f:
                json.dump(sensor_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"标记传感器数据为已上传时发生错误: {e}", exc_info=True)


    def archive_device_checks(self):
        """归档所有当前设备自检数据，不依赖时间戳"""
        try:
            # 获取设备自检实例
            import builtins
            if not hasattr(builtins, 'device_health_instance'):
                self.logger.error("设备自检实例不可用，无法归档数据")
                return False
                
            device_health = getattr(builtins, 'device_health_instance')
            
            # 调用设备自检系统的归档方法
            if hasattr(device_health, 'archive_all_current_checks'):
                result = device_health.archive_all_current_checks()
                if result:
                    self.logger.info("设备自检数据归档成功")
                else:
                    self.logger.warning("设备自检数据归档失败")
                return result
            else:
                self.logger.error("设备自检实例缺少archive_all_current_checks方法")
                return False
                
        except Exception as e:
            self.logger.error(f"归档设备自检数据时发生错误: {e}", exc_info=True)
            return False
        
    def update_sensor_counts(self, processed_count, normal_count, error_count):
        """更新传感器数据计数
        
        Args:
            processed_count: 已处理的数据总数
            normal_count: 正常处理的数据数量
            error_count: 错误的数据数量
        """
        self.sensor_data_processed_count = processed_count
        self.sensor_data_success_count = normal_count
        self.sensor_data_error_count = error_count
    
    # 重复的_archive_uploaded_co2_status方法已移除
    
    def _archive_uploaded_co2_errors(self):
        """归档已上传的CO2错误数据"""
        try:
            # 读取现有CO2错误数据
            co2_errors_data = []
            try:
                with open(self.co2_errors_file, 'r', encoding='utf-8') as f:
                    co2_errors_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                self.logger.warning(f"无法读取CO2错误数据文件或文件为空")
                co2_errors_data = []
                
            if not co2_errors_data:
                return
                
            # 分离已上传和未上传的数据
            uploaded_errors = [item for item in co2_errors_data if item.get("uploaded", False)]
            pending_errors = [item for item in co2_errors_data if not item.get("uploaded", False)]
            
            if not uploaded_errors:
                return
                
            # 读取归档数据
            archive_data = []
            try:
                with open(self.co2_errors_archive_file, 'r', encoding='utf-8') as f:
                    archive_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                self.logger.warning(f"无法读取CO2错误归档文件或文件为空，将创建新文件")
                archive_data = []
                
            # 添加已上传的数据到归档
            archive_data.extend(uploaded_errors)
            
            # 保存归档数据
            with open(self.co2_errors_archive_file, 'w', encoding='utf-8') as f:
                json.dump(archive_data, f, ensure_ascii=False)
                
            # 保存未上传的数据
            with open(self.co2_errors_file, 'w', encoding='utf-8') as f:
                json.dump(pending_errors, f, ensure_ascii=False)
                
            self.logger.info(f"已归档 {len(uploaded_errors)} 条CO2错误数据")
            
        except Exception as e:
            self.logger.error(f"归档CO2错误数据时发生错误: {e}", exc_info=True)
        
    # 组件管理方法
    def set_component_ready(self, component_name, is_ready=True):
        """设置组件就绪状态"""
        if component_name in self.components_ready:
            self.components_ready[component_name] = is_ready
            self.logger.info(f"组件 {component_name} 状态更新为: {'就绪' if is_ready else '未就绪'}")
        else:
            self.logger.warning(f"未知组件名称: {component_name}")

    def register_device_callbacks(self, power_on_cb=None, power_off_cb=None, restart_cb=None, component_switch_cb=None):
        """注册设备操作回调函数"""
        if power_on_cb is not None:
            self.device_callbacks["power_on"] = power_on_cb
            
        if power_off_cb is not None:
            self.device_callbacks["power_off"] = power_off_cb
            
        if restart_cb is not None:
            self.device_callbacks["restart"] = restart_cb
            
        if component_switch_cb is not None:
            self._handle_component_switch_callback = component_switch_cb
            
        self.logger.info("已注册设备操作回调函数")

    def _add_sensor_data_to_file(self, data_item):
        """将传感器数据保存到本地文件
        
        Args:
            data_item: 传感器数据
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 读取现有数据
            sensor_data = []
            try:
                with open(self.sensor_data_file, 'r', encoding='utf-8') as f:
                    sensor_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                self.logger.warning(f"无法读取传感器数据文件或文件为空，将创建新文件")
                sensor_data = []
            
            # 添加新数据
            sensor_data.append(data_item)
            
            # 写回文件
            with open(self.sensor_data_file, 'w', encoding='utf-8') as f:
                json.dump(sensor_data, f, ensure_ascii=False)
                
            self.logger.debug(f"已保存传感器数据到本地文件: {data_item.get('time', '')}")
            return True
        except Exception as e:
            self.logger.error(f"保存传感器数据到本地文件时发生错误: {e}", exc_info=True)
            return False
            
    def _add_co2_status_to_file(self, status_item):
        """此方法已被移除，CO2状态数据现在使用独立文件存储

        Args:
            status_item: CO2状态数据

        Returns:
            bool: 始终返回True (兼容性保留)
        """
        # 该方法不再使用，已迁移到基于独立文件的存储方案
        self.logger.debug("_add_co2_status_to_file方法已弃用，忽略调用")
        return True
            
    def _add_device_check_to_file(self, check_item):
        """此方法已被移除，由独立文件方案替代
        
        Args:
            check_item: 设备自检数据
            
        Returns:
            bool: 始终返回True (兼容性保留)
        """
        # 该方法不再使用，已迁移到基于独立文件的存储方案
        self.logger.debug("_add_device_check_to_file方法已弃用，忽略调用")
        return True
            
    def _save_credentials(self):
        """保存凭证到本地文件"""
        if not self.client_id or not self.username or not self.password or not self.broker or not self.port:
            self.logger.warning("MQTT凭证不完整，无法保存")
            return False
            
        credentials = {
            "client_id": self.client_id,
            "username": self.username,
            "password": self.password,
            "broker": self.broker,
            "port": self.port,
            "device_id": self.device_id,
            "timestamp": int(time.time())
        }
        
        credentials_file = os.path.join(self.data_dir, 
                                       self.storage_config.get('credentials_file', 'credentials.json'))
        
        try:
            with open(credentials_file, 'w', encoding='utf-8') as f:
                json.dump(credentials, f)
            self.logger.info(f"凭证已保存到本地: {credentials_file}")
            return True
        except Exception as e:
            self.logger.error(f"保存凭证失败: {e}")
            return False
            
    def _load_credentials(self):
        """从本地文件加载凭证"""
        credentials_file = os.path.join(self.data_dir, 
                                       self.storage_config.get('credentials_file', 'credentials.json'))
        
        if not os.path.exists(credentials_file):
            self.logger.info("本地凭证文件不存在")
            return False
            
        try:
            with open(credentials_file, 'r', encoding='utf-8') as f:
                credentials = json.load(f)
                
            # 检查凭证是否过期
            credential_expiry_days = self.mqtt_config.get('credential_expiry_days', 30)  # 凭证过期天数，默认30天
            expiry_seconds = credential_expiry_days * 24 * 3600
            
            if time.time() - credentials.get("timestamp", 0) > expiry_seconds:
                self.logger.info(f"本地凭证已过期 (超过 {credential_expiry_days} 天)")
                return False
                
            # 检查凭证完整性
            required_fields = ["client_id", "username", "password", "broker", "port", "device_id"]
            if not all(credentials.get(field) for field in required_fields):
                self.logger.warning("本地凭证不完整")
                return False
                
            self.client_id = credentials.get("client_id")
            self.username = credentials.get("username")
            self.password = credentials.get("password")
            self.broker = credentials.get("broker")
            self.port = credentials.get("port")
            
            # 从client_id中提取设备ID
            # client_id格式为"GID-SD-X1@@@SD-X1-0003"，取@@@后面的部分作为设备ID
            original_device_id = credentials.get("device_id")
            if self.client_id and "@@@" in self.client_id:
                extracted_device_id = self.client_id.split("@@@")[1]
                self.device_id = extracted_device_id
                self.logger.info(f"从client_id提取设备ID: {self.device_id} (原始ID: {original_device_id})")
            else:
                # 如果client_id中没有@@@，则使用原始device_id
                self.device_id = original_device_id
                self.logger.info(f"使用原始设备ID: {self.device_id}")
            
            self.logger.info("成功加载本地凭证")
            return True
        except Exception as e:
            self.logger.error(f"加载凭证失败: {e}")
            return False

    def publish_mosquito_data(self, data, file_id):
        """发布蚊子检测数据 (兼容旧方法，内部调用简化版本)
        
        Args:
            data: 蚊子检测数据
            file_id: 文件ID，不再使用但保留参数兼容性
            
        Returns:
            bool: 是否成功发送
        """
        # 直接调用简化版本
        return self.publish_mosquito_data_simple(data)
    
    def handle_mosquito_reply(self, data, device_id):
        """处理蚊子检测数据回复

        Args:
            data: 回复数据
            device_id: 设备ID
        """
        try:
            self.logger.info(f"收到蚊子检测回复(平台确认): {data}")

            # 验证设备ID
            if "devid" not in data or str(data["devid"]) != str(self.device_id):
                self.logger.warning(f"蚊子检测回复设备ID不匹配: 期望={self.device_id}, 实际={data.get('devid')}")
                return False

            # 设备ID匹配，认为上传成功（不检查code字段）
            self.logger.info(f"蚊子检测数据上传成功确认（设备ID匹配）")

            # 记录平台响应信息（仅用于调试，不影响成功判断）
            if "code" in data:
                code = data.get("code", "未知")
                msg = data.get("msg", "无消息")
                self.logger.debug(f"平台响应详情: 代码={code}, 消息={msg}")

            # 简化的通知方法，只基于devid匹配
            self._notify_mosquito_result_simple(True, data)

        except Exception as e:
            self.logger.error(f"处理蚊子检测回复时发生错误: {e}", exc_info=True)
    
    def _notify_mosquito_result(self, file_id, success, data):
        """通知蚊子检测结果

        Args:
            file_id: 文件ID
            success: 是否成功
            data: 回复数据
        """
        # 使用事件回调通知监听器
        try:
            # 获取监控实例
            import builtins
            if hasattr(builtins, 'mosquito_monitor_instance'):
                monitor = getattr(builtins, 'mosquito_monitor_instance')
                monitor.handle_result(file_id, success, data)
        except Exception as e:
            self.logger.error(f"通知蚊子检测结果时发生错误: {e}", exc_info=True)

    def _notify_mosquito_result_without_file_id(self, success, data):
        """通知蚊子检测结果（不需要file_id）

        Args:
            success: 是否成功
            data: 回复数据
        """
        # 使用事件回调通知监听器处理最近的文件
        try:
            # 获取监控实例
            import builtins
            if hasattr(builtins, 'mosquito_monitor_instance'):
                monitor = getattr(builtins, 'mosquito_monitor_instance')
                monitor.handle_result_without_file_id(success, data)
        except Exception as e:
            self.logger.error(f"通知蚊子检测结果时发生错误: {e}", exc_info=True)

    def _notify_mosquito_result_by_devid(self, success, data):
        """通知蚊子检测结果（基于devid匹配）

        Args:
            success: 是否成功
            data: 回复数据
        """
        try:
            # 获取监控实例
            import builtins
            if hasattr(builtins, 'mosquito_monitor_instance'):
                monitor = getattr(builtins, 'mosquito_monitor_instance')
                monitor.handle_result_by_devid(success, data)
        except Exception as e:
            self.logger.error(f"通知蚊子检测结果时发生错误: {e}", exc_info=True)

    def simulate_message(self, message_data):
        """模拟处理MQTT消息，用于测试服务器
        
        Args:
            message_data: 包含topic和payload的字典
            
        Returns:
            bool: 是否成功处理消息
        """
        try:
            if "topic" not in message_data or "payload" not in message_data:
                self.logger.error("模拟MQTT消息缺少topic或payload字段")
                return False
                
            topic = message_data["topic"]
            payload = message_data["payload"]
            
            # 转换payload为字符串（如果是字典）
            if isinstance(payload, dict):
                payload = json.dumps(payload)
                
            # 记录模拟消息
            self.logger.info(f"模拟MQTT消息: topic={topic}, payload={payload}")
            
            # 直接调用on_message方法处理消息
            # 创建一个模拟的mqtt消息对象
            class MockMQTTMessage:
                def __init__(self, t, p):
                    self.topic = t
                    self.payload = p.encode('utf-8') if isinstance(p, str) else p
            
            # 创建消息对象并调用处理函数
            mock_msg = MockMQTTMessage(topic, payload)
            self.on_message(None, None, mock_msg)
            
            return True
            
        except Exception as e:
            self.logger.error(f"模拟MQTT消息处理失败: {e}", exc_info=True)
            return False

    def _deduplicate_data_by_timestamp(self, data_list, data_type="数据"):
        """通用的时间戳去重函数

        Args:
            data_list: 数据列表
            data_type: 数据类型名称，用于日志

        Returns:
            list: 去重后的数据列表
        """
        from collections import Counter

        timestamps = [item.get('time') or item.get('timestamp') for item in data_list]
        timestamp_counts = Counter(timestamps)
        duplicates = {ts: count for ts, count in timestamp_counts.items() if count > 1}

        if duplicates:
            self.logger.warning(f"发现重复{data_type}，进行去重处理: {duplicates}")
            unique_data = []
            seen_timestamps = set()

            for item in data_list:
                timestamp = item.get('time') or item.get('timestamp')
                if timestamp not in seen_timestamps:
                    unique_data.append(item)
                    seen_timestamps.add(timestamp)

            self.logger.info(f"{data_type}去重完成: 原始{len(data_list)}条 -> 去重后{len(unique_data)}条")
            return unique_data

        return data_list

    def _check_connection_status(self):
        """检查并同步连接状态"""
        try:
            # 检查客户端是否已初始化
            if not hasattr(self, 'client') or self.client is None:
                self.logger.warning("MQTT客户端未初始化，设置连接状态为断开")
                self.connected = False
                self.connection_state = ConnectionState.DISCONNECTED
                return
                
            # 使用客户端的is_connected方法检查实际连接状态
            if hasattr(self.client, 'is_connected') and callable(self.client.is_connected):
                try:
                    actual_status = self.client.is_connected()
                    
                    if actual_status != self.connected:
                        self.logger.warning(f"检测到连接状态不一致：客户端状态={actual_status}，记录状态={self.connected}，正在同步")
                        
                        # 保存旧状态，用于后续判断
                        old_status = self.connected
                        
                        # 更新连接状态
                        self.connected = actual_status
                        self.connection_state = ConnectionState.CONNECTED if actual_status else ConnectionState.DISCONNECTED
                        
                        # 同步后记录日志
                        self.logger.info(f"连接状态已同步为: {self.connection_state.name}, connected={self.connected}")
                        
                        # 如果状态从未连接变为已连接，处理队列数据（仅当未在重连过程中）
                        if actual_status and not old_status and not self._reconnect_scheduled:
                            self.logger.info("检测到MQTT从断开状态恢复连接，启动队列处理")
                            threading.Thread(target=self._process_queued_data, daemon=True).start()
                        
                        # 如果状态从已连接变为未连接，记录警告
                        if not actual_status and not old_status:
                            self.logger.warning("检测到MQTT连接已断开，数据将缓存在本地")
                    else:
                        # 即使状态一致也记录日志（调试级别）
                        self.logger.debug(f"连接状态检查：客户端状态={actual_status}，记录状态={self.connected}, 状态={self.connection_state.name}")
                except Exception as e:
                    self.logger.error(f"调用is_connected方法时出错: {e}")
                    # 连接状态检查失败，假设已断开
                    if self.connected:
                        self.logger.warning("连接状态检查失败，设置为断开状态")
                        self.connected = False
                        self.connection_state = ConnectionState.DISCONNECTED
            else:
                self.logger.debug("客户端不支持is_connected方法，使用ping测试连接状态")
                # 尝试通过其他方式检查连接状态，例如尝试ping
                try:
                    if hasattr(self.client, '_sock') and self.client._sock:
                        self.connected = True
                        self.connection_state = ConnectionState.CONNECTED
                    else:
                        self.connected = False
                        self.connection_state = ConnectionState.DISCONNECTED
                except:
                    self.connected = False
                    self.connection_state = ConnectionState.DISCONNECTED
        except Exception as e:
            self.logger.error(f"检查连接状态时出错: {e}", exc_info=True)
            # 发生异常时，假设连接已断开
            self.connected = False
            self.connection_state = ConnectionState.DISCONNECTED

    def _get_message_type_from_topic(self, topic):
        """从主题名称判断消息类型
        
        Args:
            topic: 主题名称
            
        Returns:
            str: 消息类型描述
        """
        if "sensor" in topic:
            return "传感器数据"
        elif "co2" in topic:
            return "CO2控制器状态"
        elif "check" in topic:
            return "设备自检数据"
        elif "mosquito" in topic or "mosqutio" in topic: # 注意两种拼写都要检查
            return "蚊子检测数据"
        elif "devopen" in topic:
            return "设备开机"
        elif "devclose" in topic:
            return "设备关机"
        elif "devrestart" in topic:
            return "设备重启"
        elif "swsensor" in topic:
            return "传感器开关"
        else:
            return "未知类型"

    def _mark_co2_status_as_uploaded(self, timestamp):
        """将指定时间戳的CO2状态文件标记为已上传

        Args:
            timestamp: 要标记为已上传的数据时间戳
        """
        try:

            # 获取数据目录
            co2_data_dir = os.path.join(self.data_dir, "co2")
            if not os.path.exists(co2_data_dir):
                return
                
            # 匹配时间戳的文件名可能的模式
            timestamp_str = str(timestamp)
            
            # 获取所有CO2状态文件
            for filename in os.listdir(co2_data_dir):
                if not filename.endswith(".json"):
                    continue
                    
                file_path = os.path.join(co2_data_dir, filename)
                
                # 先尝试从文件名匹配
                if timestamp_str in filename:
                    self._move_file_to_uploaded(co2_data_dir, filename)
                    return
                    
                # 如果文件名不匹配，尝试读取文件内容匹配
                try:
                    with open(file_path, 'r') as f:
                        content = json.load(f)
                        if content.get("time") == timestamp or content.get("timestamp") == timestamp:
                            self._move_file_to_uploaded(co2_data_dir, filename)
                            return
                except Exception as e:
                    self.logger.debug(f"读取文件 {filename} 时出错: {str(e)}")
                    
        except Exception as e:
            self.logger.error(f"标记CO2状态文件时出错: {str(e)}", exc_info=True)

    def _move_file_to_uploaded(self, base_dir, filename):
        """将文件移动到已上传目录

        Args:
            base_dir: 基础目录
            filename: 文件名
        """
        file_path = os.path.join(base_dir, filename)
        uploaded_dir = os.path.join(base_dir, "uploaded")
        
        if not os.path.exists(uploaded_dir):
            os.makedirs(uploaded_dir, exist_ok=True)
            
        # 创建目标文件路径
        target_path = os.path.join(uploaded_dir, filename)
        
        # 如果目标已存在，添加时间戳避免冲突
        if os.path.exists(target_path):
            timestamp = int(time.time())
            base, ext = os.path.splitext(filename)
            target_path = os.path.join(uploaded_dir, f"{base}_{timestamp}{ext}")
            
        # 移动文件
        try:
            shutil.move(file_path, target_path)
            self.logger.debug(f"CO2状态文件已标记为已上传: {filename}")
        except Exception as e:
            self.logger.warning(f"标记CO2状态文件为已上传时出错: {str(e)}")

    def report_power_on_status(self):
        """设备主动上报开机状态"""
        if not self.connected or not self.device_id:
            self.logger.warning("MQTT未连接或设备ID未设置，无法上报开机状态")
            return False
            
        try:
            # 更新设备状态
            self.device_status["power_status"] = 1  # 1表示开机
            
            # 准备消息数据
            message_data = {
                "data": {
                    "status": 1  # 1表示开机
                },
                "time": int(time.time()),
                "devid": self.device_id,
                "dir": "up",
                "ver": self.mqtt_config.get('version', '1.0')
            }
            
            # 使用上报主题而非回复主题
            topic = self.mqtt_config['devopen_topic'].format(device_id=self.device_id)
            
            # 生成消息ID用于跟踪
            message_id = f"report_power_on_{int(time.time())}_{random.randint(1000, 9999)}"
            self._mark_message_sent(message_id)
            
            # 将数据转换为JSON字符串
            payload = json.dumps(message_data)
            
            # 等待消息上传间隔
            self._wait_for_message_interval()

            # 发布消息
            result = self.client.publish(topic, payload, qos=1)

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                return True
            else:
                self.logger.error(f"上报设备开机状态失败，错误码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"上报设备开机状态时发生异常: {e}", exc_info=True)
            return False

    def report_power_off_status(self):
        """设备主动上报关机状态"""
        if not self.connected or not self.device_id:
            self.logger.warning("MQTT未连接或设备ID未设置，无法上报关机状态")
            return False
            
        try:
            # 更新设备状态
            self.device_status["power_status"] = 2  # 2表示关机
            
            # 准备消息数据
            message_data = {
                "data": {
                    "status": 2  # 2表示关机
                },
                "time": int(time.time()),
                "devid": self.device_id,
                "dir": "up",
                "ver": self.mqtt_config.get('version', '1.0')
            }
            
            # 使用上报主题而非回复主题
            topic = self.mqtt_config['devclose_topic'].format(device_id=self.device_id)
            
            # 生成消息ID用于跟踪
            message_id = f"report_power_off_{int(time.time())}_{random.randint(1000, 9999)}"
            self._mark_message_sent(message_id)
            
            # 将数据转换为JSON字符串
            payload = json.dumps(message_data)
            
            # 等待消息上传间隔
            self._wait_for_message_interval()

            # 发布消息
            result = self.client.publish(topic, payload, qos=1)

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"已上报设备关机状态到平台，等待回复确认: topic={topic}")
                return True
            else:
                self.logger.error(f"上报设备关机状态失败，错误码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"上报设备关机状态时发生异常: {e}", exc_info=True)
            return False

    def report_restart_status(self):
        """设备主动上报重启状态"""
        if not self.connected or not self.device_id:
            self.logger.warning("MQTT未连接或设备ID未设置，无法上报重启状态")
            return False
            
        try:
            # 更新设备状态
            self.device_status["power_status"] = 3  # 3表示重启
            
            # 准备消息数据
            message_data = {
                "data": {
                    "status": 3  # 3表示重启
                },
                "time": int(time.time()),
                "devid": self.device_id,
                "dir": "up",
                "ver": self.mqtt_config.get('version', '1.0')
            }
            
            # 使用上报主题而非回复主题
            topic = self.mqtt_config['devrestart_topic'].format(device_id=self.device_id)
            
            # 生成消息ID用于跟踪
            message_id = f"report_restart_{int(time.time())}_{random.randint(1000, 9999)}"
            self._mark_message_sent(message_id)
            
            # 将数据转换为JSON字符串
            payload = json.dumps(message_data)
            
            # 等待消息上传间隔
            self._wait_for_message_interval()

            # 发布消息
            result = self.client.publish(topic, payload, qos=1)

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"已上报设备重启状态到平台，等待回复确认: topic={topic}")
                return True
            else:
                self.logger.error(f"上报设备重启状态失败，错误码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"上报设备重启状态时发生异常: {e}", exc_info=True)
            return False

    def reply_to_power_on_command(self, received_message_id):
        """回复平台的开机命令"""
        if not self.connected or not self.device_id:
            self.logger.warning("MQTT未连接或设备ID未设置，无法回复开机命令")
            return False
            
        try:
            # 准备回复数据
            reply_data = {
                "devid": self.device_id,
                "code": 200,
                "msg": "success",
                "data": {},
                "dir": "up"
            }
            
            # 使用回复主题
            topic = self.mqtt_config['devopen_reply_topic'].format(device_id=self.device_id)
            
            # 将数据转换为JSON字符串
            payload = json.dumps(reply_data)
            
            # 等待消息上传间隔
            self._wait_for_message_interval()

            # 发布消息
            result = self.client.publish(topic, payload, qos=1)

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"已回复平台开机命令: message_id={received_message_id}")
                return True
            else:
                self.logger.error(f"回复平台开机命令失败，错误码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"回复平台开机命令时发生异常: {e}", exc_info=True)
            return False

    def reply_to_power_off_command(self, received_message_id):
        """回复平台的关机命令"""
        if not self.connected or not self.device_id:
            self.logger.warning("MQTT未连接或设备ID未设置，无法回复关机命令")
            return False
            
        try:
            # 准备回复数据
            reply_data = {
                "devid": self.device_id,
                "code": 200,
                "msg": "success",
                "data": {},
                "dir": "up"
            }
            
            # 使用回复主题
            topic = self.mqtt_config['devclose_reply_topic'].format(device_id=self.device_id)
            
            # 将数据转换为JSON字符串
            payload = json.dumps(reply_data)
            
            # 等待消息上传间隔
            self._wait_for_message_interval()

            # 发布消息
            result = self.client.publish(topic, payload, qos=1)

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"已回复平台关机命令: message_id={received_message_id}")
                return True
            else:
                self.logger.error(f"回复平台关机命令失败，错误码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"回复平台关机命令时发生异常: {e}", exc_info=True)
            return False

    def reply_to_restart_command(self, received_message_id):
        """回复平台的重启命令"""
        if not self.connected or not self.device_id:
            self.logger.warning("MQTT未连接或设备ID未设置，无法回复重启命令")
            return False
            
        try:
            # 准备回复数据
            reply_data = {
                "devid": self.device_id,
                "code": 200,
                "msg": "success",
                "data": {},
                "dir": "up"
            }
            
            # 使用回复主题
            topic = self.mqtt_config['devrestart_reply_topic'].format(device_id=self.device_id)
            
            # 将数据转换为JSON字符串
            payload = json.dumps(reply_data)
            
            # 等待消息上传间隔
            self._wait_for_message_interval()

            # 发布消息
            result = self.client.publish(topic, payload, qos=1)

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"已回复平台重启命令: message_id={received_message_id}")
                return True
            else:
                self.logger.error(f"回复平台重启命令失败，错误码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"回复平台重启命令时发生异常: {e}", exc_info=True)
            return False

    def _archive_uploaded_sensor_data_by_timestamp(self, timestamp):
        """归档指定时间戳的数据，队列和文件同步归档，防止重复上传"""
        try:
            # 读取现有传感器数据
            sensor_data = []
            try:
                with open(self.sensor_data_file, 'r', encoding='utf-8') as f:
                    sensor_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                sensor_data = []
            # 找到已上传的数据
            uploaded = [item for item in sensor_data if (item.get('time') == timestamp or item.get('timestamp') == timestamp)]
            pending = [item for item in sensor_data if not (item.get('time') == timestamp or item.get('timestamp') == timestamp)]
            # 读取归档数据
            archive_data = []
            try:
                with open(self.sensor_data_archive_file, 'r', encoding='utf-8') as f:
                    archive_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                archive_data = []
            # 添加到归档
            archive_data.extend(uploaded)
            # 保存归档
            with open(self.sensor_data_archive_file, 'w', encoding='utf-8') as f:
                json.dump(archive_data, f, ensure_ascii=False)
            # 保存未上传数据
            with open(self.sensor_data_file, 'w', encoding='utf-8') as f:
                json.dump(pending, f, ensure_ascii=False)
            self.logger.info(f"已归档传感器数据: 时间戳={timestamp}")
        except Exception as e:
            self.logger.error(f"归档指定传感器数据失败: {e}")

    def enqueue_sensor_data(self, data):
        """将采集到的数据加入上传队列，避免重复时间戳数据入队"""
        timestamp = data.get('time') or data.get('timestamp')
        # 检查队列中是否已存在该时间戳
        for item in list(self.sensor_data_queue.queue):
            if (item.get('time') or item.get('timestamp')) == timestamp:
                self.logger.debug(f"已存在相同时间戳的数据在队列中，跳过入队: {timestamp}")
                return False
        # 检查本地文件中是否已存在该时间戳且已上传
        try:
            if os.path.exists(self.sensor_data_file):
                with open(self.sensor_data_file, 'r', encoding='utf-8') as f:
                    data_list = json.load(f)
                    for item in data_list:
                        if (item.get('time') or item.get('timestamp')) == timestamp and item.get('uploaded', False):
                            self.logger.debug(f"本地文件已存在已上传的数据，跳过入队: {timestamp}")
                            return False
        except Exception as e:
            self.logger.error(f"检查本地文件去重时异常: {e}")
        # 入队
        try:
            self.sensor_data_queue.put_nowait(data)
            self.logger.debug(f"新数据已入队: {timestamp}")
            return True
        except queue.Full:
            self.logger.error("传感器数据队列已满，数据丢失")
            return False

    def _safe_read_json(self, file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            return []

    def publish_mosquito_data_simple(self, data):
        """简化版的蚊子检测数据发布方法，不使用file_id和额外字段
        
        Args:
            data: 蚊子检测数据
            
        Returns:
            bool: 是否成功发送
        """
        # 添加状态检查，确保状态一致
        self._check_connection_status()
        
        if not self.connected:
            return False
            
        try:
            # 确保设备ID存在
            if "devid" not in data or not data["devid"]:
                data["devid"] = self.device_id
            
            # 从配置中获取主题
            if 'mosquito_topic' not in self.mqtt_config:
                self.logger.error("未配置蚊子检测主题")
                return False
                
            topic = self.mqtt_config['mosquito_topic'].format(device_id=data["devid"])
            
            # 将数据转换为JSON字符串
            payload = json.dumps(data)
            
            # 等待消息上传间隔
            self._wait_for_message_interval()

            # 发布消息
            result = self.client.publish(topic, payload, qos=1)

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                return True
            else:
                self.logger.error(f"发布蚊子检测数据失败，错误码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布蚊子检测数据时发生异常: {e}", exc_info=True)
            return False
    
    def handle_mosquito_reply(self, data, device_id):
        """处理蚊子检测数据回复

        Args:
            data: 回复数据
            device_id: 设备ID
        """
        try:
            self.logger.info(f"收到蚊子检测回复(平台确认): {data}")

            # 验证设备ID
            if "devid" not in data or str(data["devid"]) != str(self.device_id):
                self.logger.warning(f"蚊子检测回复设备ID不匹配: 期望={self.device_id}, 实际={data.get('devid')}")
                return False

            # 设备ID匹配，认为上传成功（不检查code字段）
            self.logger.info(f"蚊子检测数据上传成功确认（设备ID匹配）")

            # 记录平台响应信息（仅用于调试，不影响成功判断）
            if "code" in data:
                code = data.get("code", "未知")
                msg = data.get("msg", "无消息")
                self.logger.debug(f"平台响应详情: 代码={code}, 消息={msg}")

            # 简化的通知方法，只基于devid匹配
            self._notify_mosquito_result_simple(True, data)

        except Exception as e:
            self.logger.error(f"处理蚊子检测回复时发生错误: {e}", exc_info=True)
            
    def _notify_mosquito_result_simple(self, success, data):
        """简化版通知蚊子检测结果（只基于devid匹配）
        
        Args:
            success: 是否成功
            data: 回复数据
        """
        try:
            # 获取监控实例
            import builtins
            if hasattr(builtins, 'mosquito_monitor_instance'):
                monitor = getattr(builtins, 'mosquito_monitor_instance')
                # 调用简化的处理结果方法
                monitor.handle_result_simple(success, data)
        except Exception as e:
            self.logger.error(f"通知蚊子检测结果时发生错误: {e}", exc_info=True)

