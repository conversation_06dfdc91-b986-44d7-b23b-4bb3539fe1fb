# 设备签名算法说明

## 概述

设备绑定接口使用 HMAC-SHA256 算法对请求进行签名验证，确保请求的完整性和真实性。

## 签名算法

### 1. 签名数据格式

签名数据按以下格式拼接：
```
{device_unique_id}|{timestamp}|{nonce}|{device_type}
```

### 2. 签名计算步骤

1. **构建签名字符串**：按照上述格式拼接参数
2. **获取设备密钥**：根据设备类型获取对应的签名密钥
3. **计算HMAC-SHA256**：使用密钥对签名字符串进行HMAC-SHA256计算
4. **转换为十六进制**：将计算结果转换为小写十六进制字符串

### 3. 设备类型密钥配置

| 设备类型 | 密钥 |
|----------|------|
| mosquito | MQ2025_7f8e9d6c5b4a3f2e1d0c9b8a7f6e5d4c3b2a1f0e9d8c7b6a5f4e3d2c1b0a9f8e7d6c |

## 代码示例

### Go 语言实现

```go
package main

import (
    "crypto/hmac"
    "crypto/sha256"
    "encoding/hex"
    "fmt"
    "strconv"
    "time"
)

// 计算设备签名
func calculateDeviceSignature(deviceUniqueID, deviceType, timestamp, nonce string) string {
    // 1. 构建签名字符串
    data := fmt.Sprintf("%s|%s|%s|%s", deviceUniqueID, timestamp, nonce, deviceType)
    
    // 2. 获取设备密钥
    secretKey := getDeviceSecretKey(deviceType)
    
    // 3. 计算HMAC-SHA256
    h := hmac.New(sha256.New, []byte(secretKey))
    h.Write([]byte(data))
    
    // 4. 转换为十六进制
    return hex.EncodeToString(h.Sum(nil))
}

// 获取设备密钥
func getDeviceSecretKey(deviceType string) string {
    secretKeys := map[string]string{
        "mosquito": "MQ2025_7f8e9d6c5b4a3f2e1d0c9b8a7f6e5d4c3b2a1f0e9d8c7b6a5f4e3d2c1b0a9f8e7d6c",
    }
    
    if key, exists := secretKeys[deviceType]; exists {
        return key
    }
    return secretKeys["default"]
}

// 示例使用
func main() {
    deviceUniqueID := "DEV2025001"
    deviceType := "mosquito"
    timestamp := strconv.FormatInt(time.Now().Unix(), 10)
    nonce := "nonce_328000"
    
    signature := calculateDeviceSignature(deviceUniqueID, deviceType, timestamp, nonce)
    
    fmt.Printf("设备唯一ID: %s\n", deviceUniqueID)
    fmt.Printf("设备类型: %s\n", deviceType)
    fmt.Printf("时间戳: %s\n", timestamp)
    fmt.Printf("随机数: %s\n", nonce)
    fmt.Printf("签名: %s\n", signature)
}
```

### Python 实现

```python
import hmac
import hashlib
import time
import random
import string

def calculate_device_signature(device_unique_id, device_type, timestamp, nonce):
    """计算设备签名"""
    # 1. 构建签名字符串
    data = f"{device_unique_id}|{timestamp}|{nonce}|{device_type}"
    
    # 2. 获取设备密钥
    secret_key = get_device_secret_key(device_type)
    
    # 3. 计算HMAC-SHA256
    signature = hmac.new(
        secret_key.encode('utf-8'),
        data.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    return signature

def get_device_secret_key(device_type):
    """获取设备密钥"""
    secret_keys = {
        "mosquito": "MQ2025_7f8e9d6c5b4a3f2e1d0c9b8a7f6e5d4c3b2a1f0e9d8c7b6a5f4e3d2c1b0a9f8e7d6c",
    }
    
    return secret_keys.get(device_type, secret_keys["default"])

# 示例使用
if __name__ == "__main__":
    device_unique_id = "DEV2025001"
    device_type = "mosquito"
    timestamp = str(int(time.time()))
    nonce = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
    
    signature = calculate_device_signature(device_unique_id, device_type, timestamp, nonce)
    
    print(f"设备唯一ID: {device_unique_id}")
    print(f"设备类型: {device_type}")
    print(f"时间戳: {timestamp}")
    print(f"随机数: {nonce}")
    print(f"签名: {signature}")
```


## 安全注意事项

1. **密钥管理**：生产环境中密钥应存储在安全的密钥管理系统中
2. **时间戳验证**：服务端会验证时间戳在5分钟内有效
3. **随机数**：每次请求应使用不同的随机数
4. **传输安全**：建议使用HTTPS传输加密请求
5. **密钥轮换**：定期更换签名密钥提高安全性

## 测试示例

### 蚊媒监测器签名示例

```
设备唯一ID: DEV2025001
设备类型: mosquito
时间戳: 1752222285
随机数: nonce_328000
签名数据: DEV2025001|1752222285|nonce_328000|mosquito
密钥: MQ2025_7f8e9d6c5b4a3f2e1d0c9b8a7f6e5d4c3b2a1f0e9d8c7b6a5f4e3d2c1b0a9f8e7d6c
计算结果: 9ea595e6fd73029366824140453b783209ae50f9f4617116acb97a5307fde770
```
