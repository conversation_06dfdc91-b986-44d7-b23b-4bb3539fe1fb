#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
设备绑定测试套件
包含设备绑定功能的完整测试，包括正常流程、异常处理、性能测试等
"""

import os
import sys
import json
import time
import hmac
import hashlib
import random
import string
import logging
import threading
import requests
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from utils.logger import get_logger

# 设置日志
logger = get_logger("binding_test", logging.INFO)

class DeviceBindingTestSuite:
    """设备绑定测试套件"""
    
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
        self.api_key = "test_key_123"
        self.headers = {
            "Content-Type": "application/json",
            "X-API-Key": self.api_key
        }
        
        # 设备类型密钥配置
        self.device_secret_keys = {
            "mosquito": "MQ2025_7f8e9d6c5b4a3f2e1d0c9b8a7f6e5d4c3b2a1f0e9d8c7b6a5f4e3d2c1b0a9f8e7d6c"
        }
        
        # 测试结果统计
        self.test_results = {
            "total": 0,
            "passed": 0,
            "failed": 0,
            "errors": []
        }
    
    def calculate_signature(self, device_unique_id, device_type, timestamp, nonce):
        """计算设备签名"""
        try:
            # 构建签名字符串
            data = f"{device_unique_id}|{timestamp}|{nonce}|{device_type}"
            
            # 获取设备密钥
            secret_key = self.device_secret_keys.get(device_type, "")
            if not secret_key:
                raise ValueError(f"未找到设备类型 {device_type} 的密钥")
            
            # 计算HMAC-SHA256
            signature = hmac.new(
                secret_key.encode('utf-8'),
                data.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            return signature
            
        except Exception as e:
            logger.error(f"计算签名失败: {e}")
            raise
    
    def generate_test_device_data(self, device_type="mosquito"):
        """生成测试设备数据"""
        timestamp = str(int(time.time()))
        nonce = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
        device_unique_id = f"TEST_DEV_{int(time.time())}_{random.randint(1000, 9999)}"
        
        signature = self.calculate_signature(device_unique_id, device_type, timestamp, nonce)
        
        return {
            "device_unique_id": device_unique_id,
            "device_type": device_type,
            "mac_address": f"AA:BB:CC:DD:EE:{random.randint(10, 99):02X}",
            "serial_number": f"SN{timestamp}{random.randint(100, 999)}",
            "timestamp": timestamp,
            "nonce": nonce,
            "signature": signature
        }
    
    def test_normal_binding(self):
        """测试正常设备绑定流程"""
        logger.info("开始测试正常设备绑定流程...")
        
        try:
            # 生成测试设备数据
            device_data = self.generate_test_device_data()
            
            # 发送绑定请求
            response = requests.post(
                f"{self.base_url}/api/v1/device/bind",
                headers=self.headers,
                json=device_data,
                timeout=10
            )
            
            # 验证响应
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0 and "data" in result:
                    logger.info(f"正常绑定测试通过: {device_data['device_unique_id']}")
                    self.test_results["passed"] += 1
                    return True
                else:
                    logger.error(f"绑定响应格式错误: {result}")
                    self.test_results["failed"] += 1
                    return False
            else:
                logger.error(f"绑定请求失败: {response.status_code}, {response.text}")
                self.test_results["failed"] += 1
                return False
                
        except Exception as e:
            logger.error(f"正常绑定测试失败: {e}")
            self.test_results["errors"].append(f"正常绑定测试: {e}")
            self.test_results["failed"] += 1
            return False
        finally:
            self.test_results["total"] += 1
    
    def test_signature_validation(self):
        """测试签名验证"""
        logger.info("开始测试签名验证...")
        
        try:
            # 生成测试设备数据
            device_data = self.generate_test_device_data()
            
            # 故意使用错误的签名
            device_data["signature"] = "wrong_signature_12345"
            
            # 发送绑定请求
            response = requests.post(
                f"{self.base_url}/api/v1/device/bind",
                headers=self.headers,
                json=device_data,
                timeout=10
            )
            
            # 验证响应（应该返回签名错误）
            if response.status_code == 400:
                result = response.json()
                if "签名" in result.get("message", ""):
                    logger.info("签名验证测试通过: 正确拒绝了错误签名")
                    self.test_results["passed"] += 1
                    return True
                else:
                    logger.error(f"签名验证错误消息不正确: {result}")
                    self.test_results["failed"] += 1
                    return False
            else:
                logger.error(f"签名验证测试失败: 应该返回400错误，实际返回{response.status_code}")
                self.test_results["failed"] += 1
                return False
                
        except Exception as e:
            logger.error(f"签名验证测试失败: {e}")
            self.test_results["errors"].append(f"签名验证测试: {e}")
            self.test_results["failed"] += 1
            return False
        finally:
            self.test_results["total"] += 1
    
    def test_duplicate_binding(self):
        """测试重复绑定处理"""
        logger.info("开始测试重复绑定处理...")
        
        try:
            # 生成测试设备数据
            device_data = self.generate_test_device_data()
            
            # 第一次绑定
            response1 = requests.post(
                f"{self.base_url}/api/v1/device/bind",
                headers=self.headers,
                json=device_data,
                timeout=10
            )
            
            if response1.status_code != 200:
                logger.error(f"第一次绑定失败: {response1.status_code}")
                self.test_results["failed"] += 1
                return False
            
            # 第二次绑定（重复）
            # 需要重新计算签名（因为时间戳和nonce需要更新）
            device_data["timestamp"] = str(int(time.time()))
            device_data["nonce"] = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
            device_data["signature"] = self.calculate_signature(
                device_data["device_unique_id"],
                device_data["device_type"],
                device_data["timestamp"],
                device_data["nonce"]
            )
            
            response2 = requests.post(
                f"{self.base_url}/api/v1/device/bind",
                headers=self.headers,
                json=device_data,
                timeout=10
            )
            
            # 验证响应（应该返回设备已绑定错误）
            if response2.status_code == 422:
                result = response2.json()
                if "已绑定" in result.get("message", ""):
                    logger.info("重复绑定测试通过: 正确拒绝了重复绑定")
                    self.test_results["passed"] += 1
                    return True
                else:
                    logger.error(f"重复绑定错误消息不正确: {result}")
                    self.test_results["failed"] += 1
                    return False
            else:
                logger.error(f"重复绑定测试失败: 应该返回422错误，实际返回{response2.status_code}")
                self.test_results["failed"] += 1
                return False
                
        except Exception as e:
            logger.error(f"重复绑定测试失败: {e}")
            self.test_results["errors"].append(f"重复绑定测试: {e}")
            self.test_results["failed"] += 1
            return False
        finally:
            self.test_results["total"] += 1
    
    def test_concurrent_binding(self, concurrent_count=10):
        """测试并发绑定"""
        logger.info(f"开始测试并发绑定: {concurrent_count}个并发请求...")
        
        def single_binding_test():
            """单个绑定测试"""
            try:
                device_data = self.generate_test_device_data()
                response = requests.post(
                    f"{self.base_url}/api/v1/device/bind",
                    headers=self.headers,
                    json=device_data,
                    timeout=10
                )
                return response.status_code == 200
            except Exception as e:
                logger.error(f"并发绑定单个请求失败: {e}")
                return False
        
        try:
            start_time = time.time()
            
            # 使用线程池执行并发请求
            with ThreadPoolExecutor(max_workers=concurrent_count) as executor:
                futures = [executor.submit(single_binding_test) for _ in range(concurrent_count)]
                results = [future.result() for future in as_completed(futures)]
            
            end_time = time.time()
            duration = end_time - start_time
            
            success_count = sum(results)
            success_rate = success_count / concurrent_count * 100
            
            logger.info(f"并发绑定测试完成: {success_count}/{concurrent_count} 成功, "
                       f"成功率: {success_rate:.1f}%, 耗时: {duration:.2f}s")
            
            # 如果成功率超过80%，认为测试通过
            if success_rate >= 80:
                self.test_results["passed"] += 1
                return True
            else:
                logger.error(f"并发绑定成功率过低: {success_rate:.1f}%")
                self.test_results["failed"] += 1
                return False
                
        except Exception as e:
            logger.error(f"并发绑定测试失败: {e}")
            self.test_results["errors"].append(f"并发绑定测试: {e}")
            self.test_results["failed"] += 1
            return False
        finally:
            self.test_results["total"] += 1
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("=" * 60)
        logger.info("开始设备绑定测试套件")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        # 执行所有测试
        tests = [
            ("正常绑定流程测试", self.test_normal_binding),
            ("签名验证测试", self.test_signature_validation),
            ("重复绑定处理测试", self.test_duplicate_binding),
            ("并发绑定测试", lambda: self.test_concurrent_binding(5))
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n执行测试: {test_name}")
            try:
                test_func()
            except Exception as e:
                logger.error(f"测试执行异常: {test_name}, 错误: {e}")
                self.test_results["errors"].append(f"{test_name}: {e}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 输出测试结果
        logger.info("\n" + "=" * 60)
        logger.info("设备绑定测试套件完成")
        logger.info("=" * 60)
        logger.info(f"总测试数: {self.test_results['total']}")
        logger.info(f"通过: {self.test_results['passed']}")
        logger.info(f"失败: {self.test_results['failed']}")
        logger.info(f"总耗时: {duration:.2f}秒")
        
        if self.test_results["errors"]:
            logger.info("\n错误详情:")
            for error in self.test_results["errors"]:
                logger.error(f"  - {error}")
        
        success_rate = self.test_results["passed"] / self.test_results["total"] * 100 if self.test_results["total"] > 0 else 0
        logger.info(f"\n总体成功率: {success_rate:.1f}%")
        
        return success_rate >= 80

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="设备绑定测试套件")
    parser.add_argument("--url", default="http://localhost:8080", help="测试服务器URL")
    parser.add_argument("--concurrent", type=int, default=5, help="并发测试数量")
    
    args = parser.parse_args()
    
    # 创建测试套件
    test_suite = DeviceBindingTestSuite(args.url)
    
    # 运行测试
    success = test_suite.run_all_tests()
    
    # 退出码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
