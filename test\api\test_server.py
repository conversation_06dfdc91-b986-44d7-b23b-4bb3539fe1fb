#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import time
import threading
import logging
import psutil
from datetime import datetime
from flask import Flask, request, jsonify, Response, send_from_directory
from flask_socketio import SocketIO, emit
from flask_cors import CORS
from functools import wraps
import random # Added for massive data injection

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入项目模块
from utils.logger import get_logger

# 初始化Flask应用
app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), 'static'))
CORS(app)  # 启用CORS支持跨域请求
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')  # 使用threading模式避免阻塞

# 设置日志
logger = get_logger("test_server", logging.INFO)

# 全局变量
sensor_system = None  # 传感器系统实例
monitor_thread = None  # 监控线程
stop_monitor = threading.Event()  # 控制监控线程的事件

# API密钥列表
API_KEYS = ["test_key_123", "dev_key_456"]

# 简单的认证装饰器
def require_api_key(f):
    """API密钥验证装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        if api_key and api_key in API_KEYS:
            return f(*args, **kwargs)
        return jsonify({"status": "error", "message": "无效的API密钥"}), 401
    return decorated

# 健康检查接口
@app.route('/api/v1/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    if sensor_system is None:
        return jsonify({"status": "warning", "message": "测试服务器运行中，但传感器系统未连接"})
    return jsonify({"status": "ok", "message": "测试服务器和传感器系统正常运行"})

# 传感器数据注入接口 - 移除限流装饰器
@app.route('/api/v1/inject/sensor', methods=['POST'])
@require_api_key
def inject_sensor_data():
    """注入模拟的传感器数据"""
    start_time = time.time()
    
    if not request.is_json:
        return jsonify({"status": "error", "message": "请求必须是JSON格式"}), 400
        
    data = request.json
    
    # 验证传感器系统是否可用
    if not sensor_system or not sensor_system.sensor_collector:
        return jsonify({"status": "error", "message": "传感器收集器未初始化"}), 500
    
    try:
        # 将模拟数据注入到传感器收集器，强制接受所有数据
        result = sensor_system.sensor_collector.inject_test_data(data, skip_validation=True)
        
        # 记录API调用性能
        if hasattr(sensor_system.mqtt_client, 'api_single_stats'):
            process_time = (time.time() - start_time) * 1000  # 转换为毫秒
            stats = sensor_system.mqtt_client.api_single_stats
            stats['count'] += 1
            stats['total_time'] += process_time
            stats['avg_time'] = stats['total_time'] / stats['count']
        
        return jsonify({
            "status": "success", 
            "message": "传感器数据注入成功",
            "timestamp": int(time.time()),
            "process_time_ms": round((time.time() - start_time) * 1000, 2)
        })
            
    except Exception as e:
        logger.error(f"传感器数据注入失败: {e}", exc_info=True)
        return jsonify({
            "status": "error", 
            "message": f"传感器数据注入失败: {str(e)}"
        }), 500

# 批量传感器数据注入接口 - 移除限流装饰器和数据量限制
@app.route('/api/v1/inject/sensor/batch', methods=['POST'])
@require_api_key
def inject_sensor_data_batch():
    """批量注入模拟的传感器数据"""
    start_time = time.time()
    
    if not request.is_json:
        return jsonify({"status": "error", "message": "请求必须是JSON格式"}), 400
        
    data_batch = request.json
    
    # 验证数据格式
    if not isinstance(data_batch, list):
        return jsonify({"status": "error", "message": "批量数据必须是数组格式"}), 400
        
    # 验证传感器系统是否可用
    if not sensor_system or not sensor_system.sensor_collector:
        return jsonify({"status": "error", "message": "传感器收集器未初始化"}), 500
    
    results = {
        "total": len(data_batch),
        "success": 0,
        "failed": 0,
        "failures": []
    }
    
    try:
        for i, data in enumerate(data_batch):
            try:
                # 将模拟数据注入到传感器收集器，强制接受所有数据
                result = sensor_system.sensor_collector.inject_test_data(data, skip_validation=True)
                results["success"] += 1
            except Exception as e:
                results["failed"] += 1
                results["failures"].append({
                    "index": i,
                    "reason": str(e)
                })
                
        # 记录API调用性能
        if hasattr(sensor_system.mqtt_client, 'api_batch_stats'):
            process_time = (time.time() - start_time) * 1000  # 转换为毫秒
            stats = sensor_system.mqtt_client.api_batch_stats
            stats['count'] += 1
            stats['total_time'] += process_time
            stats['avg_time'] = stats['total_time'] / stats['count']
                
        return jsonify({
            "status": "success", 
            "message": f"批量传感器数据注入完成，共 {len(data_batch)} 条数据",
            "results": results,
            "timestamp": int(time.time()),
            "process_time_ms": round((time.time() - start_time) * 1000, 2)
        })
            
    except Exception as e:
        logger.error(f"批量传感器数据注入失败: {e}", exc_info=True)
        return jsonify({
            "status": "error", 
            "message": f"批量传感器数据注入失败: {str(e)}"
        }), 500

# MQTT消息模拟接口 - 移除限流装饰器
@app.route('/api/v1/inject/mqtt', methods=['POST'])
@require_api_key
def inject_mqtt_message():
    """注入模拟的MQTT消息"""
    if not request.is_json:
        return jsonify({"status": "error", "message": "请求必须是JSON格式"}), 400
        
    message_data = request.json
    
    # 验证传感器系统是否可用
    if not sensor_system or not sensor_system.mqtt_client:
        return jsonify({"status": "error", "message": "MQTT客户端未初始化"}), 500
    
    try:
        # 模拟MQTT消息处理
        result = sensor_system.mqtt_client.simulate_message(message_data)
        
        if result:
            return jsonify({
                "status": "success", 
                "message": "MQTT消息模拟成功",
                "timestamp": int(time.time())
            })
        else:
            return jsonify({
                "status": "error", 
                "message": "MQTT消息模拟失败"
            }), 400
            
    except Exception as e:
        logger.error(f"MQTT消息模拟失败: {e}", exc_info=True)
        return jsonify({
            "status": "error", 
            "message": f"MQTT消息模拟失败: {str(e)}"
        }), 500

# 硬件故障模拟接口 - 移除限流装饰器
@app.route('/api/v1/inject/hardware_failure', methods=['POST'])
@require_api_key
def inject_hardware_failure():
    """模拟硬件故障"""
    if not request.is_json:
        return jsonify({"status": "error", "message": "请求必须是JSON格式"}), 400
        
    failure_data = request.json
    
    # 验证数据格式
    if 'component' not in failure_data or 'status' not in failure_data:
        return jsonify({
            "status": "error", 
            "message": "请求必须包含component和status字段"
        }), 400
    
    # 验证传感器系统是否可用
    if not sensor_system or not sensor_system.device_health:
        return jsonify({"status": "error", "message": "设备自检系统未初始化"}), 500
    
    try:
        # 模拟硬件故障
        component = failure_data['component']
        status = failure_data['status']
        result = sensor_system.device_health.simulate_hardware_failure(component, status)
        
        if result:
            return jsonify({
                "status": "success", 
                "message": f"硬件故障模拟成功: {component}",
                "timestamp": int(time.time())
            })
        else:
            return jsonify({
                "status": "error", 
                "message": f"硬件故障模拟失败: {component}"
            }), 400
            
    except Exception as e:
        logger.error(f"硬件故障模拟失败: {e}", exc_info=True)
        return jsonify({
            "status": "error", 
            "message": f"硬件故障模拟失败: {str(e)}"
        }), 500

# 系统资源监控接口
@app.route('/api/v1/monitor/resources', methods=['GET'])
@require_api_key
def get_system_resources():
    """获取系统资源使用情况"""
    try:
        # 获取CPU使用率
        cpu_percent = psutil.cpu_percent(interval=0.5)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        
        # 获取内存使用情况
        memory = psutil.virtual_memory()
        
        # 获取磁盘使用情况
        disk = psutil.disk_usage('/')
        
        # 获取网络IO统计
        net_io = psutil.net_io_counters()
        
        # 获取系统启动时间
        boot_time = datetime.fromtimestamp(psutil.boot_time()).strftime("%Y-%m-%d %H:%M:%S")
        
        # 获取进程信息
        process = psutil.Process(os.getpid())
        process_info = {
            "cpu_percent": process.cpu_percent(interval=0.1),
            "memory_percent": process.memory_percent(),
            "threads": process.num_threads(),
            "create_time": datetime.fromtimestamp(process.create_time()).strftime("%Y-%m-%d %H:%M:%S")
        }
        
        resource_data = {
            "status": "success",
            "timestamp": int(time.time()),
            "data": {
                "cpu": {
                    "percent": cpu_percent,
                    "count": cpu_count,
                    "freq_current": cpu_freq.current if cpu_freq else None,
                    "freq_max": cpu_freq.max if cpu_freq else None
                },
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "percent": memory.percent
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": disk.percent
                },
                "network": {
                    "bytes_sent": net_io.bytes_sent,
                    "bytes_recv": net_io.bytes_recv,
                    "packets_sent": net_io.packets_sent,
                    "packets_recv": net_io.packets_recv
                },
                "system": {
                    "boot_time": boot_time
                },
                "process": process_info
            }
        }
        
        return jsonify(resource_data)
            
    except Exception as e:
        logger.error(f"获取系统资源信息失败: {e}", exc_info=True)
        return jsonify({
            "status": "error", 
            "message": f"获取系统资源信息失败: {str(e)}"
        }), 500

# 线程管理器监控接口
@app.route('/api/v1/monitor/threads', methods=['GET'])
@require_api_key
def get_thread_stats():
    """获取线程管理器统计信息"""
    try:
        if sensor_system and hasattr(sensor_system, 'thread_manager') and sensor_system.thread_manager:
            thread_manager = sensor_system.thread_manager

            # 获取线程统计信息
            stats = thread_manager.get_stats()
            thread_info = thread_manager.get_thread_info()

            response_data = {
                "status": "success",
                "data": {
                    "stats": stats,
                    "threads": thread_info,
                    "manager_running": thread_manager.running,
                    "scheduled_tasks": len(thread_manager.scheduled_tasks) if hasattr(thread_manager, 'scheduled_tasks') else 0
                },
                "timestamp": int(time.time() * 1000)
            }
        else:
            response_data = {
                "status": "warning",
                "message": "线程管理器未初始化或不可用",
                "data": {
                    "stats": {
                        "tasks_submitted": 0,
                        "tasks_completed": 0,
                        "tasks_failed": 0,
                        "active_threads": 0,
                        "total_threads": 0
                    },
                    "threads": [],
                    "manager_running": False,
                    "scheduled_tasks": 0
                },
                "timestamp": int(time.time() * 1000)
            }

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"获取线程统计失败: {e}")
        return jsonify({
            "status": "error",
            "message": f"获取线程统计失败: {str(e)}",
            "timestamp": int(time.time() * 1000)
        }), 500

# MQTT连接模式管理接口
@app.route('/api/v1/mqtt/mode', methods=['GET', 'POST'])
@require_api_key
def mqtt_mode():
    """获取或设置MQTT连接模式"""
    if not sensor_system or not sensor_system.mqtt_client:
        return jsonify({"status": "error", "message": "MQTT客户端未初始化"}), 500
    
    mqtt_client = sensor_system.mqtt_client
    
    if request.method == 'GET':
        return jsonify({
            "status": "success",
            "data": {
                "enable_mqtt_connection": mqtt_client.enable_mqtt_connection,
                "connection_state": mqtt_client.connection_state.name,
                "connected": mqtt_client.connected,
                "device_id": mqtt_client.device_id
            }
        })
    
    if request.method == 'POST':
        if not request.is_json:
            return jsonify({"status": "error", "message": "请求必须是JSON格式"}), 400
            
        data = request.json
        enable_mqtt = data.get('enable_mqtt_connection', True)
        
        try:
            # 使用新版的set_connection_mode方法，只传入enable_mqtt参数
            mqtt_client.set_connection_mode(enable_mqtt)
            
            return jsonify({
                "status": "success",
                "message": f"MQTT连接模式已设置为: {'启用' if enable_mqtt else '禁用'}",
                "data": {
                    "enable_mqtt_connection": mqtt_client.enable_mqtt_connection,
                    "connection_state": mqtt_client.connection_state.name,
                    "connected": mqtt_client.connected
                }
            })
        except Exception as e:
            logger.error(f"设置MQTT连接模式失败: {e}", exc_info=True)
            return jsonify({
                "status": "error",
                "message": f"设置MQTT连接模式失败: {str(e)}"
            }), 500

# MQTT凭证状态查询接口
@app.route('/api/v1/mqtt/credential_status', methods=['GET'])
@require_api_key
def get_mqtt_credential_status():
    """获取MQTT凭证的状态"""
    if not sensor_system or not sensor_system.mqtt_client:
        return jsonify({"status": "error", "message": "MQTT客户端未初始化"}), 500
    
    mqtt_client = sensor_system.mqtt_client
    
    try:
        # 获取凭证状态
        credentials_file = os.path.join(mqtt_client.data_dir, 
                                       mqtt_client.storage_config.get('credentials_file', 'credentials.json'))
        
        credentials_exist = os.path.exists(credentials_file)
        
        # 获取凭证过期信息
        credentials_expiry = None
        expiry_days = None
        
        if credentials_exist:
            try:
                with open(credentials_file, 'r', encoding='utf-8') as f:
                    credentials = json.load(f)
                
                if 'timestamp' in credentials:
                    # 计算凭证已存在的天数
                    timestamp = credentials.get('timestamp', 0)
                    current_time = int(time.time())
                    days_passed = (current_time - timestamp) / (24 * 3600)
                    
                    # 获取凭证过期天数
                    credential_expiry_days = mqtt_client.mqtt_config.get('credential_expiry_days', 30)
                    days_remaining = credential_expiry_days - days_passed
                    
                    credentials_expiry = {
                        "timestamp": timestamp,
                        "age_days": round(days_passed, 2),
                        "expiry_days": credential_expiry_days,
                        "days_remaining": round(days_remaining, 2),
                        "expired": days_passed >= credential_expiry_days
                    }
            except Exception as e:
                logger.error(f"读取凭证文件失败: {e}")
        
        # 构建状态响应
        credential_status = {
            "credentials_exist": credentials_exist,
            "expiry_info": credentials_expiry,
            "mqtt_connected": mqtt_client.connected,
            "connection_state": mqtt_client.connection_state.name,
            "device_id": mqtt_client.device_id,
            "retry_info": {
                "credential_retry_count": mqtt_client.credential_retry_count,
                "credential_max_retry_attempts": mqtt_client.credential_max_retry_attempts
            }
        }
        
        return jsonify({
            "status": "success",
            "data": credential_status
        })
    
    except Exception as e:
        logger.error(f"获取MQTT凭证状态失败: {e}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"获取MQTT凭证状态失败: {str(e)}"
        }), 500

# MQTT凭证请求触发接口
@app.route('/api/v1/mqtt/request_credentials', methods=['POST'])
@require_api_key
def request_mqtt_credentials():
    """手动触发MQTT凭证获取过程"""
    if not sensor_system or not sensor_system.mqtt_client:
        return jsonify({"status": "error", "message": "MQTT客户端未初始化"}), 500
    
    mqtt_client = sensor_system.mqtt_client
    
    try:
        # 验证请求参数
        force = False
        if request.is_json:
            data = request.json
            force = data.get('force', False)
        
        # 检查凭证文件是否存在
        credentials_file = os.path.join(mqtt_client.data_dir, 
                                       mqtt_client.storage_config.get('credentials_file', 'credentials.json'))
        credentials_exist = os.path.exists(credentials_file)
        
        # 检查是否需要强制更新
        if credentials_exist and not force:
            return jsonify({
                "status": "warning",
                "message": "凭证文件已存在，未执行更新。如需强制更新，请设置force=true"
            })
        
        # 在后台线程中执行凭证请求，避免阻塞API响应
        def request_credentials_thread():
            try:
                # 重置重试计数器
                mqtt_client.credential_retry_count = 0
                
                # 尝试删除现有凭证文件（如果强制更新）
                if force and credentials_exist:
                    try:
                        os.remove(credentials_file)
                        logger.info(f"已删除现有凭证文件: {credentials_file}")
                    except Exception as e:
                        logger.error(f"删除凭证文件失败: {e}")
                
                # 执行凭证请求
                result = mqtt_client._request_device_binding()
                
                if result:
                    logger.info("手动触发的MQTT凭证获取成功")
                else:
                    logger.warning("手动触发的MQTT凭证获取未成功完成，可能已进入重试队列")
            except Exception as e:
                logger.error(f"手动触发MQTT凭证获取失败: {e}", exc_info=True)
        
        # 启动后台线程
        threading.Thread(target=request_credentials_thread, daemon=True).start()
        
        return jsonify({
            "status": "success",
            "message": "MQTT凭证获取请求已触发，请稍后通过状态接口检查结果"
        })
    
    except Exception as e:
        logger.error(f"触发MQTT凭证获取请求失败: {e}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"触发MQTT凭证获取请求失败: {str(e)}"
        }), 500

# 队列状态监控接口
@app.route('/api/v1/monitor/queues', methods=['GET'])
@require_api_key
def get_queue_status():
    """获取队列状态"""
    if not sensor_system or not sensor_system.mqtt_client:
        return jsonify({"status": "error", "message": "MQTT客户端未初始化"}), 500
        
    try:
        mqtt_client = sensor_system.mqtt_client
        
        queue_status = {
            "sensor_data_queue": {
                "size": mqtt_client.sensor_data_queue.qsize(),
                "maxsize": mqtt_client.sensor_data_queue.maxsize
            },
            "error_data_queue": {
                "size": mqtt_client.error_data_queue.qsize(),
                "maxsize": mqtt_client.error_data_queue.maxsize
            },
            "co2_status_queue": {
                "size": mqtt_client.co2_status_queue.qsize(),
                "maxsize": mqtt_client.co2_status_queue.maxsize
            },
            "check_data_queue": {
                "size": mqtt_client.check_data_queue.qsize(),
                "maxsize": mqtt_client.check_data_queue.maxsize
            }
        }
        
        return jsonify({
            "status": "success",
            "timestamp": int(time.time()),
            "data": queue_status
        })
            
    except Exception as e:
        logger.error(f"获取队列状态失败: {e}", exc_info=True)
        return jsonify({
            "status": "error", 
            "message": f"获取队列状态失败: {str(e)}"
        }), 500

# 组件状态监控接口
@app.route('/api/v1/monitor/components', methods=['GET'])
@require_api_key
def get_component_status():
    """获取组件状态"""
    if not sensor_system:
        return jsonify({"status": "error", "message": "传感器系统未初始化"}), 500
        
    try:
        # 获取MQTT客户端组件就绪状态
        mqtt_components_ready = {}
        if sensor_system.mqtt_client:
            mqtt_components_ready = sensor_system.mqtt_client.components_ready
            
        # 获取传感器状态
        sensor_status = {}
        if sensor_system.sensor_collector:
            sensor_status = sensor_system.sensor_collector.get_sensor_status()
            
        # 获取CO2控制器状态
        co2_status = "unknown"
        if sensor_system.co2_controller:
            co2_status = "running" if sensor_system.co2_controller.running else "stopped"
            
        # 获取设备自检状态
        device_health_status = "unknown"
        if sensor_system.device_health:
            device_health_status = "running" if sensor_system.device_health.running else "stopped"
            
        return jsonify({
            "status": "success",
            "timestamp": int(time.time()),
            "data": {
                "mqtt_client": {
                    "status": "connected" if sensor_system.mqtt_client and sensor_system.mqtt_client.connected else "disconnected",
                    "components_ready": mqtt_components_ready
                },
                "sensor_collector": {
                    "status": "running" if sensor_system.sensor_collector and sensor_system.sensor_collector.running else "stopped",
                    "sensors": sensor_status
                },
                "co2_controller": {
                    "status": co2_status
                },
                "device_health": {
                    "status": device_health_status
                }
            }
        })
            
    except Exception as e:
        logger.error(f"获取组件状态失败: {e}", exc_info=True)
        return jsonify({
            "status": "error", 
            "message": f"获取组件状态失败: {str(e)}"
        }), 500

# 日志流式输出接口
@app.route('/api/v1/monitor/logs', methods=['GET'])
@require_api_key
def stream_logs():
    """流式输出最近的日志"""
    def generate():
        log_file = os.path.join('logs', 'sensor_system.log')
        
        if not os.path.exists(log_file):
            yield json.dumps({"status": "error", "message": "日志文件不存在"}) + "\n"
            return
            
        # 获取文件大小
        file_size = os.path.getsize(log_file)
        
        # 如果文件太大，只读取最后10KB
        position = max(0, file_size - 10240)
        
        with open(log_file, 'r') as f:
            f.seek(position)
            # 丢弃第一行，因为可能是不完整的
            if position > 0:
                f.readline()
                
            # 读取最后的日志行
            last_lines = f.readlines()
            
            # 发送最近的日志
            for line in last_lines:
                yield json.dumps({"log": line.strip()}) + "\n"
                
            # 继续监视文件
            while True:
                line = f.readline()
                if line:
                    yield json.dumps({"log": line.strip()}) + "\n"
                else:
                    time.sleep(1)
                    
    return Response(generate(), mimetype='text/event-stream')

# 性能测试报告生成接口
@app.route('/api/v1/report/performance', methods=['GET'])
@require_api_key
def generate_performance_report():
    """生成性能测试报告"""
    try:
        # 收集系统性能数据
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # 收集队列状态
        queue_status = {}
        if sensor_system and sensor_system.mqtt_client:
            mqtt_client = sensor_system.mqtt_client
            queue_status = {
                "sensor_data_queue": mqtt_client.sensor_data_queue.qsize(),
                "error_data_queue": mqtt_client.error_data_queue.qsize(),
                "co2_status_queue": mqtt_client.co2_status_queue.qsize(),
                "check_data_queue": mqtt_client.check_data_queue.qsize()
            }
            
        # 生成报告
        report = {
            "timestamp": int(time.time()),
            "datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "system_resources": {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "disk_percent": disk.percent
            },
            "queue_status": queue_status
        }
        
        return jsonify({
            "status": "success",
            "report": report
        })
            
    except Exception as e:
        logger.error(f"生成性能测试报告失败: {e}", exc_info=True)
        return jsonify({
            "status": "error", 
            "message": f"生成性能测试报告失败: {str(e)}"
        }), 500

# 添加超大批量数据注入接口，用于压力测试
@app.route('/api/v1/inject/sensor/massive', methods=['POST'])
@require_api_key
def inject_massive_sensor_data():
    """注入超大批量的传感器数据用于压力测试"""
    start_time = time.time()
    
    if not request.is_json:
        return jsonify({"status": "error", "message": "请求必须是JSON格式"}), 400
        
    params = request.json
    
    # 验证参数
    if not isinstance(params, dict):
        return jsonify({"status": "error", "message": "请求必须是字典格式"}), 400
        
    # 获取参数
    count = params.get('count', 1000)  # 默认生成1000条数据
    template = params.get('template', {})  # 数据模板
    interval = params.get('interval', 0)  # 数据注入间隔，0表示尽可能快
    
    # 验证传感器系统是否可用
    if not sensor_system or not sensor_system.sensor_collector:
        return jsonify({"status": "error", "message": "传感器收集器未初始化"}), 500
    
    # 启动后台线程进行数据注入
    def inject_data_thread():
        logger.info(f"开始注入超大批量数据: {count}条")
        success = 0
        failed = 0
        
        thread_start_time = time.time()
        for i in range(count):
            try:
                # 基于模板生成数据
                data = template.copy()
                
                # 添加时间戳和随机变化
                data['timestamp'] = int(time.time())
                data['datetime'] = time.strftime('%Y-%m-%d %H:%M:%S')
                
                # 如果模板中有温度，添加随机变化
                if 'temperature' in template:
                    data['temperature'] = template['temperature'] + random.uniform(-1, 1)
                    
                # 如果模板中有湿度，添加随机变化
                if 'humidity' in template:
                    data['humidity'] = template['humidity'] + random.uniform(-2, 2)
                    
                # 如果模板中有CO2，添加随机变化
                if 'co2' in template:
                    data['co2'] = template['co2'] + random.randint(-50, 50)
                
                # 注入数据，跳过验证
                sensor_system.sensor_collector.inject_test_data(data, skip_validation=True)
                success += 1
                
                # 按指定间隔暂停
                if interval > 0:
                    time.sleep(interval)
                    
            except Exception as e:
                logger.error(f"注入第{i+1}条数据失败: {e}")
                failed += 1
                
        end_time = time.time()
        duration = end_time - thread_start_time
        rate = count / duration if duration > 0 else 0
        
        # 记录API调用性能
        if hasattr(sensor_system.mqtt_client, 'api_massive_stats'):
            stats = sensor_system.mqtt_client.api_massive_stats
            stats['count'] += 1
            stats['total_items'] += count
            stats['rate'] = rate
        
        logger.info(f"超大批量数据注入完成: 成功={success}, 失败={failed}, 用时={duration:.2f}秒, 速率={rate:.2f}条/秒")
    
    # 启动后台线程
    threading.Thread(target=inject_data_thread, daemon=True).start()
    
    response_time = (time.time() - start_time) * 1000  # 毫秒
    
    return jsonify({
        "status": "success", 
        "message": f"已开始注入 {count} 条数据，请通过日志和监控接口查看进度",
        "timestamp": int(time.time()),
        "request_process_time_ms": round(response_time, 2)
    })

# 静态文件服务
@app.route('/')
def index():
    """提供监控页面"""
    static_dir = os.path.join(os.path.dirname(__file__), 'static')
    return send_from_directory(static_dir, 'monitor.html')

# WebSocket连接事件
@socketio.on('connect')
def handle_connect():
    """处理WebSocket连接"""
    logger.info("WebSocket客户端已连接")
    emit('response', {'data': '已连接到系统资源监控'})

# WebSocket断开连接事件
@socketio.on('disconnect')
def handle_disconnect():
    """处理WebSocket断开连接"""
    logger.info("WebSocket客户端已断开连接")

def monitor_resources_task():
    """系统资源监控任务"""
    logger.info("系统资源监控任务已启动")
    
    # 性能指标跟踪
    performance_metrics = {
        "sensor": {
            "processed_total": 0,
            "success_count": 0,
            "total_count": 0,
            "process_time": 0,
            "process_rate": 0,
            "last_processed": 0,
            "last_time": time.time()
        },
        "error": {
            "processed_total": 0,
            "process_time": 0,
            "process_rate": 0,
            "last_processed": 0,
            "last_time": time.time()
        },
        "api": {
            "single": {"count": 0, "total_time": 0, "avg_time": 0},
            "batch": {"count": 0, "total_time": 0, "avg_time": 0},
            "massive": {"count": 0, "total_items": 0, "rate": 0}
        }
    }
    
    while not stop_monitor.is_set():
        try:
            # 获取CPU使用率
            cpu_percent = psutil.cpu_percent(interval=0.5)
            
            # 获取内存使用情况
            memory = psutil.virtual_memory()
            
            # 获取磁盘使用情况
            disk = psutil.disk_usage('/')
            
            # 获取网络IO统计
            net_io = psutil.net_io_counters()
            
            # 获取进程信息
            process_info = psutil.Process(os.getpid())
            
            # 尝试获取主板温度（如果可用）
            board_temp = None
            try:
                # 首先尝试使用tegrastats命令获取温度（针对Jetson设备）
                try:
                    import subprocess
                    import re
                    import signal
                    
                    # 使用Popen代替check_output，这样可以控制读取一行后终止进程
                    process = subprocess.Popen("tegrastats", 
                                             shell=True, 
                                             stdout=subprocess.PIPE,
                                             stderr=subprocess.PIPE,
                                             universal_newlines=True)
                    
                    # 只读取一行输出
                    tegrastats_output = process.stdout.readline()
                    
                    # 终止进程
                    process.terminate()
                    try:
                        # 等待进程结束，最多等待1秒
                        process.wait(timeout=1)
                    except subprocess.TimeoutExpired:
                        # 如果等待超时，发送SIGKILL强制终止
                        process.kill()
                    
                    # 解析温度数据
                    if "cpu@" in tegrastats_output:
                        # 使用更宽松的正则表达式匹配温度
                        cpu_temp_match = re.search(r'cpu@\s*(\d+\.?\d*)', tegrastats_output)
                        if cpu_temp_match:
                            board_temp = float(cpu_temp_match.group(1))
                    
                    # 如果没有找到CPU温度，尝试其他温度数据
                    if board_temp is None and "soc" in tegrastats_output:
                        soc_temp_match = re.search(r'soc\d+@\s*(\d+\.?\d*)', tegrastats_output)
                        if soc_temp_match:
                            board_temp = float(soc_temp_match.group(1))
                    
                    # 如果仍然没有找到，尝试GPU温度
                    if board_temp is None and "gpu@" in tegrastats_output:
                        gpu_temp_match = re.search(r'gpu@\s*(\d+\.?\d*)', tegrastats_output)
                        if gpu_temp_match:
                            board_temp = float(gpu_temp_match.group(1))
                except (subprocess.SubprocessError, ValueError, FileNotFoundError) as e:
                    logger.warning(f"无法使用tegrastats获取温度: {e}")
                
                # 如果tegrastats失败，尝试从系统温度文件读取
                if board_temp is None:
                    try:
                        # Jetson特有的温度文件路径
                        thermal_zones = [
                            "/sys/devices/virtual/thermal/thermal_zone0/temp",
                            "/sys/devices/virtual/thermal/thermal_zone1/temp",
                            "/sys/devices/virtual/thermal/thermal_zone2/temp"
                        ]
                        
                        for zone in thermal_zones:
                            if os.path.exists(zone):
                                with open(zone, 'r') as f:
                                    # 温度通常以毫摄氏度存储
                                    temp = int(f.read().strip()) / 1000.0
                                    if temp > 0 and temp < 150:  # 合理范围检查
                                        board_temp = temp
                                        break
                    except Exception as e:
                        logger.warning(f"无法从系统文件读取温度: {e}")
                
                # 如果以上方法都失败，尝试使用传统方法
                if board_temp is None:
                    try:
                        import sensors
                        sensors.init()
                        for chip in sensors.iter_detected_chips():
                            if 'coretemp' in str(chip):
                                for feature in chip:
                                    if 'Core' in feature.label or 'Package' in feature.label:
                                        board_temp = feature.get_value()
                                        break
                                if board_temp is not None:
                                    break
                        if board_temp is None and len(list(sensors.iter_detected_chips())) > 0:
                            # 如果没有找到特定的温度传感器，使用第一个可用的温度传感器
                            chip = list(sensors.iter_detected_chips())[0]
                            for feature in chip:
                                if hasattr(feature, 'get_value'):
                                    board_temp = feature.get_value()
                                    break
                        sensors.cleanup()
                    except:
                        # 如果无法获取传感器数据，尝试使用psutil
                        try:
                            temps = psutil.sensors_temperatures()
                            if temps:
                                for name, entries in temps.items():
                                    if entries:
                                        board_temp = entries[0].current
                                        break
                        except:
                            board_temp = None
            except Exception as e:
                logger.error(f"获取温度数据失败: {str(e)}")
                board_temp = None
                
            # 初始化全局Process对象以获取准确的进程CPU使用率
            if not hasattr(monitor_resources_task, '_process'):
                monitor_resources_task._process = process_info
                # 首次调用会返回0，调用一次但忽略结果
                monitor_resources_task._process.cpu_percent()
                # 使用小的非零值而不是0，避免显示为--
                process_cpu = 0.1
            else:
                try:
                    process_cpu = monitor_resources_task._process.cpu_percent()
                    # 确保数值有效
                    if process_cpu <= 0:
                        process_cpu = 0.1
                except Exception as e:
                    logger.warning(f"获取进程CPU使用率失败: {e}")
                    process_cpu = 0.1
            
            # 更新传感器数据处理性能指标
            if sensor_system and sensor_system.mqtt_client:
                current_time = time.time()
                mqtt_client = sensor_system.mqtt_client
                
                # 获取处理计数
                sensor_processed = getattr(mqtt_client, 'sensor_data_processed_count', 0)
                error_processed = getattr(mqtt_client, 'error_data_processed_count', 0)
                
                # 传感器数据处理率
                time_diff = current_time - performance_metrics["sensor"]["last_time"]
                if time_diff >= 1.0:
                    new_processed = sensor_processed - performance_metrics["sensor"]["last_processed"]
                    performance_metrics["sensor"]["process_rate"] = new_processed / time_diff
                    performance_metrics["sensor"]["last_processed"] = sensor_processed
                    performance_metrics["sensor"]["last_time"] = current_time
                
                # 错误数据处理率
                time_diff = current_time - performance_metrics["error"]["last_time"]
                if time_diff >= 1.0:
                    new_processed = error_processed - performance_metrics["error"]["last_processed"]
                    performance_metrics["error"]["process_rate"] = new_processed / time_diff
                    performance_metrics["error"]["last_processed"] = error_processed
                    performance_metrics["error"]["last_time"] = current_time
                
                # 更新总处理数
                performance_metrics["sensor"]["processed_total"] = sensor_processed
                performance_metrics["error"]["processed_total"] = error_processed
                
                # 更新成功率统计 - 使用接口注入的数据统计
                performance_metrics["sensor"]["success_count"] = getattr(mqtt_client, 'sensor_data_success_count', 0)
                performance_metrics["sensor"]["total_count"] = getattr(mqtt_client, 'sensor_data_total_count', 0)
                
                # 处理时间
                performance_metrics["sensor"]["process_time"] = getattr(mqtt_client, 'sensor_data_process_time', 0)
                performance_metrics["error"]["process_time"] = getattr(mqtt_client, 'error_data_process_time', 0)
                
                # API调用统计
                performance_metrics["api"]["single"] = getattr(mqtt_client, 'api_single_stats', {"count": 0, "total_time": 0, "avg_time": 0})
                performance_metrics["api"]["batch"] = getattr(mqtt_client, 'api_batch_stats', {"count": 0, "total_time": 0, "avg_time": 0})
                performance_metrics["api"]["massive"] = getattr(mqtt_client, 'api_massive_stats', {"count": 0, "total_items": 0, "rate": 0})
            
            resource_data = {
                "timestamp": int(time.time()),
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "disk_percent": disk.percent,
                "network": {
                    "bytes_sent": net_io.bytes_sent,
                    "bytes_recv": net_io.bytes_recv
                },
                "process": {
                    "cpu_percent": process_cpu,
                    "memory_percent": process_info.memory_percent()
                }
            }
            
            # 如果获取到了主板温度，添加到数据中
            if board_temp is not None:
                resource_data["board_temp"] = board_temp
            
            # 发送数据到所有连接的客户端
            socketio.emit('resource_update', resource_data)
            
            # 队列状态数据
            if sensor_system and sensor_system.mqtt_client:
                mqtt_client = sensor_system.mqtt_client
                queue_status = {
                    "sensor_data_queue": {
                        "size": mqtt_client.sensor_data_queue.qsize(),
                        "maxsize": mqtt_client.sensor_data_queue.maxsize
                    },
                    "error_data_queue": {
                        "size": mqtt_client.error_data_queue.qsize(),
                        "maxsize": mqtt_client.error_data_queue.maxsize
                    },
                    "performance": performance_metrics
                }
                socketio.emit('queue_update', queue_status)
                
        except Exception as e:
            logger.error(f"监控资源时出错: {str(e)}", exc_info=True)
            
        # 休眠一段时间
        time.sleep(2)
    
    logger.info("系统资源监控任务已停止")

def start_monitor_thread():
    """启动监控线程"""
    global monitor_thread, stop_monitor
    if monitor_thread is None or not monitor_thread.is_alive():
        stop_monitor.clear()
        monitor_thread = threading.Thread(target=monitor_resources_task)
        monitor_thread.daemon = True  # 设置为守护线程
        monitor_thread.start()
        logger.info("系统资源监控线程已启动")

def stop_monitor_thread():
    """停止监控线程"""
    global monitor_thread, stop_monitor
    if monitor_thread and monitor_thread.is_alive():
        stop_monitor.set()
        monitor_thread.join(timeout=5)
        logger.info("系统资源监控线程已停止")

# 测试服务器入口函数
def run_test_server(host='0.0.0.0', port=5000, system=None):
    """运行测试服务器
    
    Args:
        host: 主机地址
        port: 端口号
        system: 传感器系统实例
    """
    global sensor_system
    sensor_system = system
    
    logger.info(f"测试服务器启动中，监听地址: {host}:{port}")
    
    # 启动监控线程
    start_monitor_thread()
    
    try:
        # 使用调试日志输出请求信息
        @app.before_request
        def log_request_info():
            logger.debug(f"收到请求: {request.method} {request.path}")
        
        @app.after_request
        def log_response_info(response):
            logger.debug(f"发送响应: {response.status}")
            return response
            
        socketio.run(app, host=host, port=port, debug=False, use_reloader=False, allow_unsafe_werkzeug=True)
    except Exception as e:
        logger.error(f"测试服务器启动失败: {e}", exc_info=True)
    finally:
        # 停止监控线程
        stop_monitor_thread()
        logger.info("测试服务器已关闭")
    
# 如果直接运行此脚本，则启动测试服务器
if __name__ == '__main__':
    run_test_server() 