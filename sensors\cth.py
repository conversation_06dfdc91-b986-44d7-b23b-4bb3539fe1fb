import serial
import struct
import time
from utils.config_loader import load_config

class CO2TempHumiditySensor:
    def __init__(self, port=None, baudrate=None, timeout=None):
        # 如果未提供参数，则从配置文件加载
        if port is None or baudrate is None or timeout is None:
            config = load_config()
            serial_config = config['serial']
            port = port or serial_config['port']
            baudrate = baudrate or serial_config['baudrate']
            timeout = timeout or serial_config['timeout']
            
        self.ser = serial.Serial(port, baudrate, timeout=timeout)
        if not self.ser.is_open:
            self.ser.open()
        time.sleep(0.1)  # 等待串口初始化

    def calculate_crc(self, data):
        crc = 0xFFFF
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x0001:
                    crc >>= 1
                    crc ^= 0xA001
                else:
                    crc >>= 1
        return crc

    def read_sensor_data(self, device_address=None):
        # 如果未提供设备地址，则从配置文件加载
        if device_address is None:
            config = load_config()
            device_address = config['sensors']['cth']['device_address']
            
        # 构造请求帧：读取温湿度和CO2值
        request_frame = bytearray([
            device_address,  # 地址码
            0x03,            # 功能码（读取寄存器数据）
            0x00, 0x00,      # 寄存器起始地址（0x0000）
            0x00, 0x03,      # 寄存器数量（3个寄存器）
        ])
        
        # 计算并添加CRC
        crc = self.calculate_crc(request_frame)
        request_frame.append(crc & 0xFF)      # CRC低字节
        request_frame.append((crc >> 8) & 0xFF)  # CRC高字节
        
        # 发送请求
        self.ser.flushInput()  # 清空输入缓冲区
        self.ser.write(request_frame)
        
        # 读取响应 (11字节：1+1+1+2+2+2+2)
        response = self.ser.read(11)
        
        if len(response) != 11:
            print(f"响应长度错误: 期望11字节, 实际{len(response)}字节")
            return None
        
        # 验证CRC
        received_crc = struct.unpack('<H', response[-2:])[0]
        calculated_crc = self.calculate_crc(response[:-2])
        
        if received_crc != calculated_crc:
            print(f"CRC校验失败: 接收{received_crc:04X}, 计算{calculated_crc:04X}")
            return None
        
        # 解析数据
        humidity = struct.unpack('>H', response[3:5])[0] / 10.0  # 湿度值，单位 %RH
        temperature = struct.unpack('>h', response[5:7])[0] / 10.0  # 温度值，单位 ℃
        co2 = struct.unpack('>H', response[7:9])[0]  # CO2 浓度值，单位 ppm
        
        return {
            'humidity': humidity,
            'temperature': temperature,
            'co2': co2
        }

    def close(self):
        self.ser.close()

def try_read_sensor_data():
    config = load_config()
    port = config['serial']['port']
    try:
        print(f"尝试连接串口: {port}")
        sensor = CO2TempHumiditySensor()
        data = sensor.read_sensor_data()
        if data:
            print(f"湿度: {data['humidity']} %RH")
            print(f"温度: {data['temperature']} ℃")
            print(f"CO2 浓度: {data['co2']} ppm")
        sensor.close()
    except Exception as e:
        print(f"连接失败: {e}")

if __name__ == "__main__":
    try_read_sensor_data()