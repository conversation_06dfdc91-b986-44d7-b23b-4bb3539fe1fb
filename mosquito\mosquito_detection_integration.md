# 蚊子检测数据与MQTT上传系统集成文档

## 1. 集成概述

本文档描述了将外部蚊子检测系统生成的JSON数据集成到我们的MQTT上传系统的详细方案。通过此集成，我们可以确保检测数据能够可靠地上传至云平台，同时保持设备标识一致性。

## 2. 系统架构

```
外部蚊子检测系统 ──(JSON文件)──> 共享目录 ──(文件监控)──> 我方MQTT上传系统 ──(MQTT)──> 云平台
```

## 3. 文件接口规范

### 3.1 共享目录结构

```
/home/<USER>/shared_data/
├── incoming/           # 放置新生成的JSON文件的目录
├── processed/          # 成功处理和上传的文件
├── failed/             # 处理失败的文件
└── logs/               # 处理日志
```

**重要提示**：请确保您的系统仅向`incoming`目录写入文件，不要修改其他目录中的文件。

### 3.2 JSON文件命名格式

请使用以下格式命名JSON文件：
```
mosquito_detection_{TIMESTAMP}.json
```

其中`{TIMESTAMP}`为生成时间戳

示例：`mosquito_detection_20240104175615.json`

### 3.3 JSON数据格式

以下是JSON数据格式的参考示例：

```json
{
    "devid": "",  // 设备ID，请留空，由我方系统填充
    "ver": "1.0",  // 协议版本
    "data": {
        "time": 1704380175,  // 检测时间（秒级Unix时间戳）
        "picurl": "https://example.com/xxxxx.jpg",  // 图片URL
        "lr": [  // 检测结果列表
            {
                "type": "Albopictus",  // 蚊子类型
                "count": 1,            // 数量
                "idnt": 80             // 识别置信度
            }
            // 可以有多个检测结果
        ]
    }
}
```

**注意事项**：
- 此JSON格式为参考示例，具体字段可根据实际需求调整
- `devid`字段可以留空，我方系统会自动填充正确的设备ID
- `time`应为检测发生的时间，使用秒级Unix时间戳
- JSON文件应包含完整有效的JSON数据

## 4. 集成步骤（外部系统开发者）

### 4.1 目录准备

请确保以下共享目录已存在：
```bash
mkdir -p /home/<USER>/shared_data/incoming
```

### 4.2 权限设置

确保共享目录具有正确的权限：
```bash
chmod 755 /home/<USER>/shared_data
chmod 755 /home/<USER>/shared_data/incoming
```

### 4.3 数据写入流程

1. 生成JSON数据
2. 创建临时文件（带`.tmp`后缀）
3. 写入数据到临时文件
4. 使用原子操作重命名为最终文件名
5. 不要尝试修改已写入的文件

示例代码流程（伪代码）：
```
data = generate_detection_data()
timestamp = current_timestamp_as_string()
filename = f"mosquito_detection_{timestamp}.json"
temp_path = f"/home/<USER>/shared_data/incoming/{filename}.tmp"
final_path = f"/home/<USER>/shared_data/incoming/{filename}"

write_json_to_file(data, temp_path)
rename_file(temp_path, final_path)
```

## 5. MQTT通信规范

### 5.1 主题

- **发布主题**：`xcc/attr/mosqutio/${deviceID}`
- **回复主题**：`/xxc/once/mosqutio/${deviceID}/reply`

### 5.2 处理流程

1. 我方系统监控`incoming`目录中的新JSON文件
2. 读取并验证JSON格式
3. 通过MQTT发布到指定主题
4. 等待回复主题中的确认消息
5. 根据确认结果将文件移至`processed`或`failed`目录

### 5.3 上传确认

我方系统会通过以下方式确认上传是否成功：
- 监听回复主题获取服务器响应
- 验证回复中的`devid`与发送的设备ID匹配
- 验证回复中的状态码（如`code=200`）
- 如果10秒内未收到回复，视为上传失败

## 6. 故障排查

### 6.1 文件未被处理

如果您的文件放入`incoming`目录后未被处理，请检查：
- 文件名格式是否正确
- JSON格式是否有效
- 文件权限是否正确（应为644）
- 查看`logs`目录中的日志文件

### 6.2 处理失败

如果文件被移动到`failed`目录，可能的原因包括：
- JSON格式不符合规范
- 网络连接问题导致MQTT上传失败
- 服务器拒绝请求（查看失败文件的错误后缀）

## 7. 注意事项

- **不要删除处理中的文件**：一旦文件放入`incoming`目录，请不要修改或删除
- **避免重复数据**：确保每个检测事件只生成一个文件
- **文件大小**：单个JSON文件不应超过1MB
- **处理频率**：我方系统会实时监控新文件，通常在1秒内开始处理
- **设备ID**：不需要在JSON中设置正确的`devid`，我方系统会自动填充



## 附录：完整JSON示例

```json
{
    "devid": "",
    "ver": "1.0",
    "data": {
        "time": 1704380175,
        "picurl": "https://example.com/mosquito_image_20250104175615.jpg",
        "lr": [
            {
                "type": "Albopictus", 
                "count": 1,
                "idnt": 80 
            },
            {
                "type": "Culex", 
                "count": 2,
                "idnt": 75 
            }
        ]
    }
}
```

---

本文档版本：v1.0  
更新日期：2023年7月5日 