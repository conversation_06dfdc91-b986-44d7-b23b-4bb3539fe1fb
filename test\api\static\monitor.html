<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>传感器系统性能监控</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .card-header {
            background-color: #6c757d;
            color: white;
            font-weight: bold;
        }
        .resource-value {
            font-size: 24px;
            font-weight: bold;
        }
        .chart-container {
            position: relative;
            height: 250px;
            width: 100%;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-connected {
            background-color: #28a745;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
        .queue-bar {
            height: 20px;
            background-color: #007bff;
            border-radius: 3px;
        }
        .fixed-bottom-right {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        #notification {
            display: none;
            padding: 15px;
            border-radius: 5px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .metric-card {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            background-color: #e9ecef;
        }
        .metric-title {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .metric-value {
            font-size: 1.2rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <h1>传感器系统性能监控</h1>
                <div>
                    <span class="status-indicator" id="connection-status"></span>
                    <span id="connection-text">连接中...</span>
                </div>
            </div>
            <div id="notification" class="mt-2"></div>
        </header>

        <div class="row">
            <!-- CPU和内存使用率卡片 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">系统资源监控</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="resourceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 实时资源数据卡片 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">实时资源数据</div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 mb-3">
                                <div class="text-muted">CPU使用率</div>
                                <div class="resource-value" id="cpu-percent">--</div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-muted">内存使用率</div>
                                <div class="resource-value" id="memory-percent">--</div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-muted">主板温度</div>
                                <div class="resource-value" id="board-temp">--</div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-muted">进程CPU使用率</div>
                                <div class="resource-value" id="process-cpu">--</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据处理性能卡片 -->
        <div class="card">
            <div class="card-header">数据处理性能</div>
            <div class="card-body">
                <div class="row">
                    <!-- 传感器数据处理 -->
                    <div class="col-md-6">
                        <h5>传感器数据处理</h5>
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <div class="metric-card">
                                    <div class="metric-title">待处理数据</div>
                                    <div class="metric-value" id="sensor-queue-pending">0</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <div class="metric-card">
                                    <div class="metric-title">已处理总数</div>
                                    <div class="metric-value" id="sensor-processed-total">0</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <div class="metric-card">
                                    <div class="metric-title">处理速率 (条/秒)</div>
                                    <div class="metric-value" id="sensor-process-rate">0</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <div class="metric-card">
                                    <div class="metric-title">批处理耗时 (ms)</div>
                                    <div class="metric-value" id="sensor-process-time">0</div>
                                </div>
                            </div>
                        </div>
                        <label>处理进度</label>
                        <div class="progress mb-2">
                            <div class="progress-bar" id="sensor-queue-progress" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small id="sensor-queue-size">0</small>
                            <small>最大容量: <span id="sensor-queue-maxsize">0</span></small>
                        </div>
                        <label class="mt-3">成功率</label>
                        <div class="progress mb-2">
                            <div class="progress-bar bg-success" id="sensor-success-rate-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small><span id="sensor-success-rate">0</span>%</small>
                            <small>成功: <span id="sensor-success-count">0</span> / 总计: <span id="sensor-total-count">0</span></small>
                        </div>
                    </div>
                    
                    <!-- 错误数据处理 -->
                    <div class="col-md-6">
                        <h5>错误数据处理</h5>
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <div class="metric-card">
                                    <div class="metric-title">待处理错误</div>
                                    <div class="metric-value" id="error-queue-pending">0</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <div class="metric-card">
                                    <div class="metric-title">已处理总数</div>
                                    <div class="metric-value" id="error-processed-total">0</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <div class="metric-card">
                                    <div class="metric-title">处理速率 (条/秒)</div>
                                    <div class="metric-value" id="error-process-rate">0</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <div class="metric-card">
                                    <div class="metric-title">处理耗时 (ms)</div>
                                    <div class="metric-value" id="error-process-time">0</div>
                                </div>
                            </div>
                        </div>
                        <label>处理进度</label>
                        <div class="progress mb-2">
                            <div class="progress-bar bg-warning" id="error-queue-progress" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small id="error-queue-size">0</small>
                            <small>最大容量: <span id="error-queue-maxsize">0</span></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API性能指标卡片 -->
        <div class="card">
            <div class="card-header">API性能指标</div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label>单条数据注入 (/api/v1/inject/sensor)</label>
                        <div class="metric-card">
                            <div class="d-flex justify-content-between mb-2">
                                <div>总调用次数:</div>
                                <div id="api-single-count">0</div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <div>平均响应时间:</div>
                                <div id="api-single-time">0 ms</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label>批量数据注入 (/api/v1/inject/sensor/batch)</label>
                        <div class="metric-card">
                            <div class="d-flex justify-content-between mb-2">
                                <div>总调用次数:</div>
                                <div id="api-batch-count">0</div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <div>平均响应时间:</div>
                                <div id="api-batch-time">0 ms</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label>大量数据注入 (/api/v1/inject/sensor/massive)</label>
                        <div class="metric-card">
                            <div class="d-flex justify-content-between mb-2">
                                <div>总调用次数:</div>
                                <div id="api-massive-count">0</div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <div>处理速率:</div>
                                <div id="api-massive-rate">0 条/秒</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据处理历史图表 -->
        <div class="card">
            <div class="card-header">数据处理历史</div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="processRateChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="fixed-bottom-right">
        <button class="btn btn-primary" id="toggle-btn" onclick="toggleMonitor()">悬浮窗口</button>
    </div>

    <!-- 悬浮监控窗口 -->
    <div class="card" id="floating-monitor" style="display: none; position: fixed; right: 20px; bottom: 70px; width: 250px; z-index: 1000;">
        <div class="card-header">
            资源监控
            <button type="button" class="btn-close float-end" onclick="hideFloatingMonitor()"></button>
        </div>
        <div class="card-body p-2">
            <div class="mb-2">
                <label>CPU: <span id="float-cpu">--</span>%</label>
                <div class="progress">
                    <div class="progress-bar" id="float-cpu-bar" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
            <div class="mb-2">
                <label>内存: <span id="float-memory">--</span>%</label>
                <div class="progress">
                    <div class="progress-bar bg-info" id="float-memory-bar" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
            <div class="mb-2">
                <label>处理速率: <span id="float-process-rate">--</span> 条/秒</label>
            </div>
        </div>
    </div>

    <script>
        // 图表初始化
        const ctx1 = document.getElementById('resourceChart').getContext('2d');
        const ctx2 = document.getElementById('processRateChart').getContext('2d');
        
        // 最多保存60个数据点（2分钟）
        const maxDataPoints = 60;
        const initialData = Array(maxDataPoints).fill(0);
        const labels = Array(maxDataPoints).fill('');
        
        // 资源使用率图表
        const resourceChart = new Chart(ctx1, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'CPU使用率',
                        data: [...initialData],
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: '内存使用率',
                        data: [...initialData],
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: '使用率 (%)'
                        }
                    }
                },
                animation: {
                    duration: 0
                }
            }
        });
        
        // 数据处理速率图表
        const processRateChart = new Chart(ctx2, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: '传感器数据处理速率',
                        data: [...initialData],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: '错误数据处理速率',
                        data: [...initialData],
                        borderColor: 'rgb(153, 102, 255)',
                        backgroundColor: 'rgba(153, 102, 255, 0.2)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '处理速率 (条/秒)'
                        }
                    }
                },
                animation: {
                    duration: 0
                }
            }
        });
        
        // 性能指标计数器
        let performanceMetrics = {
            sensorProcessed: 0,
            sensorSuccessCount: 0,
            sensorTotalCount: 0,
            sensorProcessTime: 0,
            sensorLastBatchSize: 0,
            sensorBatchStartTime: 0,
            errorProcessed: 0,
            errorProcessTime: 0,
            apiSingleCount: 0,
            apiSingleTime: 0,
            apiBatchCount: 0,
            apiBatchTime: 0,
            apiMassiveCount: 0,
            apiMassiveRate: 0
        };
        
        // WebSocket连接
        const socket = io();
        let connected = false;
        
        // 连接建立时
        socket.on('connect', function() {
            connected = true;
            document.getElementById('connection-status').className = 'status-indicator status-connected';
            document.getElementById('connection-text').textContent = '已连接';
            showNotification('已连接到服务器，开始接收实时数据');
        });
        
        // 连接断开时
        socket.on('disconnect', function() {
            connected = false;
            document.getElementById('connection-status').className = 'status-indicator status-disconnected';
            document.getElementById('connection-text').textContent = '已断开';
            showNotification('与服务器的连接已断开，请刷新页面重试', 'danger');
        });
        
        // 初始响应
        socket.on('response', function(data) {
            console.log('收到服务器响应:', data);
        });
        
        // 更新资源信息
        socket.on('resource_update', function(data) {
            try {
                // 安全更新元素辅助函数
                function safeUpdateElement(id, value) {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = value;
                    }
                }
                
                function safeUpdateWidth(id, percent) {
                    const element = document.getElementById(id);
                    if (element) {
                        element.style.width = percent + '%';
                    }
                }
                
                // 更新图表数据
                const timestamp = new Date(data.timestamp * 1000);
                const timeStr = timestamp.getHours().toString().padStart(2, '0') + ':' + 
                            timestamp.getMinutes().toString().padStart(2, '0') + ':' + 
                            timestamp.getSeconds().toString().padStart(2, '0');
                
                // 确保图表存在再更新
                if (resourceChart && resourceChart.data && resourceChart.data.datasets) {
                    // 更新CPU和内存图表
                    if (resourceChart.data.datasets[0] && resourceChart.data.datasets[1]) {
                        resourceChart.data.datasets[0].data.push(data.cpu_percent);
                        resourceChart.data.datasets[0].data.shift();
                        resourceChart.data.datasets[1].data.push(data.memory_percent);
                        resourceChart.data.datasets[1].data.shift();
                        resourceChart.data.labels.push(timeStr);
                        resourceChart.data.labels.shift();
                        resourceChart.update();
                    }
                }
                
                // 更新数值显示
                safeUpdateElement('cpu-percent', data.cpu_percent.toFixed(1) + '%');
                safeUpdateElement('memory-percent', data.memory_percent.toFixed(1) + '%');
                safeUpdateElement('disk-percent', data.disk_percent.toFixed(1) + '%');
                safeUpdateElement('process-cpu', data.process.cpu_percent.toFixed(1) + '%');
                
                // 如果有主板温度数据则更新
                if (data.board_temp !== undefined) {
                    safeUpdateElement('board-temp', data.board_temp.toFixed(1) + '°C');
                }
                
                // 更新悬浮窗口
                safeUpdateElement('float-cpu', data.cpu_percent.toFixed(1));
                safeUpdateWidth('float-cpu-bar', data.cpu_percent);
                safeUpdateElement('float-memory', data.memory_percent.toFixed(1));
                safeUpdateWidth('float-memory-bar', data.memory_percent);
                
                // 根据使用率设置颜色
                const cpuBar = document.getElementById('float-cpu-bar');
                if (cpuBar) {
                    setCpuBarColor('float-cpu-bar', data.cpu_percent);
                }
                
                const memBar = document.getElementById('float-memory-bar');
                if (memBar) {
                    setMemoryBarColor('float-memory-bar', data.memory_percent);
                }
            } catch (error) {
                console.error('更新资源信息时出错:', error);
            }
        });
        
        // 更新队列状态
        socket.on('queue_update', function(data) {
            try {
                // 传感器数据队列
                const sensorQueue = data.sensor_data_queue;
                const sensorPercent = (sensorQueue.size / sensorQueue.maxsize) * 100;
                
                // 安全更新元素
                function safeUpdateElement(id, value) {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = value;
                    }
                }
                
                function safeUpdateWidth(id, percent) {
                    const element = document.getElementById(id);
                    if (element) {
                        element.style.width = percent + '%';
                    }
                }
                
                // 更新传感器数据队列UI
                safeUpdateElement('sensor-queue-pending', sensorQueue.size);
                safeUpdateWidth('sensor-queue-progress', sensorPercent);
                safeUpdateElement('sensor-queue-size', sensorQueue.size);
                safeUpdateElement('sensor-queue-maxsize', sensorQueue.maxsize);
                
                // 错误数据队列
                const errorQueue = data.error_data_queue;
                const errorPercent = (errorQueue.size / errorQueue.maxsize) * 100;
                
                // 更新错误数据队列UI
                safeUpdateElement('error-queue-pending', errorQueue.size);
                safeUpdateWidth('error-queue-progress', errorPercent);
                safeUpdateElement('error-queue-size', errorQueue.size);
                safeUpdateElement('error-queue-maxsize', errorQueue.maxsize);
                
                // 如果有处理性能数据则更新
                if (data.performance) {
                    // 更新处理图表
                    let updateChart = false;
                    
                    // 传感器数据处理性能
                    if (data.performance.sensor) {
                        const sensorPerf = data.performance.sensor;
                        safeUpdateElement('sensor-processed-total', sensorPerf.processed_total);
                        safeUpdateElement('sensor-process-rate', sensorPerf.process_rate.toFixed(2));
                        safeUpdateElement('sensor-process-time', sensorPerf.process_time.toFixed(0));
                        
                        safeUpdateElement('sensor-success-count', sensorPerf.success_count);
                        safeUpdateElement('sensor-total-count', sensorPerf.total_count);
                        
                        const successRate = sensorPerf.total_count > 0 ? 
                            (sensorPerf.success_count / sensorPerf.total_count * 100) : 0;
                        safeUpdateElement('sensor-success-rate', successRate.toFixed(1));
                        safeUpdateWidth('sensor-success-rate-bar', successRate);
                        
                        // 更新悬浮窗口
                        safeUpdateElement('float-process-rate', sensorPerf.process_rate.toFixed(2));
                        
                        // 更新处理速率图表
                        if (processRateChart && processRateChart.data && processRateChart.data.datasets && processRateChart.data.datasets[0]) {
                            processRateChart.data.datasets[0].data.push(sensorPerf.process_rate);
                            processRateChart.data.datasets[0].data.shift();
                            updateChart = true;
                        }
                    }
                    
                    // 错误数据处理性能
                    if (data.performance.error) {
                        const errorPerf = data.performance.error;
                        safeUpdateElement('error-processed-total', errorPerf.processed_total);
                        safeUpdateElement('error-process-rate', errorPerf.process_rate.toFixed(2));
                        safeUpdateElement('error-process-time', errorPerf.process_time.toFixed(0));
                        
                        // 更新处理速率图表
                        if (processRateChart && processRateChart.data && processRateChart.data.datasets && processRateChart.data.datasets[1]) {
                            processRateChart.data.datasets[1].data.push(errorPerf.process_rate);
                            processRateChart.data.datasets[1].data.shift();
                            updateChart = true;
                        }
                    }
                    
                    // 更新图表标签和显示
                    if (updateChart && processRateChart && processRateChart.data && processRateChart.data.labels) {
                        processRateChart.data.labels.push(new Date().toTimeString().substr(0, 8));
                        processRateChart.data.labels.shift();
                        processRateChart.update();
                    }
                    
                    // API性能数据
                    if (data.performance.api) {
                        const apiPerf = data.performance.api;
                        if (apiPerf.single) {
                            safeUpdateElement('api-single-count', apiPerf.single.count);
                            safeUpdateElement('api-single-time', apiPerf.single.avg_time.toFixed(2) + ' ms');
                        }
                        if (apiPerf.batch) {
                            safeUpdateElement('api-batch-count', apiPerf.batch.count);
                            safeUpdateElement('api-batch-time', apiPerf.batch.avg_time.toFixed(2) + ' ms');
                        }
                        if (apiPerf.massive) {
                            safeUpdateElement('api-massive-count', apiPerf.massive.count);
                            safeUpdateElement('api-massive-rate', apiPerf.massive.rate.toFixed(2) + ' 条/秒');
                        }
                    }
                }
            } catch (error) {
                console.error('更新队列状态时出错:', error);
            }
        });
        
        // 设置CPU进度条颜色
        function setCpuBarColor(id, percent) {
            const element = document.getElementById(id);
            if (percent < 70) {
                element.className = 'progress-bar';
            } else if (percent < 90) {
                element.className = 'progress-bar bg-warning';
            } else {
                element.className = 'progress-bar bg-danger';
            }
        }
        
        // 设置内存进度条颜色
        function setMemoryBarColor(id, percent) {
            const element = document.getElementById(id);
            if (percent < 70) {
                element.className = 'progress-bar bg-info';
            } else if (percent < 90) {
                element.className = 'progress-bar bg-warning';
            } else {
                element.className = 'progress-bar bg-danger';
            }
        }
        
        // 显示通知
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.style.display = 'block';
            
            if (type === 'success') {
                notification.style.backgroundColor = '#d4edda';
                notification.style.borderColor = '#c3e6cb';
                notification.style.color = '#155724';
            } else if (type === 'danger') {
                notification.style.backgroundColor = '#f8d7da';
                notification.style.borderColor = '#f5c6cb';
                notification.style.color = '#721c24';
            }
            
            setTimeout(() => {
                notification.style.display = 'none';
            }, 5000);
        }
        
        // 显示/隐藏悬浮窗口
        function toggleMonitor() {
            const floatingMonitor = document.getElementById('floating-monitor');
            if (floatingMonitor.style.display === 'none') {
                floatingMonitor.style.display = 'block';
                document.getElementById('toggle-btn').textContent = '隐藏悬浮窗';
            } else {
                hideFloatingMonitor();
            }
        }
        
        function hideFloatingMonitor() {
            document.getElementById('floating-monitor').style.display = 'none';
            document.getElementById('toggle-btn').textContent = '悬浮窗口';
        }
    </script>
</body>
</html> 