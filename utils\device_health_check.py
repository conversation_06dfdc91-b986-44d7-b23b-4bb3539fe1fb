#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import json
import threading
import subprocess
import logging
import glob
from datetime import datetime
import psutil
import Jetson.GPIO as GPIO
import smbus
import shutil

# 不在此处清理GPIO，避免"GPIO模式未设置"的警告
# GPIO的设置和清理由主程序统一管理

from utils.logger import get_logger
from utils.config_loader import load_config

class DeviceHealthCheck:
    """设备自检系统，用于检查各种硬件和系统状态"""
    
    # 状态常量
    STATUS_NORMAL = 0  # 正常
    STATUS_ERROR = 1   # 异常
    
    def __init__(self):
        """初始化设备自检系统"""
        # 初始化日志
        self.logger = get_logger("device_health")
        self.logger.info("初始化设备自检系统...")
        
        # 加载配置
        self.config = load_config()
        self.storage_config = self.config['data_storage']
        self.mqtt_config = self.config['mqtt']
        self.device_health_config = self.config.get('device_health', {})
        
        # 设备ID初始化为空字符串，将在需要时从MQTT客户端获取
        self.device_id = ""
        
        # 数据目录设置
        self.data_dir = os.path.join(self.storage_config['data_dir'])
        self.check_dir = os.path.join(self.data_dir, self.storage_config.get('check_dir', 'check'))

        # 确保基础目录存在
        if not os.path.exists(self.check_dir):
            os.makedirs(self.check_dir)
            self.logger.info(f"创建目录: {self.check_dir}")
        
        # 自检数据现在使用独立文件存储，不再需要数组文件
        # 确保目录存在
        self._ensure_directories_exist()
        
        # 传感器读取锁
        self.sensor_lock = threading.Lock()
        
        # 运行状态
        self.running = False
        self.check_thread = None
        self.check_interval = self.device_health_config.get('check_interval', 1800)  # 从配置读取，默认30分钟
        self.logger.info(f"设备自检间隔设置为: {self.check_interval}秒 ({self.check_interval/60:.1f}分钟)")
        
        # 添加组件状态跟踪
        self.previous_check_data = {}  # 用于存储上一次自检的组件状态
        self.components_in_error = set()  # 跟踪当前处于异常状态的组件

        # 退避策略配置
        self.backoff_config = self.device_health_config.get('backoff', {})
        self.initial_backoff_interval = self.backoff_config.get('initial_interval', 60)
        self.max_backoff_interval = self.backoff_config.get('max_interval', 1800)
        self.backoff_multiplier = self.backoff_config.get('multiplier', 2)

        # 状态检测配置
        self.state_detection_config = self.device_health_config.get('state_detection', {})
        self.same_state_window = self.state_detection_config.get('same_state_window', 300)

        # 组件状态跟踪（用于退避策略）
        self.component_states = {}  # 组件当前状态
        self.component_last_report = {}  # 组件最后上报时间
        self.component_backoff_intervals = {}  # 组件退避间隔
        self.component_consecutive_failures = {}  # 组件连续失败次数

        # 恢复检查线程设置
        self.recovery_thread = None
        self.recovery_check_interval = self.state_detection_config.get('recovery_check_interval', 30)
        
        # 电池电压阈值
        self.battery_min_voltage = self.device_health_config.get('battery_min_voltage', 10.5)
        self.battery_max_voltage = self.device_health_config.get('battery_max_voltage', 12.0)
        self.battery_threshold = self.device_health_config.get('battery_threshold', 20)
        
        # ADS1115配置
        self.i2c_bus = self.device_health_config.get('i2c_bus', 7)  # 默认使用I2C总线7
        self.ads_address = self.device_health_config.get('ads_address', 0x48)  # 默认地址0x48
        self.battery_retry_count = self.device_health_config.get('battery_retry_count', 3)  # 重试次数
        self.battery_retry_delay = self.device_health_config.get('battery_retry_delay', 0.1)  # 重试间隔
        self.i2c = None
        
        # 初始化I2C总线
        try:
            # 使用smbus直接指定总线号
            self.i2c = smbus.SMBus(self.i2c_bus)
            self.logger.info(f"成功打开I2C总线 {self.i2c_bus}，ADS1115地址:0x{self.ads_address:02x}")
        except Exception as e:
            self.logger.error(f"I2C总线初始化失败: {e}")
            self.i2c = None
            
        # 传感器收集器引用
        self.sensor_collector = None
            
        # GPIO模式由主程序统一设置，此处不再设置
        # GPIO.setmode(GPIO.BOARD)
        
        self.logger.info("设备自检系统初始化完成")

    def set_sensor_collector(self, sensor_collector):
        """设置传感器收集器引用
        
        Args:
            sensor_collector: 传感器收集器实例
        """
        self.sensor_collector = sensor_collector
        self.logger.info("已设置传感器收集器引用")
    
    def _ensure_directories_exist(self):
        """确保自检数据目录存在"""
        # 确保pending、uploaded、history目录存在
        pending_dir = os.path.join(self.check_dir, 'pending')
        uploaded_dir = os.path.join(self.check_dir, 'uploaded')
        history_dir = os.path.join(self.check_dir, 'history')

        for directory in [pending_dir, uploaded_dir, history_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                self.logger.info(f"创建目录: {directory}")
    
    def start(self):
        """启动自检系统"""
        if self.running:
            self.logger.warning("设备自检系统已经在运行")
            return
        
        self.running = True
        
        # 启动时立即执行一次全面自检
        self.perform_full_check()
        
        # 启动定期自检线程
        self.check_thread = threading.Thread(target=self._check_loop, daemon=True)
        self.check_thread.start()
        
        # 启动组件恢复检查线程
        self.recovery_thread = threading.Thread(target=self._recovery_check_loop, daemon=True)
        self.recovery_thread.start()
        
        self.logger.info("设备自检系统线程已启动")
        
        # 通知MQTT客户端设备自检系统已就绪
        try:
            import builtins
            if hasattr(builtins, 'mqtt_client_instance'):
                mqtt_client = getattr(builtins, 'mqtt_client_instance')
                mqtt_client.set_component_ready("device_health", True)
        except Exception as e:
            self.logger.error(f"通知MQTT客户端设备自检系统就绪状态失败: {e}")
    
    def stop(self):
        """停止自检系统"""
        self.logger.info("正在停止设备自检系统...")
        self.running = False
        if self.check_thread:
            self.check_thread.join(timeout=2.0)
        
        # 关闭I2C总线资源
        try:
            if hasattr(self, 'i2c') and self.i2c is not None:
                self.i2c.close()
                self.logger.info("I2C总线资源已释放")
        except Exception as e:
            self.logger.error(f"关闭I2C总线资源失败: {e}")
            
        self.logger.info("设备自检系统已停止")
        
        # 通知MQTT客户端设备自检系统已停止
        try:
            import builtins
            if hasattr(builtins, 'mqtt_client_instance'):
                mqtt_client = getattr(builtins, 'mqtt_client_instance')
                mqtt_client.set_component_ready("device_health", False)
        except Exception as e:
            self.logger.error(f"通知MQTT客户端设备自检系统停止状态失败: {e}")
    
    def _check_loop(self):
        """自检循环，定期执行全面自检"""
        last_check_time = int(time.time())
        
        while self.running:
            try:
                current_time = int(time.time())
                
                # 检查是否到达自检间隔，只在定时自检时更新时间戳
                # 中途调用handle_device_error触发的自检不会重置此计时器
                time_since_last_check = current_time - last_check_time
                if time_since_last_check >= self.check_interval:
                    self.logger.info(f"触发定期自检: 距离上次自检{time_since_last_check}秒，间隔要求{self.check_interval}秒")
                    self.logger.info("--- 开始定期自检 ---")
                    self.perform_full_check()
                    last_check_time = current_time  # 只在定时自检时更新时间戳
                    self.logger.info("--- 定期自检完成 ---")
                    
                    # 添加：标记设备自检需要立即上报
                    try:
                        import builtins
                        if hasattr(builtins, 'mqtt_client_instance'):
                            mqtt_client = getattr(builtins, 'mqtt_client_instance')
                            mqtt_client.mark_device_check_for_immediate_upload()
                            self.logger.info("已标记定期自检需要立即上报")
                    except Exception as e:
                        self.logger.error(f"标记设备自检立即上报失败: {e}")
                
                # 等待一段时间再检查
                time.sleep(30)
                
            except Exception as e:
                self.logger.error(f"自检循环发生错误: {e}", exc_info=True)
                time.sleep(60)  # 发生错误时等待较长时间
    
    def perform_full_check(self):
        """执行全面设备自检
        
        Returns:
            dict: 自检结果数据
        """
        self.logger.info("开始执行全面设备自检...")
        
        # 检查设备ID，如果为空则尝试从MQTT客户端获取
        if not self.device_id:
            try:
                import builtins
                if hasattr(builtins, 'mqtt_client_instance'):
                    mqtt_client = getattr(builtins, 'mqtt_client_instance')
                    if mqtt_client.device_id:
                        self.device_id = mqtt_client.device_id
                        self.logger.info(f"从MQTT客户端获取设备ID: {self.device_id}")
            except Exception as e:
                self.logger.warning(f"尝试获取设备ID失败: {e}")
        
        # 准备检查数据结构
        check_data = {
            "data": {
                "co2": {"status": self.STATUS_NORMAL, "message": ""},
                "gps": {"status": self.STATUS_NORMAL, "message": ""},
                "hum": {"status": self.STATUS_NORMAL, "message": ""},
                "temp": {"status": self.STATUS_NORMAL, "message": ""},
                "ws": {"status": self.STATUS_NORMAL, "message": ""},
                "camera": {"status": self.STATUS_NORMAL, "message": ""},
                "fan": {"status": self.STATUS_NORMAL, "message": ""},
                "co2device": {"status": self.STATUS_NORMAL, "message": ""},
                "battery": {"status": self.STATUS_NORMAL, "message": ""},
                "cpu": {"status": self.STATUS_NORMAL, "message": ""},
                "memory": {"status": self.STATUS_NORMAL, "message": ""},
                "disk": {"status": self.STATUS_NORMAL, "message": ""},
                "gpu": {"status": self.STATUS_NORMAL, "message": ""}
            },
            "time": int(time.time()),
            "devid": self.device_id,
            "ver": self.mqtt_config.get('version', '2.0'),
            "dir": "up"
        }
        
        # 执行各项检查
        self._check_sensors(check_data)
        self._check_camera(check_data)
        self._check_battery(check_data)
        self._check_fan(check_data)
        self._check_co2_device(check_data)
        self._check_system_resources(check_data)
        
        # 汇总所有检查结果
        self._summarize_check_results(check_data)
        
        # 保存检查结果
        self._save_check_result(check_data)
        
        self.logger.info("全面设备自检完成")
        return check_data
    
    def _check_sensors(self, check_data):
        """检查各传感器状态"""
        self.logger.info("正在检查传感器状态...")
        
        # 检查传感器收集器是否可用
        if not self.sensor_collector:
            self.logger.error("传感器收集器未设置，无法直接读取传感器数据")
            for sensor in ["co2", "gps", "hum", "temp", "ws"]:
                check_data["data"][sensor]["status"] = self.STATUS_ERROR
                check_data["data"][sensor]["message"] = "传感器收集器未设置，无法读取数据"
            return
        
        # 尝试获取传感器锁
        if not self.sensor_lock.acquire(blocking=False):
            self.logger.warning("无法获取传感器锁，可能有其他线程正在读取传感器")
            for sensor in ["co2", "gps", "hum", "temp", "ws"]:
                check_data["data"][sensor]["status"] = self.STATUS_ERROR
                check_data["data"][sensor]["message"] = "传感器锁获取失败，无法检测"
            return
        
        try:
            # 初始化传感器数据字典
            sensor_values = {
                "temperature": None,
                "humidity": None,
                "co2": None,
                "latitude": None,
                "longitude": None,
                "wind_speed": None
            }
            
            # 直接读取温湿度CO2传感器数据
            self.logger.info("直接读取温湿度CO2传感器数据...")
            cth_data = self.sensor_collector.get_cth_data()
            if cth_data:
                self.logger.info(f"读取温湿度CO2成功: 温度={cth_data.get('temperature')}℃, 湿度={cth_data.get('humidity')}%, CO2={cth_data.get('co2')}ppm")
                sensor_values["temperature"] = cth_data.get("temperature")
                sensor_values["humidity"] = cth_data.get("humidity")
                sensor_values["co2"] = cth_data.get("co2")
            else:
                self.logger.warning("读取温湿度CO2失败")
            
            # 直接读取GPS传感器数据
            self.logger.info("直接读取GPS传感器数据...")
            gps_data = self.sensor_collector.get_gps_data()
            if gps_data:
                self.logger.info(f"读取GPS成功: 经度={gps_data.get('longitude')}, 纬度={gps_data.get('latitude')}")
                sensor_values["longitude"] = gps_data.get("longitude")
                sensor_values["latitude"] = gps_data.get("latitude")
            else:
                self.logger.warning("读取GPS失败")
            
            # 直接读取风速传感器数据
            self.logger.info("直接读取风速传感器数据...")
            ws_data = self.sensor_collector.get_wind_speed()
            if ws_data:
                self.logger.info(f"读取风速成功: {ws_data.get('wind_speed')} m/s")
                sensor_values["wind_speed"] = ws_data.get("wind_speed")
            else:
                self.logger.warning("读取风速失败")
            
            # 检查CO2传感器
            if sensor_values["co2"] is not None:
                co2_value = sensor_values["co2"]
                if co2_value < 0:
                    check_data["data"]["co2"]["status"] = self.STATUS_ERROR
                    check_data["data"]["co2"]["message"] = "CO2传感器读取异常"
                else:
                    # 正常状态下message为空
                    check_data["data"]["co2"]["status"] = self.STATUS_NORMAL
                    check_data["data"]["co2"]["message"] = ""
            else:
                check_data["data"]["co2"]["status"] = self.STATUS_ERROR
                check_data["data"]["co2"]["message"] = "CO2传感器数据缺失"
            
            # 检查GPS传感器
            if sensor_values["latitude"] is not None and sensor_values["longitude"] is not None:
                lat = sensor_values["latitude"]
                lon = sensor_values["longitude"]
                if abs(lat) < 0.01 and abs(lon) < 0.01:
                    check_data["data"]["gps"]["status"] = self.STATUS_ERROR
                    check_data["data"]["gps"]["message"] = "GPS定位异常"
                else:
                    # 正常状态下message为空
                    check_data["data"]["gps"]["status"] = self.STATUS_NORMAL
                    check_data["data"]["gps"]["message"] = ""
            else:
                check_data["data"]["gps"]["status"] = self.STATUS_ERROR
                check_data["data"]["gps"]["message"] = "GPS数据缺失"
            
            # 检查湿度传感器
            if sensor_values["humidity"] is not None:
                hum_value = sensor_values["humidity"]
                if hum_value < 0 or hum_value > 100:
                    check_data["data"]["hum"]["status"] = self.STATUS_ERROR
                    check_data["data"]["hum"]["message"] = "湿度传感器读取异常"
                else:
                    # 正常状态下message为空
                    check_data["data"]["hum"]["status"] = self.STATUS_NORMAL
                    check_data["data"]["hum"]["message"] = ""
            else:
                check_data["data"]["hum"]["status"] = self.STATUS_ERROR
                check_data["data"]["hum"]["message"] = "湿度传感器数据缺失"
            
            # 检查温度传感器
            if sensor_values["temperature"] is not None:
                temp_value = sensor_values["temperature"]
                if temp_value < -50 or temp_value > 100:
                    check_data["data"]["temp"]["status"] = self.STATUS_ERROR
                    check_data["data"]["temp"]["message"] = "温度传感器读取异常"
                else:
                    # 正常状态下message为空
                    check_data["data"]["temp"]["status"] = self.STATUS_NORMAL
                    check_data["data"]["temp"]["message"] = ""
            else:
                check_data["data"]["temp"]["status"] = self.STATUS_ERROR
                check_data["data"]["temp"]["message"] = "温度传感器数据缺失"
            
            # 检查风速传感器
            if sensor_values["wind_speed"] is not None:
                ws_value = sensor_values["wind_speed"]
                if ws_value < 0 or ws_value > 100:
                    check_data["data"]["ws"]["status"] = self.STATUS_ERROR
                    check_data["data"]["ws"]["message"] = "风速传感器读取异常"
                else:
                    # 正常状态下message为空
                    check_data["data"]["ws"]["status"] = self.STATUS_NORMAL
                    check_data["data"]["ws"]["message"] = ""
            else:
                check_data["data"]["ws"]["status"] = self.STATUS_ERROR
                check_data["data"]["ws"]["message"] = "风速传感器数据缺失"
                
        finally:
            # 释放传感器锁
            self.sensor_lock.release()
    
    def _check_camera(self, check_data):
        """检查摄像头状态"""
        self.logger.info("正在检查摄像头状态...")
        
        # 检查视频设备是否存在
        video_devices = glob.glob('/dev/video*')
        if not video_devices:
            check_data["data"]["camera"]["status"] = self.STATUS_ERROR
            check_data["data"]["camera"]["message"] = "摄像头未连接"
            self.logger.warning("未检测到摄像头设备")
        else:
            check_data["data"]["camera"]["status"] = self.STATUS_NORMAL
            check_data["data"]["camera"]["message"] = ""
            self.logger.info(f"检测到摄像头设备: {video_devices}")
    
    def _check_battery(self, check_data):
        """检查电池状态，使用ADS1115读取电池电压"""
        self.logger.info("正在检查电池状态...")
        
        # 从配置中获取重试参数
        max_retries = self.battery_retry_count
        retry_delay = self.battery_retry_delay
        
        for attempt in range(max_retries):
            try:
                if self.i2c is None:
                    check_data["data"]["battery"]["status"] = self.STATUS_ERROR
                    check_data["data"]["battery"]["message"] = "I2C总线不可用，无法读取电池电压"
                    self.logger.error("I2C总线不可用，无法读取电池电压")
                    return
                
                # ADS1115配置参数（单端A0，PGA=±4.096V，单次模式，128 SPS）
                # 高字节: 0xC3 (11000011)
                #   OS=1, MUX=100, PGA=001, MODE=1
                # 低字节: 0x83 (10000011)
                #   DR=100, COMP_QUE=11 (禁用比较器)
                config = [0xC3, 0x83]  # 高字节和低字节
                
                # 写入配置寄存器
                self.i2c.write_i2c_block_data(self.ads_address, 0x01, config)
                time.sleep(0.01)  # 等待转换完成
                
                # 读取转换结果
                data = self.i2c.read_i2c_block_data(self.ads_address, 0x00, 2)
                raw = (data[0] << 8) | data[1]
                if raw > 0x7FFF:
                    raw -= 0xFFFF
                    
                # 计算电压值
                voltage = raw * 4.096 / 32767
                battery_voltage = voltage * (10 + 2.2) / 2.2
                
                # 验证电压值是否在合理范围内
                if battery_voltage < 0 or battery_voltage > 15:  # 假设最大电压为15V
                    self.logger.warning(f"电压值异常: {battery_voltage:.2f}V")
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        continue
                    check_data["data"]["battery"]["status"] = self.STATUS_ERROR
                    check_data["data"]["battery"]["message"] = f"电压值异常: {battery_voltage:.2f}V"
                    return
                
                # 计算电池百分比（使用更精确的电量对照表）
                percent = self._calculate_battery_percentage(battery_voltage)
                
                self.logger.info(f"电池电压: {battery_voltage:.2f}V, 电量百分比: {percent:.1f}%")
                
                # 判断电池是否处于低电量状态
                if percent < self.battery_threshold:
                    check_data["data"]["battery"]["status"] = self.STATUS_ERROR
                    check_data["data"]["battery"]["message"] = f"电池电量低: {percent:.1f}%, 电压: {battery_voltage:.2f}V"
                    self.logger.warning(f"电池电量低: {percent:.1f}%, 电压: {battery_voltage:.2f}V")
                else:
                    check_data["data"]["battery"]["status"] = self.STATUS_NORMAL
                    check_data["data"]["battery"]["message"] = ""
                    self.logger.info(f"电池电量正常: {percent:.1f}%, 电压: {battery_voltage:.2f}V")
                
                # 成功读取，跳出循环
                break
                
            except Exception as e:
                self.logger.error(f"检查电池状态失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                else:
                    check_data["data"]["battery"]["status"] = self.STATUS_ERROR
                    check_data["data"]["battery"]["message"] = f"检查电池状态失败: {str(e)[:50]}"
    
    def _calculate_battery_percentage(self, voltage):
        """根据电池电压计算电池百分比"""
        # 3节锂电池（12.0V及以上为满电）电量对照表
        if voltage >= 12.0:
            return 100
        elif voltage >= 11.7:
            return 90
        elif voltage >= 11.4:
            return 80
        elif voltage >= 11.1:
            return 70
        elif voltage >= 10.8:
            return 60
        elif voltage >= 10.5:
            return 50
        elif voltage >= 10.2:
            return 40
        elif voltage >= 9.9:
            return 30
        elif voltage >= 9.6:
            return 20
        elif voltage >= 9.3:
            return 10
        else:
            return 0
    
    def _check_fan(self, check_data):
        """检查风扇状态"""
        self.logger.info("正在检查风扇状态...")
        
        try:
            # 风扇连接在物理引脚23
            fan_pin = 23
            GPIO.setup(fan_pin, GPIO.OUT)
            
            # 读取当前状态
            fan_status = GPIO.input(fan_pin)
            
            # 验证风扇是否开启
            if fan_status != GPIO.HIGH:
                check_data["data"]["fan"]["status"] = self.STATUS_ERROR
                check_data["data"]["fan"]["message"] = "风扇GPIO状态异常"
                self.logger.warning("风扇GPIO状态异常")
            else:
                check_data["data"]["fan"]["status"] = self.STATUS_NORMAL
                check_data["data"]["fan"]["message"] = ""
                self.logger.info("风扇GPIO状态正常")
        except Exception as e:
            self.logger.error(f"检查风扇状态失败: {e}")
            check_data["data"]["fan"]["status"] = self.STATUS_ERROR
            check_data["data"]["fan"]["message"] = f"检查风扇状态失败: {str(e)[:50]}"
    
    def _check_co2_device(self, check_data):
        """检查CO2控制器状态"""
        self.logger.info("正在检查CO2控制器状态...")

        try:
            # 尝试获取CO2控制器实例
            import builtins
            if hasattr(builtins, 'co2_controller_instance'):
                co2_controller = getattr(builtins, 'co2_controller_instance')
                
                # 检查CO2控制器是否处于运行状态
                if hasattr(co2_controller, 'running') and co2_controller.running:
                    # 检查当前状态
                    if hasattr(co2_controller, 'state'):
                        # 导入CO2ControllerState枚举
                        from devices.co2_controller import CO2ControllerState
                        
                        if co2_controller.state == CO2ControllerState.ERROR:
                            check_data["data"]["co2device"]["status"] = self.STATUS_ERROR
                            check_data["data"]["co2device"]["message"] = "CO2控制器处于错误状态"
                            self.logger.warning("CO2控制器处于错误状态")
                        else:
                            check_data["data"]["co2device"]["status"] = self.STATUS_NORMAL
                            check_data["data"]["co2device"]["message"] = ""
                            self.logger.info(f"CO2控制器状态正常: {co2_controller.state.name}")
                    else:
                        check_data["data"]["co2device"]["status"] = self.STATUS_ERROR
                        check_data["data"]["co2device"]["message"] = "CO2控制器状态未知"
                        self.logger.warning("CO2控制器状态未知")
                else:
                    check_data["data"]["co2device"]["status"] = self.STATUS_ERROR
                    check_data["data"]["co2device"]["message"] = "CO2控制器未运行"
                    self.logger.warning("CO2控制器未运行")
            else:
                check_data["data"]["co2device"]["status"] = self.STATUS_ERROR
                check_data["data"]["co2device"]["message"] = "CO2控制器实例不可用"
                self.logger.warning("CO2控制器实例不可用")
        except Exception as e:
            self.logger.error(f"检查CO2控制器状态时发生错误: {e}", exc_info=True)
            check_data["data"]["co2device"]["status"] = self.STATUS_ERROR
            check_data["data"]["co2device"]["message"] = f"检查CO2控制器状态时发生错误: {str(e)[:50]}"
    
    def _check_system_resources(self, check_data):
        """检查系统资源（CPU、内存、硬盘、GPU）"""
        self.logger.info("正在检查系统资源...")
        
        # 检查CPU使用率
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 90:
                check_data["data"]["cpu"]["status"] = self.STATUS_ERROR
                check_data["data"]["cpu"]["message"] = f"CPU使用率过高: {cpu_percent}%"
                self.logger.warning(f"CPU使用率过高: {cpu_percent}%")
            else:
                check_data["data"]["cpu"]["status"] = self.STATUS_NORMAL
                check_data["data"]["cpu"]["message"] = ""
                self.logger.info(f"CPU使用率正常: {cpu_percent}%")
        except Exception as e:
            self.logger.error(f"检查CPU使用率失败: {e}")
            check_data["data"]["cpu"]["status"] = self.STATUS_ERROR
            check_data["data"]["cpu"]["message"] = f"检查CPU使用率失败: {str(e)[:50]}"
        
        # 检查内存使用率
        try:
            memory = psutil.virtual_memory()
            available_percent = memory.available * 100 / memory.total
            if available_percent < 10:
                check_data["data"]["memory"]["status"] = self.STATUS_ERROR
                check_data["data"]["memory"]["message"] = f"可用内存不足: {available_percent:.1f}%"
                self.logger.warning(f"可用内存不足: {available_percent:.1f}%")
            else:
                check_data["data"]["memory"]["status"] = self.STATUS_NORMAL
                check_data["data"]["memory"]["message"] = ""
                self.logger.info(f"内存使用率正常: 可用{available_percent:.1f}%")
        except Exception as e:
            self.logger.error(f"检查内存使用率失败: {e}")
            check_data["data"]["memory"]["status"] = self.STATUS_ERROR
            check_data["data"]["memory"]["message"] = f"检查内存使用率失败: {str(e)[:50]}"
        
        # 检查硬盘空间
        try:
            disk = psutil.disk_usage('/')
            free_percent = disk.free * 100 / disk.total
            if free_percent < 10:
                check_data["data"]["disk"]["status"] = self.STATUS_ERROR
                check_data["data"]["disk"]["message"] = f"硬盘空间不足: {free_percent:.1f}%"
                self.logger.warning(f"硬盘空间不足: {free_percent:.1f}%")
            else:
                check_data["data"]["disk"]["status"] = self.STATUS_NORMAL
                check_data["data"]["disk"]["message"] = ""
                self.logger.info(f"硬盘空间充足: 可用{free_percent:.1f}%")
        except Exception as e:
            self.logger.error(f"检查硬盘空间失败: {e}")
            check_data["data"]["disk"]["status"] = self.STATUS_ERROR
            check_data["data"]["disk"]["message"] = f"检查硬盘空间失败: {str(e)[:50]}"
        
        # 检查GPU使用率（使用tegrastats命令）
        try:
            # 使用管道和head组合获取tegrastats的单行输出
            cmd = "tegrastats --interval 500 | head -n1"
            tegrastats_output = subprocess.check_output(cmd, shell=True, universal_newlines=True, timeout=2)
            
            self.logger.debug(f"tegrastats原始输出: {tegrastats_output}")
            
            # 解析GPU使用率 - 查找GR3D_FREQ
            if "GR3D_FREQ" in tegrastats_output:
                # 提取GR3D_FREQ后的百分比
                gpu_info = tegrastats_output.split("GR3D_FREQ")[1].split("%")[0].strip()
                try:
                    gpu_percent = float(gpu_info)
                    if gpu_percent > 90:
                        check_data["data"]["gpu"]["status"] = self.STATUS_ERROR
                        check_data["data"]["gpu"]["message"] = f"GPU使用率过高: {gpu_percent}%"
                        self.logger.warning(f"GPU使用率过高: {gpu_percent}%")
                    else:
                        check_data["data"]["gpu"]["status"] = self.STATUS_NORMAL
                        check_data["data"]["gpu"]["message"] = ""
                        self.logger.info(f"GPU使用率正常: {gpu_percent}%")
                except ValueError:
                    self.logger.warning(f"无法解析GPU使用率: {gpu_info}")
                    check_data["data"]["gpu"]["status"] = self.STATUS_NORMAL
                    check_data["data"]["gpu"]["message"] = ""
            else:
                check_data["data"]["gpu"]["status"] = self.STATUS_NORMAL
                check_data["data"]["gpu"]["message"] = ""
                self.logger.info("未在输出中找到GPU信息(GR3D_FREQ)")
        except subprocess.TimeoutExpired:
            self.logger.warning("获取GPU信息超时")
            check_data["data"]["gpu"]["status"] = self.STATUS_NORMAL
            check_data["data"]["gpu"]["message"] = ""
        except Exception as e:
            self.logger.error(f"检查GPU使用率失败: {e}", exc_info=True)
            # 不将GPU检查失败视为错误，因为这可能是由于命令不可用
            check_data["data"]["gpu"]["status"] = self.STATUS_NORMAL
            check_data["data"]["gpu"]["message"] = ""
            self.logger.warning(f"无法获取GPU信息: {str(e)}")
    
    def _summarize_check_results(self, check_data):
        """汇总所有检查结果并跟踪异常组件"""
        self.logger.info("汇总自检结果...")
        
        # 检查是否有任何错误
        has_error = False
        error_messages = []
        error_count = 0
        
        # 记录各类型传感器的状态
        sensor_errors = []
        system_errors = []
        device_errors = []
        
        # 更新异常组件跟踪集合
        components_now_in_error = set()
        
        for item, status in check_data["data"].items():
            if status["status"] == self.STATUS_ERROR:
                has_error = True
                error_count += 1
                components_now_in_error.add(item)  # 添加到当前异常集合

                # 根据错误类型分类
                if item in ["co2", "gps", "hum", "temp", "ws"]:
                    sensor_errors.append(f"{item}: {status['message']}")
                elif item in ["cpu", "memory", "disk", "gpu"]:
                    system_errors.append(f"{item}: {status['message']}")
                else:
                    device_errors.append(f"{item}: {status['message']}")

        # 更新组件异常状态跟踪
        self.components_in_error = components_now_in_error

        # 合并所有错误信息
        error_messages.extend(sensor_errors)
        error_messages.extend(device_errors)
        error_messages.extend(system_errors)

        # 记录汇总状态（仅用于日志，不再保存到JSON中）
        if has_error:
            # 构建更有信息量的错误消息
            summary_parts = []

            if sensor_errors:
                sensor_count = len(sensor_errors)
                summary_parts.append(f"{sensor_count}个传感器异常")

            if device_errors:
                device_count = len(device_errors)
                summary_parts.append(f"{device_count}个设备异常")

            if system_errors:
                system_count = len(system_errors)
                summary_parts.append(f"{system_count}个系统资源异常")

            # 合并汇总信息
            summary = "、".join(summary_parts)

            self.logger.warning(f"自检发现{error_count}项异常: {summary}")

            # 添加：检测到异常时标记设备自检需要立即上报
            try:
                import builtins
                if hasattr(builtins, 'mqtt_client_instance'):
                    mqtt_client = getattr(builtins, 'mqtt_client_instance')
                    mqtt_client.mark_device_check_for_immediate_upload()
                    self.logger.info("检测到异常，已标记设备自检需要立即上报")
            except Exception as e:
                self.logger.error(f"标记设备自检立即上报失败: {e}")
        else:
            self.logger.info("所有自检项目正常")
    
    def _save_check_result(self, check_data):
        """保存自检结果到独立文件
        Args:
            check_data: 自检数据字典
        """
        try:
            # 确保不在上传数据中包含uploaded字段
            mqtt_check_data = check_data.copy()
            if "uploaded" in mqtt_check_data:
                del mqtt_check_data["uploaded"]
                
            # 准备保存的数据
            local_check_data = check_data.copy()
            # 统一使用time字段作为时间戳
            timestamp = local_check_data.get('time', int(time.time()))
            # 确保time字段存在
            local_check_data["time"] = timestamp
            # 添加上传标志
            local_check_data["uploaded"] = False
            
            # 确保pending目录存在
            pending_dir = os.path.join(self.check_dir, 'pending')
            if not os.path.exists(pending_dir):
                os.makedirs(pending_dir)
                self.logger.info(f"创建目录: {pending_dir}")
            
            # 使用时间戳作为文件名
            file_path = os.path.join(pending_dir, f"check_{timestamp}.json")
            
            # 检查是否已存在同名文件(防止重复)
            if os.path.exists(file_path):
                self.logger.warning(f"跳过重复自检数据保存: 时间戳={timestamp} 文件已存在")
                return
            
            # 保存为单独的JSON文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(local_check_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"已保存设备自检数据到文件 {file_path}")
            
            # 注释/删除MQTT主动上传部分
            # try:
            #     import builtins
            #     if hasattr(builtins, 'mqtt_client_instance'):
            #         mqtt_client = getattr(builtins, 'mqtt_client_instance')
            #         mqtt_client.publish_device_check(mqtt_check_data)
            #         self.logger.info("已将自检数据发送到MQTT客户端")
            # except Exception as e:
            #     self.logger.error(f"通过MQTT上传设备自检数据失败: {e}")
                
        except Exception as e:
            self.logger.error(f"保存设备自检数据失败: {e}", exc_info=True)
    
    def archive_uploaded_checks(self, timestamps):
        """归档已上传的自检数据
        
        Args:
            timestamps: 已上传的自检数据时间戳列表
        
        Returns:
            bool: 是否成功归档
        """
        try:
            self.logger.info(f"归档时间戳列表: {timestamps}")
            
            # 确保上传和历史目录存在
            uploaded_dir = os.path.join(self.check_dir, 'uploaded')
            history_dir = os.path.join(self.check_dir, 'history')
            
            for directory in [uploaded_dir, history_dir]:
                if not os.path.exists(directory):
                    os.makedirs(directory)
                    self.logger.info(f"创建目录: {directory}")
            
            # 获取uploaded目录中的所有文件
            if not os.path.isdir(uploaded_dir):
                self.logger.warning(f"{uploaded_dir} 不是有效目录")
                return False
                
            files = os.listdir(uploaded_dir)
            archived_count = 0
            
            # 按时间戳排序，处理最早的N个文件，N等于timestamps的长度
            check_files = []
            for filename in files:
                if filename.startswith("check_") and filename.endswith(".json"):
                    # 从文件名解析时间戳
                    try:
                        ts = int(filename[6:-5])  # 提取check_{timestamp}.json中的timestamp部分
                        check_files.append((ts, filename))
                    except ValueError:
                        self.logger.warning(f"无法从文件名解析时间戳: {filename}")
            
            # 按时间戳排序
            check_files.sort()
            
            # 处理最早的N个文件
            for i, (ts, filename) in enumerate(check_files):
                if i >= len(timestamps):
                    break
                
                src_path = os.path.join(uploaded_dir, filename)
                dst_path = os.path.join(history_dir, filename)
                
                try:
                    # 移动文件到历史目录
                    shutil.move(src_path, dst_path)
                    archived_count += 1
                    self.logger.info(f"已归档文件: {filename}")
                except Exception as e:
                    self.logger.error(f"归档文件 {filename} 失败: {e}")
            
            self.logger.info(f"已归档 {archived_count} 个设备自检文件")
            return True
            
        except Exception as e:
            self.logger.error(f"归档设备自检数据失败: {e}", exc_info=True)
            return False
            
    def archive_all_current_checks(self):
        """归档所有当前自检数据，不依赖时间戳匹配
        
        Returns:
            bool: 是否成功归档
        """
        try:
            self.logger.info("开始归档所有当前设备自检数据")
            
            # 确保上传和历史目录存在
            pending_dir = os.path.join(self.check_dir, 'pending')
            uploaded_dir = os.path.join(self.check_dir, 'uploaded')
            history_dir = os.path.join(self.check_dir, 'history')
            
            for directory in [pending_dir, uploaded_dir, history_dir]:
                if not os.path.exists(directory):
                    os.makedirs(directory)
                    self.logger.info(f"创建目录: {directory}")
            
            # 获取pending目录中的所有文件
            if not os.path.isdir(pending_dir):
                self.logger.warning(f"{pending_dir} 不是有效目录")
                return False
                
            # 遍历pending目录中的所有自检文件
            archived_count = 0
            for filename in os.listdir(pending_dir):
                if filename.startswith("check_") and filename.endswith(".json"):
                    src_path = os.path.join(pending_dir, filename)
                    dst_path = os.path.join(history_dir, filename)
                    
                    try:
                        # 移动文件到历史目录
                        shutil.move(src_path, dst_path)
                        archived_count += 1
                        self.logger.debug(f"已归档文件: {filename}")
                    except Exception as e:
                        self.logger.error(f"归档文件 {filename} 失败: {e}")
            
            # 遍历uploaded目录中的所有自检文件
            for filename in os.listdir(uploaded_dir):
                if filename.startswith("check_") and filename.endswith(".json"):
                    src_path = os.path.join(uploaded_dir, filename)
                    dst_path = os.path.join(history_dir, filename)
                    
                    try:
                        # 移动文件到历史目录
                        shutil.move(src_path, dst_path)
                        archived_count += 1
                        self.logger.debug(f"已归档文件: {filename}")
                    except Exception as e:
                        self.logger.error(f"归档文件 {filename} 失败: {e}")
            
            self.logger.info(f"已归档 {archived_count} 个设备自检文件")
            return True
            
        except Exception as e:
            self.logger.error(f"归档设备自检数据失败: {e}", exc_info=True)
            return False
            
    # 添加用于测试的方法
    def simulate_hardware_failure(self, component, status):
        """模拟硬件故障
        
        Args:
            component (str): 要模拟故障的组件名称
            status (int): 故障状态码
            
        Returns:
            bool: 故障模拟是否成功
        """
        self.logger.info(f"模拟硬件故障: {component}, 状态: {status}")
        
        # 检查设备ID，如果为空则尝试从MQTT客户端获取
        if not self.device_id:
            try:
                import builtins
                if hasattr(builtins, 'mqtt_client_instance'):
                    mqtt_client = getattr(builtins, 'mqtt_client_instance')
                    if mqtt_client.device_id:
                        self.device_id = mqtt_client.device_id
                        self.logger.info(f"从MQTT客户端获取设备ID: {self.device_id}")
            except Exception as e:
                self.logger.warning(f"尝试获取设备ID失败: {e}")
        
        # 初始化自检结果数据
        check_data = {
            "data": {
                "co2": {"status": self.STATUS_NORMAL, "message": ""},
                "gps": {"status": self.STATUS_NORMAL, "message": ""},
                "hum": {"status": self.STATUS_NORMAL, "message": ""},
                "temp": {"status": self.STATUS_NORMAL, "message": ""},
                "ws": {"status": self.STATUS_NORMAL, "message": ""},
                "camera": {"status": self.STATUS_NORMAL, "message": ""},
                "battery": {"status": self.STATUS_NORMAL, "message": ""},
                "fan": {"status": self.STATUS_NORMAL, "message": ""},
                "co2device": {"status": self.STATUS_NORMAL, "message": ""},
                "cpu": {"status": self.STATUS_NORMAL, "message": ""},
                "memory": {"status": self.STATUS_NORMAL, "message": ""},
                "disk": {"status": self.STATUS_NORMAL, "message": ""},
                "gpu": {"status": self.STATUS_NORMAL, "message": ""}
            },
            "time": int(time.time()),
            "devid": self.device_id,
            "ver": "2.0",
            "dir": "up"
        }
        
        # 验证组件名称是否有效
        if component not in check_data["data"]:
            self.logger.error(f"无效的组件名称: {component}")
            return False
            
        # 更新组件状态
        check_data["data"][component]["status"] = status
        check_data["data"][component]["message"] = "测试模拟故障"
        
        # 执行正常的故障处理流程
        self._summarize_check_results(check_data)
        self._save_check_result(check_data)
        
        self.logger.info(f"已模拟 {component} 组件故障，状态: {status}")
        return True 

    def handle_device_error(self, component, error_message):
        """处理设备错误，触发自检并上报
        
        Args:
            component: 出错的组件名称
            error_message: 错误消息
        """
        self.logger.warning(f"设备错误触发自检: {component} - {error_message}")
        
        # 执行自检
        check_data = self.perform_full_check()
        
        # 确保出错的组件被标记为错误
        if component in check_data["data"]:
            check_data["data"][component]["status"] = self.STATUS_ERROR
            check_data["data"][component]["message"] = error_message
            
            # 更新汇总状态
            self._summarize_check_results(check_data)
            
            # 保存并上报结果
            self._save_check_result(check_data)
            
        self.logger.info(f"已为组件 {component} 错误触发自检上报")
        
        return check_data  # 返回自检数据以便调用方可以进行进一步处理 

    def report_component_status(self, component, status, message=""):
        """上报组件状态（带状态变化检测）

        Args:
            component: 组件名称
            status: 状态码 (0=正常, 1=异常)
            message: 状态消息，正常时为空字符串

        Returns:
            bool: 是否成功上报
        """
        current_time = time.time()

        # 检查状态是否发生变化
        old_status = self.component_states.get(component, {}).get('status', None)
        old_message = self.component_states.get(component, {}).get('message', '')

        # 更新组件状态记录
        if component not in self.component_states:
            self.component_states[component] = {}

        self.component_states[component].update({
            'status': status,
            'message': message,
            'last_change_time': current_time
        })

        # 只在状态真正变化时立即上报
        if old_status != status:
            self.logger.info(f"组件 {component} 状态变化: {old_status} → {status}, 消息: {message}")
            return self._do_immediate_report(component, status, message)
        elif status == self.STATUS_ERROR and old_message != message:
            # 错误状态下消息变化也立即上报
            self.logger.info(f"组件 {component} 错误消息变化: {old_message} → {message}")
            return self._do_immediate_report(component, status, message)
        else:
            # 状态未变化，记录但不立即上报
            self.logger.debug(f"组件 {component} 状态未变化: {status}, 消息: {message}")
            return True

    def _do_immediate_report(self, component, status, message):
        """执行立即上报

        Args:
            component: 组件名称
            status: 状态码
            message: 状态消息

        Returns:
            bool: 是否成功上报
        """
        self.logger.info(f"立即上报组件 {component} 状态: {status}, 消息: {message}")

        # 检查设备ID，如果为空则尝试从MQTT客户端获取
        if not self.device_id:
            try:
                import builtins
                if hasattr(builtins, 'mqtt_client_instance'):
                    mqtt_client = getattr(builtins, 'mqtt_client_instance')
                    if mqtt_client.device_id:
                        self.device_id = mqtt_client.device_id
                        self.logger.info(f"从MQTT客户端获取设备ID: {self.device_id}")
            except Exception as e:
                self.logger.warning(f"尝试获取设备ID失败: {e}")

        # 创建完整的检查数据结构
        check_data = {
            "data": {
                "co2": {"status": self.STATUS_NORMAL, "message": ""},
                "gps": {"status": self.STATUS_NORMAL, "message": ""},
                "hum": {"status": self.STATUS_NORMAL, "message": ""},
                "temp": {"status": self.STATUS_NORMAL, "message": ""},
                "ws": {"status": self.STATUS_NORMAL, "message": ""},
                "camera": {"status": self.STATUS_NORMAL, "message": ""},
                "fan": {"status": self.STATUS_NORMAL, "message": ""},
                "co2device": {"status": self.STATUS_NORMAL, "message": ""},
                "battery": {"status": self.STATUS_NORMAL, "message": ""},
                "cpu": {"status": self.STATUS_NORMAL, "message": ""},
                "memory": {"status": self.STATUS_NORMAL, "message": ""},
                "disk": {"status": self.STATUS_NORMAL, "message": ""},
                "gpu": {"status": self.STATUS_NORMAL, "message": ""}
            },
            "time": int(time.time()),  # 使用time字段，统一格式
            "devid": self.device_id,
            "ver": self.mqtt_config.get('version', '2.0'),
            "dir": "up"
        }

        # 验证组件名称是否有效
        if component not in check_data["data"]:
            self.logger.error(f"无效的组件名称: {component}")
            return False

        # 更新指定组件的状态
        check_data["data"][component]["status"] = status
        check_data["data"][component]["message"] = message

        # 记录上报时间
        self.component_last_report[component] = time.time()

        # 重置退避间隔（状态变化时重置）
        if status == self.STATUS_NORMAL:
            # 恢复正常时重置退避
            self.component_backoff_intervals[component] = self.initial_backoff_interval
            self.component_consecutive_failures[component] = 0

        # 保存并上报结果
        self._save_check_result(check_data)

        # 标记设备自检需要立即上报
        try:
            import builtins
            if hasattr(builtins, 'mqtt_client_instance'):
                mqtt_client = getattr(builtins, 'mqtt_client_instance')
                mqtt_client.mark_device_check_for_immediate_upload()
                self.logger.debug("已标记设备自检需要立即上报")
        except Exception as e:
            self.logger.error(f"标记设备自检立即上报失败: {e}")

        # 更新异常组件集合
        if status == self.STATUS_ERROR:
            self.components_in_error.add(component)
        else:
            self.components_in_error.discard(component)

        return True

    def report_component_status_with_backoff(self, component, status, message=""):
        """使用退避策略上报组件状态

        Args:
            component: 组件名称
            status: 状态码 (0=正常, 1=异常)
            message: 状态消息

        Returns:
            bool: 是否成功上报
        """
        current_time = time.time()

        # 检查是否需要退避
        if component in self.component_last_report:
            last_report_time = self.component_last_report[component]
            backoff_interval = self.component_backoff_intervals.get(component, self.initial_backoff_interval)

            if current_time - last_report_time < backoff_interval:
                remaining_time = backoff_interval - (current_time - last_report_time)
                self.logger.debug(f"组件 {component} 上报被退避，剩余时间: {remaining_time:.1f}秒")
                return False

        # 执行上报
        success = self._do_immediate_report(component, status, message)

        if success:
            # 根据状态调整下次上报间隔
            if status == self.STATUS_ERROR:
                # 错误状态使用指数退避
                current_interval = self.component_backoff_intervals.get(component, self.initial_backoff_interval)
                new_interval = min(current_interval * self.backoff_multiplier, self.max_backoff_interval)
                self.component_backoff_intervals[component] = new_interval

                # 增加连续失败计数
                self.component_consecutive_failures[component] = self.component_consecutive_failures.get(component, 0) + 1

                self.logger.debug(f"组件 {component} 错误上报成功，下次退避间隔: {new_interval}秒")
            else:
                # 正常状态重置间隔
                self.component_backoff_intervals[component] = self.initial_backoff_interval
                self.component_consecutive_failures[component] = 0
                self.logger.debug(f"组件 {component} 恢复正常，退避间隔已重置")

        return success

    def report_component_error(self, component, error_code, error_message):
        """组件发生异常时立即上报
        
        Args:
            component: 组件名称
            error_code: 错误代码
            error_message: 错误详细信息
            
        Returns:
            bool: 是否成功上报
        """
        self.logger.warning(f"组件 {component} 发生异常，立即上报: {error_message}")
        
        # 直接调用report_component_status，使用统一的上报格式
        # 这样可以保证异常上报的JSON格式与自检上传一致
        status = self.STATUS_ERROR
        message = f"错误代码: {error_code}, 详情: {error_message}"
        
        # 使用现有的组件状态上报机制，它会自动处理立即上报
        success = self.report_component_status(component, status, message)
        
        if success:
            self.logger.info(f"组件 {component} 异常已触发立即上报")
            # 将组件添加到异常组件集合中，以便恢复检查
            self.components_in_error.add(component)
        else:
            self.logger.error(f"组件 {component} 异常上报失败")
            
        return success

    def _recovery_check_loop(self):
        """组件恢复检查循环，检查处于异常状态的组件是否已恢复"""
        self.logger.info("组件恢复检查线程已启动")
        
        while self.running:
            try:
                # 如果有组件处于异常状态，尝试检查它们是否恢复
                if self.components_in_error:
                    self.logger.info(f"开始检查异常组件恢复状态: {self.components_in_error}")
                    components_to_check = self.components_in_error.copy()
                    
                    for component in components_to_check:
                        # 根据组件类型执行特定的检查
                        recovered = False
                        if component in ['co2', 'temp', 'hum']:
                            recovered = self._check_cth_sensor_recovery(component)
                        elif component == 'gps':
                            recovered = self._check_gps_recovery()
                        elif component == 'ws':
                            recovered = self._check_ws_recovery()
                        elif component == 'camera':
                            recovered = self._check_camera_recovery()
                        elif component == 'fan':
                            recovered = self._check_fan_recovery()
                        elif component == 'co2device':
                            recovered = self._check_co2device_recovery()
                        elif component in ['cpu', 'memory', 'disk', 'gpu']:
                            recovered = self._check_system_resource_recovery(component)

                        # 如果组件已恢复，上报恢复状态
                        if recovered:
                            self.logger.info(f"组件 {component} 已恢复，上报恢复状态")
                            self.report_component_status(component, self.STATUS_NORMAL, "组件已恢复正常")
                
                # 等待下一次检查
                time.sleep(self.recovery_check_interval)
                
            except Exception as e:
                self.logger.error(f"组件恢复检查过程中发生错误: {e}", exc_info=True)
                time.sleep(30)  # 发生错误时等待较长时间 

    def _check_cth_sensor_recovery(self, sensor_type):
        """检查温湿度CO2传感器是否已恢复
        
        Args:
            sensor_type: 传感器类型 (co2, temp, hum)
            
        Returns:
            bool: 是否恢复正常
        """
        try:
            if not self.sensor_collector:
                return False
                
            cth_data = self.sensor_collector.get_cth_data()
            if not cth_data:
                return False
                
            if sensor_type == 'co2' and 'co2' in cth_data and cth_data['co2'] is not None:
                co2_value = cth_data['co2']
                if co2_value >= 0:  # 有效的CO2值
                    self.logger.info("CO2传感器已恢复正常")
                    return True
                    
            elif sensor_type == 'temp' and 'temperature' in cth_data and cth_data['temperature'] is not None:
                temp_value = cth_data['temperature']
                if -50 <= temp_value <= 100:  # 有效的温度范围
                    self.logger.info("温度传感器已恢复正常")
                    return True
                    
            elif sensor_type == 'hum' and 'humidity' in cth_data and cth_data['humidity'] is not None:
                hum_value = cth_data['humidity']
                if 0 <= hum_value <= 100:  # 有效的湿度范围
                    self.logger.info("湿度传感器已恢复正常")
                    return True
                    
            return False
        except Exception as e:
            self.logger.error(f"检查{sensor_type}传感器恢复失败: {e}")
            return False

    def _check_gps_recovery(self):
        """检查GPS传感器是否已恢复
        
        Returns:
            bool: 是否恢复正常
        """
        try:
            if not self.sensor_collector:
                return False
                
            gps_data = self.sensor_collector.get_gps_data()
            if gps_data and 'latitude' in gps_data and 'longitude' in gps_data:
                lat = gps_data['latitude']
                lon = gps_data['longitude']
                
                if lat is not None and lon is not None and (abs(lat) > 0.01 or abs(lon) > 0.01):
                    self.logger.info("GPS传感器已恢复正常")
                    return True
                    
            return False
        except Exception as e:
            self.logger.error(f"检查GPS传感器恢复失败: {e}")
            return False

    def _check_ws_recovery(self):
        """检查风速传感器是否已恢复
        
        Returns:
            bool: 是否恢复正常
        """
        try:
            if not self.sensor_collector:
                return False
                
            ws_data = self.sensor_collector.get_wind_speed()
            if ws_data and 'wind_speed' in ws_data and ws_data['wind_speed'] is not None:
                ws_value = ws_data['wind_speed']
                if 0 <= ws_value <= 100:  # 有效的风速范围
                    self.logger.info("风速传感器已恢复正常")
                    return True
                    
            return False
        except Exception as e:
            self.logger.error(f"检查风速传感器恢复失败: {e}")
            return False

    def _check_camera_recovery(self):
        """检查摄像头是否已恢复
        
        Returns:
            bool: 是否恢复正常
        """
        try:
            video_devices = glob.glob('/dev/video*')
            if video_devices:
                self.logger.info(f"摄像头设备已恢复: {video_devices}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"检查摄像头恢复失败: {e}")
            return False

    def _check_fan_recovery(self):
        """检查风扇是否已恢复
        
        Returns:
            bool: 是否恢复正常
        """
        try:
            fan_pin = 23
            GPIO.setup(fan_pin, GPIO.OUT)
            
            # 尝试重置风扇状态
            GPIO.output(fan_pin, GPIO.LOW)
            time.sleep(0.5)
            GPIO.output(fan_pin, GPIO.HIGH)
            time.sleep(0.5)
            
            fan_status = GPIO.input(fan_pin)
            if fan_status == GPIO.HIGH:
                self.logger.info("风扇已恢复正常")
                return True
            return False
        except Exception as e:
            self.logger.error(f"检查风扇恢复失败: {e}")
            return False

    def _check_co2device_recovery(self):
        """检查CO2控制器是否已恢复
        
        Returns:
            bool: 是否恢复正常
        """
        try:
            # CO2控制器现在使用独立文件存储，检查pending目录中的最新文件
            devices_dir = os.path.join(self.data_dir, self.storage_config.get('devices_dir', 'devices'))
            pending_dir = os.path.join(devices_dir, 'pending')

            self.logger.debug(f"检查CO2状态文件路径: {pending_dir}")

            if not os.path.exists(pending_dir):
                self.logger.debug(f"CO2状态pending目录不存在: {pending_dir}")
                return False

            # 获取最新的CO2状态文件
            co2_files = []
            for filename in os.listdir(pending_dir):
                if filename.startswith("co2_") and filename.endswith(".json"):
                    file_path = os.path.join(pending_dir, filename)
                    try:
                        # 从文件名提取时间戳
                        timestamp_str = filename.replace("co2_", "").replace(".json", "")
                        timestamp = int(timestamp_str)
                        co2_files.append((timestamp, file_path))
                    except ValueError:
                        continue

            self.logger.debug(f"找到 {len(co2_files)} 个CO2状态文件")

            if not co2_files:
                self.logger.debug("没有找到CO2状态文件")
                return False

            # 获取最新的状态文件
            co2_files.sort(reverse=True)  # 按时间戳降序排列
            latest_timestamp, latest_file = co2_files[0]

            self.logger.debug(f"检查最新CO2状态文件: {latest_file}")

            with open(latest_file, 'r', encoding='utf-8') as f:
                latest_status = json.load(f)

            current_time = time.time()

            if latest_status and ("time" in latest_status or "timestamp" in latest_status):
                # 检查状态是否最近更新且不处于错误状态，优先使用time字段
                status_time = latest_status.get("time", latest_status.get("timestamp", 0))
                if current_time - status_time <= 600 and latest_status.get("data", {}).get("status") != 2:  # 2是ERROR状态
                    self.logger.info("CO2控制器已恢复正常")
                    return True

            return False
        except Exception as e:
            self.logger.error(f"检查CO2控制器恢复失败: {e}")
            return False

    def _check_system_resource_recovery(self, resource_type):
        """检查系统资源是否已恢复正常
        
        Args:
            resource_type: 资源类型 (cpu, memory, disk, gpu)
            
        Returns:
            bool: 是否恢复正常
        """
        try:
            if resource_type == 'cpu':
                cpu_percent = psutil.cpu_percent(interval=1)
                if cpu_percent <= 90:
                    self.logger.info(f"CPU使用率已恢复正常: {cpu_percent}%")
                    return True
                    
            elif resource_type == 'memory':
                memory = psutil.virtual_memory()
                available_percent = memory.available * 100 / memory.total
                if available_percent >= 10:
                    self.logger.info(f"内存使用率已恢复正常: 可用{available_percent:.1f}%")
                    return True
                    
            elif resource_type == 'disk':
                disk = psutil.disk_usage('/')
                free_percent = disk.free * 100 / disk.total
                if free_percent >= 10:
                    self.logger.info(f"磁盘空间已恢复正常: 可用{free_percent:.1f}%")
                    return True
                    
            elif resource_type == 'gpu':
                try:
                    tegrastats_output = subprocess.check_output(
                        ["tegrastats", "--interval", "500", "--count", "1"], 
                        universal_newlines=True
                    )
                    
                    if "GPU" in tegrastats_output:
                        gpu_info = tegrastats_output.split("GPU")[1].split("%")[0].strip()
                        gpu_percent = float(gpu_info)
                        if gpu_percent <= 90:
                            self.logger.info(f"GPU使用率已恢复正常: {gpu_percent}%")
                            return True
                except Exception:
                    # GPU检查失败不视为错误，直接返回False
                    pass
                    
            return False
        except Exception as e:
            self.logger.error(f"检查系统资源{resource_type}恢复失败: {e}")
            return False 