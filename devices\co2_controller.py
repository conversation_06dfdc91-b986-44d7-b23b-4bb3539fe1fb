#!/usr/bin/env python3
import Jetson.GPIO as GPIO
import threading
import time
import json
import os
import logging
from enum import Enum
from utils.config_loader import load_config
from utils.logger import get_logger

class CO2ControllerState(Enum):
    """CO2控制器状态枚举"""
    ADSORPTION = 1  # 吸附阶段
    HEATING = 2     # 加热阶段
    RELEASE = 3     # 释放阶段
    ERROR = -1      # 错误状态


class CO2Controller:
    """CO2控制器主类"""
    
    def __init__(self):
        """初始化CO2控制器"""
        self.file_operation_lock = threading.Lock()
        # 初始化日志
        self.logger = get_logger("co2_controller")
        self.logger.info("初始化CO2控制器...")
        
        # 加载配置
        self.config = load_config()
        self.co2_config = self.config['co2_controller']
        self.storage_config = self.config['data_storage']
        
        # 初始化线程管理器
        self._init_thread_manager()

        # 初始化GPIO
        # 风扇引脚
        self.fan_pins = self.co2_config['pins']['fans']
        # 水泵引脚
        self.pump_pins = self.co2_config['pins']['pumps']
        # 加热片引脚
        self.heater_pin = self.co2_config['pins']['heater']
        # 步进电机引脚
        self.stepper_pins = self.co2_config['pins']['stepper']
        
        # 初始化引脚模式
        for pin in self.fan_pins + self.pump_pins + [self.heater_pin] + self.stepper_pins:
            GPIO.setup(pin, GPIO.OUT)
            GPIO.output(pin, GPIO.LOW)
        
        self.logger.info("GPIO引脚初始化完成")
        
        # 控制循环线程
        self.control_thread = None
        self.running = False
        self.stopping = False  # 添加停止中标志
        
        # 状态变量
        self.current_state = "IDLE"  # 当前状态: IDLE, ADSORPTION, HEATING, RELEASE
        self.current_round = 0       # 当前轮次
        self.start_time = 0          # 当前状态开始时间
        self.end_time = 0            # 当前状态结束时间
        
        # 添加缺少的属性
        self.state = CO2ControllerState.ADSORPTION  # 初始状态设置为吸附状态
        # 设备ID初始化为空字符串，将在需要时从MQTT客户端获取
        self.device_id = ""
        self.phase_remaining = 0  # 当前阶段剩余时间（分钟）
        
        # 确保数据目录存在
        self.data_dir = os.path.join(self.storage_config['data_dir'], self.storage_config.get('devices_dir', 'devices'))

        # 确保数据目录存在
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
            self.logger.info(f"创建目录: {self.data_dir}")

        # 数据文件（只保留错误文件，状态数据现在使用独立文件存储）
        # 错误文件暂时保存在data_dir根目录下
        self.errors_file = os.path.join(self.data_dir, self.storage_config.get('co2_errors_file', 'co2_errors.json'))
        self.errors_archive_file = os.path.join(self.data_dir, self.storage_config.get('co2_errors_archive_file', 'co2_errors_archive.json'))
        
        # 确保文件存在
        self._ensure_files_exist()
        
        self.logger.info("CO2控制器初始化完成")

    def _init_thread_manager(self):
        """初始化线程管理器"""
        try:
            from utils.thread_manager import get_thread_manager

            # 获取线程管理器实例
            self.thread_manager = get_thread_manager()

            self.logger.info("CO2控制器线程管理器初始化完成")

        except Exception as e:
            self.logger.warning(f"线程管理器初始化失败，将使用传统线程: {e}")
            self.thread_manager = None

    def _ensure_files_exist(self):
        """确保数据文件存在"""
        with self.file_operation_lock:
            # 只确保错误文件存在，状态数据现在使用独立文件存储
            for file_path in [self.errors_file, self.errors_archive_file]:
                if not os.path.exists(file_path):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump([], f)
                    self.logger.info(f"创建文件: {file_path}")

            # 确保pending和uploaded目录存在
            pending_dir = os.path.join(self.data_dir, 'pending')
            uploaded_dir = os.path.join(self.data_dir, 'uploaded')
            for directory in [pending_dir, uploaded_dir]:
                if not os.path.exists(directory):
                    os.makedirs(directory)
                    self.logger.info(f"创建目录: {directory}")
    
    def _init_gpio(self):
        """初始化GPIO引脚设置"""
        try:
            # 获取引脚配置
            pins = self.co2_config.get('pins', {})
            self.fan_pins = pins.get('fans', [13, 15])
            self.pump_pins = pins.get('pumps', [16, 21])
            self.heater_pin = pins.get('heater', 19)
            self.stepper_pins = pins.get('stepper', [22, 24, 26, 29])
            
            # 注意：GPIO模式现在由主程序统一设置，此处不再设置
            # GPIO.setmode(GPIO.BOARD)
            # GPIO.setwarnings(False)
            
            # 设置风扇引脚为输出
            for pin in self.fan_pins:
                GPIO.setup(pin, GPIO.OUT)
                GPIO.output(pin, GPIO.LOW)  # 初始状态为关闭
                
            # 设置水泵引脚为输出
            for pin in self.pump_pins:
                GPIO.setup(pin, GPIO.OUT)
                GPIO.output(pin, GPIO.LOW)  # 初始状态为关闭
            
            # 设置加热片引脚为输出
            GPIO.setup(self.heater_pin, GPIO.OUT)
            GPIO.output(self.heater_pin, GPIO.LOW)  # 初始状态为关闭
            
            # 设置步进电机引脚为输出
            for pin in self.stepper_pins:
                GPIO.setup(pin, GPIO.OUT)
                GPIO.output(pin, GPIO.LOW)  # 初始状态为关闭
                
            self.logger.info("GPIO引脚初始化完成")
            
        except Exception as e:
            self.logger.error(f"GPIO初始化失败: {e}", exc_info=True)
            self._handle_error("硬件初始化异常", f"GPIO初始化失败: {e}")
            self.state = CO2ControllerState.ERROR
    
    def start(self):
        """启动CO2控制器"""
        if self.running:
            self.logger.warning("CO2控制器已在运行中")
            return
            
        try:
            self.logger.info("启动CO2控制器...")
            
            # 先确保所有设备关闭
            self._all_devices_off(log_actions=True)
            self.logger.info("GPIO设备初始化完成，准备启动CO2控制循环")
            
            self.running = True

            # 重新获取线程管理器（确保获取到已启动的实例）
            self._init_thread_manager()

            # 初始化步进电机位置（先复位到左侧180度）
            self.logger.info("正在复位步进电机...")
            self._reset_stepper_motor()
            
            # 设置初始轮次为1
            self.current_round = 1
            
            # 获取当前轮次的吸附时长（分钟）
            timing_config = self.co2_config.get('timing', {})
            adsorption_config = timing_config.get('adsorption_duration', {})
            round_key = f"round{self.current_round}"
            duration_minutes = adsorption_config.get(round_key, 110)  # 默认110分钟
            
            # 设置剩余时间为完整的吸附时间
            self.phase_remaining = duration_minutes
            
            # 立即上报初始状态（吸附状态）
            self._save_status()
            
            # 优先使用线程管理器启动控制线程
            if self.thread_manager:
                from utils.thread_manager import TaskPriority

                self.control_future = self.thread_manager.submit_task(
                    name="co2_control_loop",
                    func=self._control_loop,
                    priority=TaskPriority.HIGH,
                    max_retries=3
                )

                self.logger.info("CO2控制任务已提交到线程管理器")
            else:
                # 回退到传统线程方式
                self.control_thread = threading.Thread(target=self._control_loop, daemon=True)
                self.control_thread.start()

                self.logger.info("CO2控制器已启动（传统线程模式）")
            
            # 通知MQTT客户端CO2控制器已就绪
            try:
                import builtins
                if hasattr(builtins, 'mqtt_client_instance'):
                    mqtt_client = getattr(builtins, 'mqtt_client_instance')
                    mqtt_client.set_component_ready("co2_controller", True)
            except Exception as e:
                self.logger.error(f"通知MQTT客户端CO2控制器就绪状态失败: {e}")
            
        except Exception as e:
            self.logger.error(f"CO2控制器启动失败: {e}", exc_info=True)
            self._handle_error("启动异常", f"CO2控制器启动失败: {e}")
            self.running = False
            self.state = CO2ControllerState.ERROR
    
    def stop(self):
        """停止CO2控制器"""
        if not self.running:
            return
            
        self.logger.info("CO2控制器停止中，准备关闭所有设备...")
        
        # 使用线程锁保护停止操作
        with threading.Lock():
            # 先设置停止标志，确保其他操作立即响应
            self.stopping = True
            self.running = False
            
            # 立即关闭所有设备，并只记录一次日志
            self._all_devices_off(log_actions=True)
        
        # 添加加强版等待循环
        max_wait = 5  # 最多等待5秒
        wait_interval = 0.2  # 每0.2秒检查一次
        start_time = time.time()
        
        # 等待控制线程结束
        if hasattr(self, 'control_future') and self.control_future:
            # 使用线程管理器的Future
            try:
                self.control_future.cancel()
                self.logger.info("已取消CO2控制任务")
            except Exception as e:
                self.logger.warning(f"取消CO2控制任务失败: {e}")
        elif hasattr(self, 'control_thread') and self.control_thread and self.control_thread.is_alive():
            # 传统线程模式
            self.logger.info("等待CO2控制线程停止...")
            while self.control_thread.is_alive() and time.time() - start_time < max_wait:
                # 不再重复关闭设备，只等待线程结束
                time.sleep(wait_interval)

            # 如果线程仍在运行，发出警告
            if self.control_thread.is_alive():
                self.logger.warning("CO2控制线程未能在预期时间内停止，将继续进行")
        
        # 确保状态一致性
        self.state = CO2ControllerState.ERROR
        self.current_round = 0
        self.phase_remaining = 0
        
        self.logger.info("CO2控制器已停止，所有CO2相关设备(风扇、水泵、加热片)已设置为安全状态")
        
        # 通知MQTT客户端CO2控制器已停止
        try:
            import builtins
            if hasattr(builtins, 'mqtt_client_instance'):
                mqtt_client = getattr(builtins, 'mqtt_client_instance')
                mqtt_client.set_component_ready("co2_controller", False)
        except Exception as e:
            self.logger.error(f"通知MQTT客户端CO2控制器停止状态失败: {e}")
    
    def _control_loop(self):
        """控制循环，管理CO2控制器的状态和各阶段运行"""
        self.logger.info("开始CO2控制循环...")
        
        try:
            while self.running:
                # 先检查是否处于停止中，如果是则立即退出循环
                if self.stopping:
                    self.logger.info("检测到停止信号，正在退出控制循环...")
                    break
                    
                # 根据当前状态执行相应的操作
                if self.state == CO2ControllerState.ADSORPTION:
                    # 执行吸附阶段
                    phase_completed = self._run_adsorption_phase()
                    
                    # 检查阶段是否正常完成，避免在停止时更改状态
                    if not phase_completed:
                        self.logger.info("吸附阶段未正常完成，可能是因为系统正在停止")
                        break
                    
                    # 再次检查是否处于停止中
                    if self.stopping or not self.running:
                        self.logger.info("吸附阶段完成后检测到停止信号，中断控制循环")
                        break
                    
                    # 检查是否是第4轮，第4轮只需要吸附阶段
                    if self.current_round == 4:
                        # 第4轮吸附完成后，直接进入下一轮
                        self.logger.info("第4轮吸附阶段完成，直接进入下一轮")
                        self.current_round = 1  # 重置为第一轮
                        self.state = CO2ControllerState.ADSORPTION
                        self._save_status()  # 保存状态变化
                        continue  # 跳过后续阶段，直接进入下一轮
                        
                    # 吸附阶段完成后，进入加热阶段
                    self.state = CO2ControllerState.HEATING
                    self._save_status()  # 保存状态变化
                    phase_completed = self._run_heating_phase()
                    
                    # 检查阶段是否正常完成
                    if not phase_completed:
                        break
                    
                elif self.state == CO2ControllerState.HEATING:
                    # 再次检查是否处于停止中
                    if self.stopping or not self.running:
                        self.logger.info("加热阶段开始前检测到停止信号，中断控制循环")
                        break
                        
                    # 加热阶段完成后，进入释放阶段
                    self.state = CO2ControllerState.RELEASE
                    self._save_status()  # 保存状态变化
                    phase_completed = self._run_release_phase()
                    
                    # 检查阶段是否正常完成
                    if not phase_completed:
                        break
                    
                elif self.state == CO2ControllerState.RELEASE:
                    # 再次检查是否处于停止中
                    if self.stopping or not self.running:
                        self.logger.info("释放阶段开始前检测到停止信号，中断控制循环")
                        break
                        
                    # 释放阶段完成后，更新轮回并进入下一轮吸附阶段
                    self.current_round += 1
                    if self.current_round > 4:
                        self.current_round = 1  # 重置为第一轮
                    
                    self.state = CO2ControllerState.ADSORPTION
                    self._save_status()  # 保存状态变化
                    phase_completed = self._run_adsorption_phase()
                    
                    # 检查阶段是否正常完成
                    if not phase_completed:
                        break
                    
                elif self.state == CO2ControllerState.ERROR:
                    # 错误状态，关闭所有设备并等待一段时间
                    self._all_devices_off(log_actions=False)  # 不重复记录日志
                    
                    # 更频繁地检查停止信号
                    for _ in range(60):  # 分成60次检查，每次1秒
                        if self.stopping or not self.running:
                            self.logger.info("错误状态中检测到停止信号，中断控制循环")
                            return  # 直接返回，结束循环
                        time.sleep(1)
                    
                    # 尝试重新初始化
                    if self.running and not self.stopping:
                        self._init_gpio()
                        if self.state != CO2ControllerState.ERROR:  # 如果初始化成功
                            self.state = CO2ControllerState.ADSORPTION
                            self._save_status()  # 保存状态变化
                
                # 短暂休眠，避免CPU占用过高
                time.sleep(0.1)  # 减少睡眠时间，提高响应性
                
        except Exception as e:
            self.logger.error(f"CO2控制循环发生错误: {e}", exc_info=True)
            self._handle_error("控制循环异常", f"CO2控制循环发生错误: {e}")
            self._all_devices_off(log_actions=False)  # 不重复记录日志
            self.state = CO2ControllerState.ERROR
            self._save_status()  # 保存错误状态
            
        # 循环结束时确保所有设备安全关闭
        self.logger.info("CO2控制循环已退出，确保所有设备安全关闭")
        self._all_devices_off(log_actions=False)  # 不重复记录日志
            
    def _run_adsorption_phase(self):
        """运行吸附阶段
        
        Returns:
            bool: 阶段是否正常完成，False表示因停止信号或错误而提前终止
        """
        try:
            # 先检查是否处于停止中
            if self.stopping or not self.running:
                self.logger.info("系统正在停止中，跳过吸附阶段")
                return False
                
            # 获取当前轮回的吸附时长（分钟）
            timing_config = self.co2_config.get('timing', {})
            adsorption_config = timing_config.get('adsorption_duration', {})
            
            # 根据当前轮回获取对应的时长
            round_key = f"round{self.current_round}"
            duration_minutes = adsorption_config.get(round_key, 110)  # 默认110分钟
            duration_seconds = duration_minutes * 60
            
            # 获取风扇切换间隔（分钟）
            fan_switch_interval_minutes = timing_config.get('fan_switch_interval', 20)
            fan_switch_interval_seconds = fan_switch_interval_minutes * 60
            
            self.logger.info(f"开始第{self.current_round}轮吸附阶段，时长: {duration_minutes}分钟")
            
            # 再次检查停止信号
            if self.stopping or not self.running:
                self.logger.info("系统正在停止中，中断吸附阶段初始化")
                return False
                
            # 步进电机旋转90度打开阀门
            try:
                self._rotate_stepper_motor_90_degrees()
            except Exception as e:
                self.logger.error(f"步进电机操作失败: {e}")
                if self.stopping or not self.running:
                    self.logger.info("在步进电机操作中检测到停止信号，中断吸附阶段")
                    return False
            
            # 记录开始时间
            start_time = time.time()
            end_time = start_time + duration_seconds
            last_switch_time = start_time
            active_fan = 0  # 当前激活的风扇索引
            
            # 更新剩余时间
            self.phase_remaining = duration_minutes
            
            # 打开第一个风扇（如果系统未停止）
            if not self.stopping and self.running:
                self._switch_fan(active_fan, True)
                self.logger.info(f"风扇{active_fan+1}开启，剩余时间: {self.phase_remaining}分钟")
                # 不需要在风扇切换时上报状态
            else:
                self.logger.info("系统正在停止中，跳过风扇开启")
                return False
            
            # 吸附阶段主循环
            check_interval = 0.2  # 检查间隔，单位秒，更快地响应停止信号
            next_check_time = time.time() + check_interval
            
            while time.time() < end_time:
                current_time = time.time()
                
                # 频繁检查停止信号
                if current_time >= next_check_time:
                    # 检查系统状态
                    if self.stopping or not self.running:
                        self.logger.info("检测到停止信号，中断吸附阶段")
                        # 关闭所有设备并退出
                        self._switch_fan(active_fan, False)
                        return False

                    # 检查状态是否变更
                    if self.state != CO2ControllerState.ADSORPTION:
                        self.logger.info(f"检测到状态变更为{self.state.name}，中断吸附阶段")
                        self._switch_fan(active_fan, False)
                        return False

                    # 更新线程活动时间，防止被误判为无响应
                    if self.thread_manager:
                        self.thread_manager.update_thread_activity()

                    next_check_time = current_time + check_interval
                
                # 检查是否需要切换风扇
                if current_time - last_switch_time >= fan_switch_interval_seconds:
                    # 在切换前再次检查停止信号
                    if self.stopping or not self.running:
                        self.logger.info("风扇切换前检测到停止信号，中断吸附阶段")
                        self._switch_fan(active_fan, False)
                        return False
                        
                    # 关闭当前风扇
                    self._switch_fan(active_fan, False)
                    
                    # 切换到另一个风扇
                    active_fan = 1 - active_fan  # 在0和1之间切换
                    
                    # 打开新的风扇
                    self._switch_fan(active_fan, True)
                    
                    # 更新剩余时间（分钟）
                    self.phase_remaining = int((end_time - current_time) / 60)
                    self.logger.info(f"风扇{active_fan+1}开启，剩余时间: {self.phase_remaining}分钟")
                    
                    # 不需要在风扇切换时上报状态
                    
                    last_switch_time = current_time
                
                # 更新剩余时间（分钟）
                self.phase_remaining = int((end_time - current_time) / 60)
                
                # 短暂休眠，避免CPU占用过高
                time.sleep(0.1)  # 减少睡眠时间，提高响应性
            
            # 阶段完成前的最终检查
            if self.stopping or not self.running:
                self.logger.info("吸附阶段即将完成时检测到停止信号")
                self._switch_fan(active_fan, False)
                return False
            
            # 关闭所有风扇
            for i in range(len(self.fan_pins)):
                self._switch_fan(i, False)
                
            self.logger.info(f"第{self.current_round}轮吸附阶段已完成")
            return True  # 表示正常完成
            
        except Exception as e:
            self.logger.error(f"吸附阶段运行错误: {e}", exc_info=True)
            self._handle_error("吸附阶段异常", f"吸附阶段运行错误: {e}")
            self._all_devices_off(log_actions=False)  # 使用无日志版本
            self.state = CO2ControllerState.ERROR
            self._save_status()  # 保存错误状态
            return False  # 表示因错误而终止
            
    def _run_heating_phase(self):
        """运行加热阶段
        
        Returns:
            bool: 阶段是否正常完成，False表示因停止信号或错误而提前终止
        """
        try:
            # 先检查是否处于停止中
            if self.stopping or not self.running:
                self.logger.info("系统正在停止中，跳过加热阶段")
                return False
            
            # 获取加热阶段时长（分钟）
            timing_config = self.co2_config.get('timing', {})
            heating_minutes = timing_config.get('heating_duration', 25)
            heating_seconds = heating_minutes * 60
            
            self.logger.info(f"开始第{self.current_round}轮加热阶段，时长: {heating_minutes}分钟")
            
            # 再次检查停止信号
            if self.stopping or not self.running:
                self.logger.info("系统正在停止中，中断加热阶段初始化")
                return False
            
            # 步进电机复位关闭阀门
            try:
                self._reset_stepper_motor()
            except Exception as e:
                self.logger.error(f"步进电机复位失败: {e}")
                if self.stopping or not self.running:
                    self.logger.info("在步进电机操作中检测到停止信号，中断加热阶段")
                    return False
            
            # 检查停止信号
            if self.stopping or not self.running:
                self.logger.info("步进电机复位后检测到停止信号，中断加热阶段")
                return False
            
            # 打开加热片
            self._switch_heater(True)
            
            # 记录开始时间
            start_time = time.time()
            end_time = start_time + heating_seconds
            
            # 更新剩余时间
            self.phase_remaining = heating_minutes
            self.logger.info(f"加热片开启，剩余时间: {self.phase_remaining}分钟")
            
            # 加热阶段主循环
            check_interval = 0.2  # 检查间隔，单位秒，更快地响应停止信号
            next_check_time = time.time() + check_interval
            
            while time.time() < end_time:
                current_time = time.time()
                
                # 频繁检查停止信号
                if current_time >= next_check_time:
                    # 检查系统状态
                    if self.stopping or not self.running:
                        self.logger.info("检测到停止信号，中断加热阶段")
                        # 关闭加热片并退出
                        self._switch_heater(False)
                        return False
                        
                    # 检查状态是否变更
                    if self.state != CO2ControllerState.HEATING:
                        self.logger.info(f"检测到状态变更为{self.state.name}，中断加热阶段")
                        self._switch_heater(False)
                        return False

                    # 更新线程活动时间，防止被误判为无响应
                    if self.thread_manager:
                        self.thread_manager.update_thread_activity()

                    next_check_time = current_time + check_interval
                
                # 更新剩余时间（分钟）
                self.phase_remaining = int((end_time - current_time) / 60)
                
                # 短暂休眠，避免CPU占用过高
                time.sleep(0.1)  # 减少睡眠时间，提高响应性
            
            # 阶段完成前的最终检查
            if self.stopping or not self.running:
                self.logger.info("加热阶段即将完成时检测到停止信号")
                self._switch_heater(False)
                return False
                
            self.logger.info(f"第{self.current_round}轮加热阶段已完成")
            
            # 注意：在正常情况下不关闭加热片，因为释放阶段也需要使用
            # 但如果检测到停止信号，则应在返回前关闭
            return True
            
        except Exception as e:
            self.logger.error(f"加热阶段运行错误: {e}", exc_info=True)
            self._handle_error("加热阶段异常", f"加热阶段运行错误: {e}")
            self._all_devices_off(log_actions=False)  # 使用无日志版本
            self.state = CO2ControllerState.ERROR
            self._save_status()  # 保存错误状态
            return False  # 表示因错误而终止
    
    def _run_release_phase(self):
        """运行释放阶段
        
        Returns:
            bool: 阶段是否正常完成，False表示因停止信号或错误而提前终止
        """
        try:
            # 先检查是否处于停止中
            if self.stopping or not self.running:
                self.logger.info("系统正在停止中，跳过释放阶段")
                return False
                
            # 获取释放阶段时长和水泵切换间隔（分钟）
            timing_config = self.co2_config.get('timing', {})
            release_minutes = timing_config.get('release_duration', 55)
            release_seconds = release_minutes * 60
            
            pump_switch_interval_minutes = timing_config.get('pump_switch_interval', 2)
            pump_switch_interval_seconds = pump_switch_interval_minutes * 60
            
            self.logger.info(f"开始第{self.current_round}轮释放阶段，时长: {release_minutes}分钟")
            
            # 再次检查停止信号
            if self.stopping or not self.running:
                self.logger.info("系统正在停止中，中断释放阶段初始化")
                return False
                
            # 记录开始时间
            start_time = time.time()
            end_time = start_time + release_seconds
            last_switch_time = start_time
            active_pump = 0  # 当前激活的水泵索引
            
            # 更新剩余时间
            self.phase_remaining = release_minutes
            
            # 检查停止信号
            if self.stopping or not self.running:
                self.logger.info("准备开启设备前检测到停止信号，中断释放阶段")
                return False
                
            # 确保加热片开启（从加热阶段继续）
            self._switch_heater(True)
            
            # 再次检查停止信号
            if self.stopping or not self.running:
                self.logger.info("加热片开启后检测到停止信号，中断释放阶段")
                self._switch_heater(False)
                return False
                
            # 打开第一个水泵
            self._switch_pump(active_pump, True)
            self.logger.info(f"水泵{active_pump+1}开启，剩余时间: {self.phase_remaining}分钟")
            
            # 释放阶段主循环
            check_interval = 0.2  # 检查间隔，单位秒，更快地响应停止信号
            next_check_time = time.time() + check_interval
            
            while time.time() < end_time:
                current_time = time.time()
                
                # 频繁检查停止信号
                if current_time >= next_check_time:
                    # 检查系统状态
                    if self.stopping or not self.running:
                        self.logger.info("检测到停止信号，中断释放阶段")
                        # 关闭当前活跃的水泵和加热片
                        self._switch_pump(active_pump, False)
                        self._switch_heater(False)
                        return False
                        
                    # 检查状态是否变更
                    if self.state != CO2ControllerState.RELEASE:
                        self.logger.info(f"检测到状态变更为{self.state.name}，中断释放阶段")
                        self._switch_pump(active_pump, False)
                        self._switch_heater(False)
                        return False

                    # 更新线程活动时间，防止被误判为无响应
                    if self.thread_manager:
                        self.thread_manager.update_thread_activity()

                    next_check_time = current_time + check_interval
                
                # 检查是否需要切换水泵
                if current_time - last_switch_time >= pump_switch_interval_seconds:
                    # 在切换前再次检查停止信号
                    if self.stopping or not self.running:
                        self.logger.info("水泵切换前检测到停止信号，中断释放阶段")
                        self._switch_pump(active_pump, False)
                        self._switch_heater(False)
                        return False
                    
                    # 关闭当前水泵
                    self._switch_pump(active_pump, False)
                    
                    # 切换到另一个水泵
                    active_pump = 1 - active_pump  # 在0和1之间切换
                    
                    # 打开新的水泵
                    self._switch_pump(active_pump, True)
                    
                    # 更新剩余时间（分钟）
                    self.phase_remaining = int((end_time - current_time) / 60)
                    self.logger.info(f"水泵{active_pump+1}开启，剩余时间: {self.phase_remaining}分钟")
                    
                    last_switch_time = current_time
                
                # 更新剩余时间（分钟）
                self.phase_remaining = int((end_time - current_time) / 60)
                
                # 短暂休眠，避免CPU占用过高
                time.sleep(0.1)  # 减少睡眠时间，提高响应性
            
            # 阶段完成前的最终检查
            if self.stopping or not self.running:
                self.logger.info("释放阶段即将完成时检测到停止信号")
                self._switch_pump(active_pump, False)
                self._switch_heater(False)
                return False
            
            # 关闭所有水泵和加热片
            for i in range(len(self.pump_pins)):
                self._switch_pump(i, False)
            self._switch_heater(False)
                
            self.logger.info(f"第{self.current_round}轮释放阶段已完成")
            return True  # 表示正常完成
            
        except Exception as e:
            self.logger.error(f"释放阶段运行错误: {e}", exc_info=True)
            self._handle_error("释放阶段异常", f"释放阶段运行错误: {e}")
            self._all_devices_off(log_actions=False)  # 使用无日志版本
            self.state = CO2ControllerState.ERROR
            self._save_status()  # 保存错误状态
            return False  # 表示因错误而终止
    
    def _reset_stepper_motor(self):
        """步进电机复位（向左旋转180度）"""
        try:
            self.logger.info("步进电机复位（向左旋转180度）...")
            # 实际控制代码需要根据步进电机的具体型号和连接方式编写
            # 这里是简化的示例
            step_count = 200  # 对应180度的步数，根据实际电机调整
            
            for _ in range(step_count):
                # 控制步进电机向左旋转1步
                for pin_index in range(4):
                    GPIO.output(self.stepper_pins[pin_index], GPIO.HIGH)
                    time.sleep(0.002)
                    GPIO.output(self.stepper_pins[pin_index], GPIO.LOW)
            
            self.logger.info("步进电机复位完成")
            
        except Exception as e:
            self.logger.error(f"步进电机复位失败: {e}")
            self._handle_error("步进电机异常", f"步进电机复位失败: {e}")
            raise
    
    def _rotate_stepper_motor_90_degrees(self):
        """步进电机旋转90度打开阀门"""
        try:
            self.logger.info("步进电机旋转90度打开阀门...")
            # 实际控制代码需要根据步进电机的具体型号和连接方式编写
            # 这里是简化的示例
            step_count = 100  # 对应90度的步数，根据实际电机调整
            
            for _ in range(step_count):
                # 控制步进电机旋转1步
                for pin_index in range(4):
                    GPIO.output(self.stepper_pins[3 - pin_index], GPIO.HIGH)  # 反向旋转
                    time.sleep(0.002)
                    GPIO.output(self.stepper_pins[3 - pin_index], GPIO.LOW)
            
            self.logger.info("步进电机旋转完成")
            
        except Exception as e:
            self.logger.error(f"步进电机旋转失败: {e}")
            self._handle_error("步进电机异常", f"步进电机旋转失败: {e}")
            raise
    
    def _switch_fan(self, fan_index, on):
        """控制风扇开关
        
        Args:
            fan_index: 风扇索引 (0或1)
            on: True表示开启，False表示关闭
        """
        try:
            # 检查是否在停止过程中，如果是，忽略所有开启操作
            if self.stopping and on:
                self.logger.warning(f"系统正在停止中，忽略风扇{fan_index}开启请求")
                return
                
            if fan_index < 0 or fan_index >= len(self.fan_pins):
                self.logger.error(f"无效的风扇索引: {fan_index}")
                return
                
            pin = self.fan_pins[fan_index]
            if on:
                # 再次检查停止状态，确保在设置GPIO前没有停止信号
                if self.stopping:
                    self.logger.warning(f"系统已开始停止过程，取消风扇{fan_index}开启操作")
                    return
                GPIO.output(pin, GPIO.HIGH)
                self.logger.info(f"开启风扇 {fan_index} (PIN: {pin})")
            else:
                # 检查当前状态，避免重复关闭
                current_state = GPIO.input(pin)
                if current_state == GPIO.LOW:
                    # 已经是关闭状态，不重复记录日志
                    return
                GPIO.output(pin, GPIO.LOW)
                self.logger.info(f"关闭风扇 {fan_index} (PIN: {pin})")
                
        except Exception as e:
            self.logger.error(f"控制风扇失败: {e}")
            self._handle_error("硬件控制异常", f"风扇控制失败: GPIO操作错误, {e}")
    
    def _switch_pump(self, pump_index, on):
        """控制水泵开关
        
        Args:
            pump_index: 水泵索引 (0或1)
            on: True表示开启，False表示关闭
        """
        try:
            # 检查是否在停止过程中，如果是，忽略所有开启操作
            if self.stopping and on:
                self.logger.warning(f"系统正在停止中，忽略水泵{pump_index}开启请求")
                return
                
            if pump_index < 0 or pump_index >= len(self.pump_pins):
                self.logger.error(f"无效的水泵索引: {pump_index}")
                return
                
            pin = self.pump_pins[pump_index]
            if on:
                # 再次检查停止状态，确保在设置GPIO前没有停止信号
                if self.stopping:
                    self.logger.warning(f"系统已开始停止过程，取消水泵{pump_index}开启操作")
                    return
                GPIO.output(pin, GPIO.HIGH)
                self.logger.info(f"开启水泵 {pump_index + 1} (PIN: {pin})")
            else:
                # 检查当前状态，避免重复关闭
                current_state = GPIO.input(pin)
                if current_state == GPIO.LOW:
                    # 已经是关闭状态，不重复记录日志
                    return
                GPIO.output(pin, GPIO.LOW)
                self.logger.info(f"关闭水泵 {pump_index + 1} (PIN: {pin})")
                
        except Exception as e:
            self.logger.error(f"控制水泵失败: {e}")
            self._handle_error("硬件控制异常", f"水泵控制失败: GPIO操作错误, {e}")
    
    def _switch_heater(self, on):
        """控制加热片开关
        
        Args:
            on: True表示开启，False表示关闭
        """
        try:
            # 检查是否在停止过程中，如果是，忽略所有开启操作
            if self.stopping and on:
                self.logger.warning("系统正在停止中，忽略加热片开启请求")
                return
                
            if on:
                # 再次检查停止状态，确保在设置GPIO前没有停止信号
                if self.stopping:
                    self.logger.warning("系统已开始停止过程，取消加热片开启操作")
                    return
                    
                # 检查运行状态
                if not self.running:
                    self.logger.warning("系统已停止运行，忽略加热片开启请求")
                    return
                    
                GPIO.output(self.heater_pin, GPIO.HIGH)
                self.logger.info(f"开启加热片 (PIN: {self.heater_pin})")
            else:
                # 检查当前状态，避免重复关闭
                current_state = GPIO.input(self.heater_pin)
                if current_state == GPIO.LOW:
                    # 已经是关闭状态，不重复记录日志
                    return
                GPIO.output(self.heater_pin, GPIO.LOW)
                self.logger.info(f"关闭加热片 (PIN: {self.heater_pin})")
                
        except Exception as e:
            self.logger.error(f"控制加热片失败: {e}")
            self._handle_error("硬件控制异常", f"加热片控制失败: GPIO操作错误, {e}")
    
    def _all_devices_off(self, log_actions=True):
        """关闭所有设备
        
        Args:
            log_actions: 是否记录设备关闭的详细日志，默认为True
        """
        # 用于避免重复日志的临时标志
        original_logging = self.logger.getEffectiveLevel()
        
        if not log_actions:
            # 临时提高日志级别，只记录警告和错误
            self.logger.setLevel(logging.WARNING)
            
        try:
            # 关闭所有风扇
            for i in range(len(self.fan_pins)):
                self._switch_fan(i, False)
            
            # 关闭所有水泵
            for i in range(len(self.pump_pins)):
                self._switch_pump(i, False)
            
            # 关闭加热片
            self._switch_heater(False)
        finally:
            # 恢复原始日志级别
            if not log_actions:
                self.logger.setLevel(original_logging)
        
        # 只记录一次总结性日志，使用更清晰的描述
        self.logger.info("已将所有设备(风扇、水泵、加热片)设置为关闭状态")
    
    def _save_status(self):
        """保存CO2控制器状态为独立文件"""
        try:
            # 检查设备ID，如果为空则尝试从MQTT客户端获取
            if not self.device_id:
                try:
                    import builtins
                    if hasattr(builtins, 'mqtt_client_instance'):
                        mqtt_client = getattr(builtins, 'mqtt_client_instance')
                        if mqtt_client.device_id:
                            self.device_id = mqtt_client.device_id
                            self.logger.info(f"从MQTT客户端获取设备ID: {self.device_id}")
                except Exception as e:
                    self.logger.warning(f"尝试获取设备ID失败: {e}")

            # 确保剩余时间计算正确
            if self.state == CO2ControllerState.ADSORPTION:
                # 获取当前轮次的吸附时长（分钟）
                timing_config = self.co2_config.get('timing', {})
                adsorption_config = timing_config.get('adsorption_duration', {})
                round_key = f"round{self.current_round}"
                duration_minutes = adsorption_config.get(round_key, 110)  # 默认110分钟

                # 如果剩余时间未设置，则设置为完整的吸附时间
                if self.phase_remaining <= 0:
                    self.phase_remaining = duration_minutes

            # 构造状态数据
            current_timestamp = int(time.time())

            status_data = {
                "data": {
                    "status": self.state.value,
                    "round": self.current_round,
                    "phase_remaining": self.phase_remaining
                },
                "time": current_timestamp,
                "devid": self.device_id,
                "ver": "1.0",
                "uploaded": False  # 添加上传标志
            }

            # 确保pending目录存在
            pending_dir = os.path.join(self.data_dir, 'pending')
            if not os.path.exists(pending_dir):
                os.makedirs(pending_dir)
                self.logger.info(f"创建目录: {pending_dir}")

            # 使用时间戳作为文件名
            file_path = os.path.join(pending_dir, f"co2_{current_timestamp}.json")

            # 检查是否已存在同名文件(防止重复)
            if os.path.exists(file_path):
                self.logger.warning(f"跳过重复CO2状态保存: 时间戳={current_timestamp} 文件已存在")
                return

            # 保存为独立的JSON文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"保存CO2状态: 时间戳={current_timestamp}, 状态={self.state.name}, 轮次={self.current_round}, 剩余时间={self.phase_remaining}分钟")

            # 添加：标记CO2状态需要立即上传
            try:
                import builtins
                if hasattr(builtins, 'mqtt_client_instance'):
                    mqtt_client = getattr(builtins, 'mqtt_client_instance')
                    mqtt_client.mark_co2_status_for_immediate_upload()
                    self.logger.info("已标记CO2状态需要立即上传")
            except Exception as e:
                self.logger.error(f"标记CO2状态立即上传失败: {e}")

        except Exception as e:
            self.logger.error(f"保存CO2控制器状态失败: {e}", exc_info=True)
    
    def _handle_error(self, error_type, error_message):
        """处理CO2控制器错误
        
        Args:
            error_type: 错误类型
            error_message: 错误消息
        """
        try:
            self.logger.error(f"CO2控制器错误: {error_type} - {error_message}")
            
            # 检查设备ID，如果为空则尝试从MQTT客户端获取
            if not self.device_id:
                try:
                    import builtins
                    if hasattr(builtins, 'mqtt_client_instance'):
                        mqtt_client = getattr(builtins, 'mqtt_client_instance')
                        if mqtt_client.device_id:
                            self.device_id = mqtt_client.device_id
                            self.logger.info(f"从MQTT客户端获取设备ID: {self.device_id}")
                except Exception as e:
                    self.logger.warning(f"尝试获取设备ID失败: {e}")
            
            # 构造错误数据
            error_data = {
                "time": int(time.time()),
                "datetime": time.strftime('%Y-%m-%d %H:%M:%S'),
                "device_id": self.device_id,
                "sensor_type": "co2_controller",
                "error_type": error_type,
                "error_message": error_message,
                "uploaded": False
            }
            
            # 保存到错误文件
            current_errors = []
            try:
                with open(self.errors_file, 'r', encoding='utf-8') as f:
                    current_errors = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                current_errors = []
                
            current_errors.append(error_data)
            with open(self.errors_file, 'w', encoding='utf-8') as f:
                json.dump(current_errors, f, indent=2, ensure_ascii=False)
            
            # 尝试通过MQTT上传错误
            try:
                import builtins
                if hasattr(builtins, 'mqtt_client_instance'):
                    mqtt_client = getattr(builtins, 'mqtt_client_instance')
                    # 直接调用publish_co2_error方法，不再检查连接状态
                    # MQTT客户端会自行处理连接状态和队列缓存
                    mqtt_client.publish_co2_error(error_data)
            except Exception as e:
                self.logger.error(f"通过MQTT上传CO2控制器错误失败: {e}")
                
        except Exception as e:
            self.logger.error(f"处理CO2控制器错误时发生错误: {e}", exc_info=True)
    
    def _turn_on_fan(self, fan_index):
        """打开指定风扇
        
        Args:
            fan_index: 风扇索引 (1或2)
        """
        try:
            pin = self.fan_pins[fan_index - 1]
            GPIO.output(pin, GPIO.HIGH)
            self.logger.info(f"开启风扇 {fan_index} (PIN: {pin})")
        except Exception as e:
            self.logger.error(f"打开风扇 {fan_index} 失败: {e}")
            self._handle_error(f"打开风扇 {fan_index} 失败", str(e))

    def _turn_off_fan(self, fan_index):
        """关闭指定风扇
        
        Args:
            fan_index: 风扇索引 (1或2)
        """
        try:
            pin = self.fan_pins[fan_index - 1]
            GPIO.output(pin, GPIO.LOW)
            self.logger.info(f"关闭风扇 {fan_index} (PIN: {pin})")
        except Exception as e:
            self.logger.error(f"关闭风扇 {fan_index} 失败: {e}")
            self._handle_error(f"关闭风扇 {fan_index} 失败", str(e)) 