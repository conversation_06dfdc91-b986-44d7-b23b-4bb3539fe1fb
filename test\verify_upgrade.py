#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试系统升级验证脚本
验证测试系统升级是否成功，所有新功能是否正常工作
"""

import os
import sys
import json
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (不存在)")
        return False

def check_directory_structure():
    """检查目录结构"""
    print_header("检查目录结构")
    
    required_structure = {
        # 新增的v2测试接口
        "test/api/v2/__init__.py": "API v2.0 初始化文件",
        "test/api/v2/test_server_v2.py": "API v2.0 测试服务器",
        
        # 设备绑定测试
        "test/api/device_binding/__init__.py": "设备绑定测试初始化",
        "test/api/device_binding/binding_test_suite.py": "设备绑定测试套件",
        
        # 压力测试
        "test/api/stress_testing/__init__.py": "压力测试初始化",
        "test/api/stress_testing/stress_test_runner.py": "压力测试运行器",
        
        # 测试环境配置
        "test/environments/__init__.py": "测试环境初始化",
        "test/environments/offline/config.yaml": "离线测试环境配置",
        
        # 数据管理
        "test/data/__init__.py": "测试数据管理初始化",
        "test/data/cleanup/auto_cleanup.py": "自动数据清理脚本",
        
        # 集成测试和性能测试
        "test/local/integration/__init__.py": "集成测试初始化",
        "test/local/performance/__init__.py": "性能测试初始化",
        
        # 文档
        "test/docs/__init__.py": "测试文档初始化",
        "test/docs/api_v2.md": "API v2.0 文档",
        
        # 主要脚本
        "test/run_all_tests.py": "测试套件总运行器",
        "test/start_test_suite.py": "测试套件启动脚本",
        "test/verify_upgrade.py": "升级验证脚本"
    }
    
    success_count = 0
    total_count = len(required_structure)
    
    for file_path, description in required_structure.items():
        if check_file_exists(file_path, description):
            success_count += 1
    
    print(f"\n📊 目录结构检查结果: {success_count}/{total_count} 通过")
    return success_count == total_count

def check_python_imports():
    """检查Python模块导入"""
    print_header("检查Python模块导入")
    
    test_imports = [
        ("test.api.v2.test_server_v2", "API v2.0 测试服务器"),
        ("test.api.device_binding.binding_test_suite", "设备绑定测试套件"),
        ("test.api.stress_testing.stress_test_runner", "压力测试运行器"),
        ("test.data.cleanup.auto_cleanup", "自动数据清理"),
        ("test.run_all_tests", "测试套件总运行器")
    ]
    
    success_count = 0
    total_count = len(test_imports)
    
    for module_name, description in test_imports:
        try:
            # 临时添加路径
            if os.path.dirname(__file__) not in sys.path:
                sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))
            
            __import__(module_name)
            print(f"✅ {description}: {module_name}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {description}: {module_name} (导入失败: {e})")
        except Exception as e:
            print(f"⚠️ {description}: {module_name} (其他错误: {e})")
    
    print(f"\n📊 模块导入检查结果: {success_count}/{total_count} 通过")
    return success_count >= total_count * 0.8  # 80%通过即可

def check_configuration_files():
    """检查配置文件"""
    print_header("检查配置文件")
    
    config_files = [
        "test/environments/offline/config.yaml"
    ]
    
    success_count = 0
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "offline" in content and "test_environment" in content:
                        print(f"✅ 配置文件有效: {config_file}")
                        success_count += 1
                    else:
                        print(f"⚠️ 配置文件格式可能有问题: {config_file}")
            except Exception as e:
                print(f"❌ 读取配置文件失败: {config_file} ({e})")
        else:
            print(f"❌ 配置文件不存在: {config_file}")
    
    print(f"\n📊 配置文件检查结果: {success_count}/{len(config_files)} 通过")
    return success_count == len(config_files)

def check_backward_compatibility():
    """检查向后兼容性"""
    print_header("检查向后兼容性")
    
    # 检查原有文件是否仍然存在
    original_files = [
        "test/api/test_server.py",
        "test/api/test_examples.py",
        "test/local/memory_stress_test.py",
        "test/local/file_io_stress_test.py",
        "test/mosquito_test_generator.py",
        "test/README.md"
    ]
    
    success_count = 0
    
    for file_path in original_files:
        if check_file_exists(file_path, f"原有文件"):
            success_count += 1
    
    print(f"\n📊 向后兼容性检查结果: {success_count}/{len(original_files)} 通过")
    return success_count >= len(original_files) * 0.9  # 90%通过即可

def check_main_program_integration():
    """检查主程序集成"""
    print_header("检查主程序集成")
    
    try:
        # 检查main.py是否包含v2测试服务器支持
        with open("main.py", 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        checks = [
            ("test_server_v2", "v2测试服务器导入"),
            ("run_test_server_v2", "v2测试服务器启动"),
            ("v2_port", "v2端口配置")
        ]
        
        success_count = 0
        for check_str, description in checks:
            if check_str in main_content:
                print(f"✅ {description}: 已集成")
                success_count += 1
            else:
                print(f"❌ {description}: 未找到")
        
        print(f"\n📊 主程序集成检查结果: {success_count}/{len(checks)} 通过")
        return success_count >= len(checks) * 0.8
        
    except Exception as e:
        print(f"❌ 检查主程序失败: {e}")
        return False

def generate_verification_report():
    """生成验证报告"""
    print_header("生成验证报告")
    
    # 运行所有检查
    checks = [
        ("目录结构", check_directory_structure),
        ("Python模块导入", check_python_imports),
        ("配置文件", check_configuration_files),
        ("向后兼容性", check_backward_compatibility),
        ("主程序集成", check_main_program_integration)
    ]
    
    results = {}
    passed_checks = 0
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results[check_name] = {"status": "passed" if result else "failed", "result": result}
            if result:
                passed_checks += 1
        except Exception as e:
            results[check_name] = {"status": "error", "error": str(e)}
    
    # 计算总体结果
    total_checks = len(checks)
    success_rate = passed_checks / total_checks * 100
    overall_status = "success" if success_rate >= 80 else "failed"
    
    # 生成报告
    report = {
        "timestamp": str(Path(__file__).stat().st_mtime),
        "overall_status": overall_status,
        "success_rate": success_rate,
        "passed_checks": passed_checks,
        "total_checks": total_checks,
        "detailed_results": results
    }
    
    # 保存报告
    report_dir = "test/reports"
    os.makedirs(report_dir, exist_ok=True)
    
    report_file = os.path.join(report_dir, "upgrade_verification_report.json")
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print(f"✅ 验证报告已保存: {report_file}")
    except Exception as e:
        print(f"❌ 保存验证报告失败: {e}")
    
    return report

def print_summary(report):
    """打印总结"""
    print_header("升级验证总结")
    
    status_emoji = "🎉" if report["overall_status"] == "success" else "😞"
    status_text = "成功" if report["overall_status"] == "success" else "失败"
    
    print(f"{status_emoji} 升级验证状态: {status_text}")
    print(f"📊 成功率: {report['success_rate']:.1f}%")
    print(f"✅ 通过检查: {report['passed_checks']}/{report['total_checks']}")
    
    print("\n📋 详细结果:")
    for check_name, result in report["detailed_results"].items():
        status = result["status"]
        if status == "passed":
            print(f"  ✅ {check_name}: 通过")
        elif status == "failed":
            print(f"  ❌ {check_name}: 失败")
        else:
            print(f"  ⚠️ {check_name}: 错误 - {result.get('error', '未知错误')}")
    
    if report["overall_status"] == "success":
        print("\n🎯 升级成功！新的测试系统已就绪")
        print("\n📖 使用指南:")
        print("  1. 启动测试套件: python test/start_test_suite.py")
        print("  2. 运行完整测试: python test/run_all_tests.py")
        print("  3. 查看文档: test/docs/api_v2.md")
    else:
        print("\n⚠️ 升级验证未完全通过，请检查失败的项目")
        print("建议重新运行升级脚本或手动修复问题")

def main():
    """主函数"""
    print("🚀 开始测试系统升级验证")
    
    # 生成验证报告
    report = generate_verification_report()
    
    # 打印总结
    print_summary(report)
    
    # 返回退出码
    sys.exit(0 if report["overall_status"] == "success" else 1)

if __name__ == "__main__":
    main()
