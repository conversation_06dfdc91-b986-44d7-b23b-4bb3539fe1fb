# 设备绑定业务流程图

## 概述

本文档详细描述了设备绑定系统的业务流程，包含完整的流程图、时序图、状态图等，帮助开发和测试人员理解业务范畴。

## 1. 核心业务流程图

```mermaid
flowchart TD
    Start([设备启动绑定流程]) --> A[构建绑定请求]
    A --> B[计算请求签名]
    B --> C[发送HTTP请求]
    C --> D[服务端接收请求]
    
    D --> E{参数验证}
    E -->|失败| E1[记录验证失败日志]
    E1 --> E2[返回400参数错误]
    E2 --> End1([绑定失败])
    
    E -->|成功| F{签名验证}
    F -->|失败| F1[记录签名失败日志]
    F1 --> F2[返回400签名错误]
    F2 --> End2([绑定失败])
    
    F -->|成功| G{设备唯一性检查}
    G -->|已存在| G1[记录设备已绑定日志]
    G1 --> G2[返回422设备已绑定]
    G2 --> End3([绑定失败])
    
    G -->|不存在| H[创建设备记录]
    H --> I{创建是否成功}
    I -->|失败| I1[记录创建失败日志]
    I1 --> I2[返回500创建失败]
    I2 --> End4([绑定失败])
    
    I -->|成功| J[注册MQTT凭证]
    J --> K{MQTT注册是否成功}
    K -->|失败| K1[记录MQTT失败日志]
    K1 --> K2[返回500MQTT失败]
    K2 --> End5([绑定失败])
    
    K -->|成功| L[生成绑定响应]
    L --> M[返回200成功响应]
    M --> End6([绑定成功])
    
    %% 样式定义
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef decision fill:#fff8e1,stroke:#e65100,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef log fill:#f1f8e9,stroke:#558b2f,stroke-width:1px
    
    class Start,End1,End2,End3,End4,End5,End6 startEnd
    class A,B,C,D,H,J,L process
    class E,F,G,I,K decision
    class M,End6 success
    class E2,F2,G2,I2,K2,End1,End2,End3,End4,End5 error
    class E1,F1,G1,I1,K1 log
```

## 2. 系统组件交互时序图

```mermaid
sequenceDiagram
    participant D as IoT设备
    participant H as Handler层
    participant L as Logic层
    participant DB as MySQL数据库
    participant MQ as 阿里云MQTT
    participant LOG as 日志系统
    
    Note over D,LOG: 设备绑定完整流程
    
    D->>H: 1. POST /api/v1/device/bind
    Note right of D: 包含签名的设备信息
    
    H->>LOG: 2. 记录"接收设备请求"
    H->>H: 3. 参数验证
    
    alt 参数验证失败
        H->>LOG: 记录"参数验证失败"
        H->>D: 返回400参数错误
    else 参数验证成功
        H->>LOG: 记录"参数验证成功"
        H->>H: 4. 签名验证
        
        alt 签名验证失败
            H->>LOG: 记录"签名验证失败"
            H->>D: 返回400签名错误
        else 签名验证成功
            H->>LOG: 记录"签名验证成功"
            H->>L: 5. 调用绑定逻辑
            
            L->>LOG: 记录"检查设备唯一性"
            L->>DB: 6. 查询设备是否存在
            DB-->>L: 返回查询结果
            
            alt 设备已存在
                L->>LOG: 记录"设备已绑定"
                L-->>H: 返回设备已绑定错误
                H->>D: 返回422设备已绑定
            else 设备不存在
                L->>LOG: 记录"创建设备"
                L->>DB: 7. 创建设备记录
                DB-->>L: 返回设备ID
                L->>LOG: 记录"创建设备成功"
                
                L->>LOG: 记录"注册MQTT凭证"
                L->>MQ: 8. 申请设备凭证
                MQ-->>L: 返回MQTT凭证
                L->>LOG: 记录"MQTT注册成功"
                
                L->>DB: 9. 更新设备MQTT信息
                L->>LOG: 记录"生成响应"
                L-->>H: 返回绑定成功结果
                H->>D: 返回200成功响应
            end
        end
    end
```

## 3. 设备绑定状态机图

```mermaid
stateDiagram-v2
    [*] --> 未绑定: 设备初始状态
    
    未绑定 --> 请求中: 发起绑定请求
    请求中 --> 参数验证中: 开始验证
    
    参数验证中 --> 参数错误: 验证失败
    参数验证中 --> 签名验证中: 验证成功
    
    签名验证中 --> 签名错误: 验证失败
    签名验证中 --> 设备检查中: 验证成功
    
    设备检查中 --> 设备冲突: 设备已存在
    设备检查中 --> 设备创建中: 设备不存在
    
    设备创建中 --> 创建失败: 创建失败
    设备创建中 --> MQTT注册中: 创建成功
    
    MQTT注册中 --> MQTT失败: 注册失败
    MQTT注册中 --> 已绑定: 注册成功
    
    %% 错误状态回到初始状态
    参数错误 --> 未绑定: 可重试
    签名错误 --> 未绑定: 可重试
    设备冲突 --> 已绑定: 设备已存在
    创建失败 --> 未绑定: 可重试
    MQTT失败 --> 未绑定: 可重试
    
    已绑定 --> [*]: 绑定完成
    
    note right of 已绑定: 设备获得:\n- 设备ID\n- MQTT配置\n- 连接凭证
    note right of 参数错误: HTTP 400\n参数格式错误
    note right of 签名错误: HTTP 400\n签名验证失败
    note right of 设备冲突: HTTP 422\n设备已绑定
    note right of 创建失败: HTTP 500\n系统错误
    note right of MQTT失败: HTTP 500\n MQTT服务错误
```

## 4. 数据流图

```mermaid
graph TB
    subgraph "输入数据"
        A1[device_unique_id]
        A2[device_type]
        A3[mac_address]
        A4[serial_number]
        A5[timestamp]
        A6[nonce]
        A7[signature]
    end
    
    subgraph "验证层"
        B1[参数验证器]
        B2[签名验证器]
        B3[时间戳验证器]
    end
    
    subgraph "业务层"
        C1[设备管理器]
        C2[MQTT管理器]
        C3[日志管理器]
    end
    
    subgraph "数据层"
        D1[(设备表)]
        D2[(绑定日志表)]
        D3[阿里云MQTT]
    end
    
    subgraph "输出数据"
        E1[device_id]
        E2[mqtt_config]
        E3[error_message]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    A5 --> B3
    A6 --> B2
    A7 --> B2
    
    B1 --> C1
    B2 --> C1
    B3 --> C1
    
    C1 --> D1
    C1 --> C2
    C1 --> C3
    C2 --> D3
    C3 --> D2
    
    D1 --> E1
    D3 --> E2
    C1 --> E3
    
    %% 样式
    classDef inputClass fill:#e3f2fd,stroke:#1976d2
    classDef validateClass fill:#f3e5f5,stroke:#7b1fa2
    classDef businessClass fill:#e8f5e8,stroke:#388e3c
    classDef dataClass fill:#fff3e0,stroke:#f57c00
    classDef outputClass fill:#fce4ec,stroke:#c2185b
    
    class A1,A2,A3,A4,A5,A6,A7 inputClass
    class B1,B2,B3 validateClass
    class C1,C2,C3 businessClass
    class D1,D2,D3 dataClass
    class E1,E2,E3 outputClass
```

## 5. 错误处理流程图

```mermaid
flowchart TD
    Error[发生错误] --> Type{错误类型}
    
    Type -->|参数错误| Param[参数验证失败]
    Type -->|签名错误| Sign[签名验证失败]
    Type -->|业务错误| Business[业务逻辑错误]
    Type -->|系统错误| System[系统异常]
    
    Param --> Log1[记录参数错误日志]
    Sign --> Log2[记录签名错误日志]
    Business --> Log3[记录业务错误日志]
    System --> Log4[记录系统错误日志]
    
    Log1 --> Resp1[返回400响应]
    Log2 --> Resp2[返回400响应]
    Log3 --> Resp3[返回422响应]
    Log4 --> Resp4[返回500响应]
    
    Resp1 --> Client[客户端接收错误]
    Resp2 --> Client
    Resp3 --> Client
    Resp4 --> Client
    
    Client --> Retry{是否可重试}
    Retry -->|是| Wait[等待重试]
    Retry -->|否| End[结束流程]
    
    Wait --> Error
    
    %% 样式
    classDef errorNode fill:#ffebee,stroke:#c62828
    classDef logNode fill:#f1f8e9,stroke:#558b2f
    classDef respNode fill:#e8eaf6,stroke:#3f51b5
    classDef decisionNode fill:#fff8e1,stroke:#e65100
    
    class Error,Param,Sign,Business,System errorNode
    class Log1,Log2,Log3,Log4 logNode
    class Resp1,Resp2,Resp3,Resp4 respNode
    class Type,Retry decisionNode
```

## 6. 业务规则说明

### 6.1 验证规则
- **参数验证**: 必填字段不能为空，格式必须正确
- **时间戳验证**: 请求时间戳必须在当前时间±5分钟内
- **签名验证**: 使用HMAC-SHA256算法验证请求完整性
- **设备类型验证**: 只支持预定义的设备类型

### 6.2 业务规则
- **设备唯一性**: 同一个device_unique_id只能绑定一次
- **MQTT凭证**: 每个设备获得唯一的MQTT客户端ID和凭证
- **日志记录**: 每个步骤都记录详细的操作日志
- **错误处理**: 任何步骤失败都会终止流程并返回相应错误

### 6.3 安全规则
- **防重放攻击**: 通过时间戳和随机数防止请求重放
- **签名验证**: 确保请求来源的真实性和完整性
- **密钥管理**: 不同设备类型使用不同的签名密钥
- **传输安全**: 建议使用HTTPS加密传输

## 7. 测试场景

### 7.1 正常流程测试
- 新设备首次绑定
- 不同设备类型的绑定
- 并发绑定请求

### 7.2 异常流程测试
- 参数缺失或格式错误
- 签名验证失败
- 设备重复绑定
- 时间戳过期
- 系统异常处理

### 7.3 性能测试
- 签名计算性能
- 并发绑定处理能力
- 数据库操作性能
