import logging
import os
import time
from logging.handlers import TimedRotatingFileHandler
from datetime import datetime

class Logger:
    """统一日志管理模块"""
    
    _loggers = {}  # 存储不同模块的logger实例
    
    @staticmethod
    def get_logger(name, log_level=logging.INFO):
        """
        获取指定名称的logger实例
        
        Args:
            name: logger名称，通常使用模块名
            log_level: 日志级别
            
        Returns:
            Logger实例
        """
        # 如果已经创建过该logger，直接返回
        if name in Logger._loggers:
            return Logger._loggers[name]
        
        # 创建logger
        logger = logging.getLogger(name)
        logger.setLevel(log_level)
        
        # 避免重复添加handler
        if logger.handlers:
            return logger
        
        # 创建日志目录
        log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 获取当前日期
        current_date = datetime.now().strftime("%Y%m%d")
        
        # 创建日志文件名 (包含日期)
        log_file = os.path.join(log_dir, f"{name}{current_date}.log")
        
        # 创建文件处理器 - 按日期轮换
        file_handler = TimedRotatingFileHandler(
            log_file,
            when='midnight',  # 每天午夜轮换
            interval=1,       # 每隔1个单位轮换一次
            backupCount=3    # 保留3天的日志
        )
        file_handler.setLevel(log_level)
        # 不使用默认的文件命名后缀
        file_handler.suffix = ""
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        
        # 创建日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        # 保存logger实例
        Logger._loggers[name] = logger
        
        return logger

# 快捷函数，用于获取不同模块的logger
def get_logger(name, log_level=logging.INFO):
    return Logger.get_logger(name, log_level)

# 测试
if __name__ == "__main__":
    logger = get_logger("test")
    logger.info("这是一条信息日志")
    logger.warning("这是一条警告日志")
    logger.error("这是一条错误日志") 