#!/usr/bin/env python3
"""
统一线程管理器
提供线程池化、任务调度、异常恢复等功能
"""

import threading
import time
import queue
import logging
from concurrent.futures import ThreadPoolExecutor, Future
from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass
from enum import Enum
import weakref
try:
    from utils.logger import get_logger
except ImportError:
    # 如果无法导入，使用标准logging
    import logging
    def get_logger(name, level=logging.INFO):
        logger = logging.getLogger(name)
        logger.setLevel(level)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

class ThreadState(Enum):
    """线程状态"""
    IDLE = "idle"
    RUNNING = "running"
    ERROR = "error"
    STOPPING = "stopping"
    STOPPED = "stopped"

@dataclass
class Task:
    """任务定义"""
    name: str
    func: Callable
    args: tuple = ()
    kwargs: dict = None
    priority: TaskPriority = TaskPriority.NORMAL
    retry_count: int = 0
    max_retries: int = 3
    timeout: Optional[float] = None
    callback: Optional[Callable] = None
    created_time: float = None
    
    def __post_init__(self):
        if self.kwargs is None:
            self.kwargs = {}
        if self.created_time is None:
            self.created_time = time.time()

@dataclass
class ThreadInfo:
    """线程信息"""
    thread_id: str
    name: str
    state: ThreadState
    start_time: float
    last_activity: float
    task_count: int = 0
    error_count: int = 0
    current_task: Optional[str] = None

class ThreadManager:
    """统一线程管理器"""
    
    def __init__(self, config=None):
        self.logger = get_logger("thread_manager", logging.INFO)
        self.config = config or {}
        
        # 配置参数
        self.max_workers = self.config.get('max_workers', 8)
        self.core_workers = self.config.get('core_workers', 4)
        self.queue_size = self.config.get('queue_size', 1000)
        self.health_check_interval = self.config.get('health_check_interval', 30)
        # 增加任务超时时间，适应长时间运行的任务（如CO2控制循环）
        self.task_timeout = self.config.get('task_timeout', 7200)  # 2小时，适应CO2控制循环
        
        # 线程池
        self.executor = None
        self.running = False
        
        # 任务队列（优先级队列）
        self.task_queue = queue.PriorityQueue(maxsize=self.queue_size)
        
        # 线程信息跟踪
        self.thread_info = {}  # thread_id -> ThreadInfo
        self.thread_lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'tasks_submitted': 0,
            'tasks_completed': 0,
            'tasks_failed': 0,
            'tasks_retried': 0,
            'threads_created': 0,
            'threads_destroyed': 0
        }
        
        # 健康检查线程
        self.health_check_thread = None
        
        # 注册的定期任务
        self.scheduled_tasks = {}  # name -> (func, interval, last_run)
        
        self.logger.info("线程管理器初始化完成")
    
    def start(self):
        """启动线程管理器"""
        if self.running:
            self.logger.warning("线程管理器已在运行")
            return
        
        self.running = True
        
        # 创建线程池
        self.executor = ThreadPoolExecutor(
            max_workers=self.max_workers,
            thread_name_prefix="ThreadManager"
        )
        
        # 启动健康检查线程
        self.health_check_thread = threading.Thread(
            target=self._health_check_loop,
            name="ThreadManager-HealthCheck",
            daemon=True
        )
        self.health_check_thread.start()
        
        self.logger.info(f"线程管理器已启动，最大工作线程数: {self.max_workers}")
    
    def stop(self, timeout=30):
        """停止线程管理器"""
        if not self.running:
            return
        
        self.logger.info("正在停止线程管理器...")
        self.running = False
        
        # 停止接受新任务
        if self.executor:
            self.executor.shutdown(wait=False)
        
        # 等待健康检查线程结束
        if self.health_check_thread and self.health_check_thread.is_alive():
            self.health_check_thread.join(timeout=5)
        
        # 等待线程池关闭
        if self.executor:
            try:
                # 尝试使用timeout参数，如果不支持则回退到传统方式
                try:
                    self.executor.shutdown(wait=True, timeout=timeout)
                except TypeError:
                    # timeout参数不被支持，使用传统方式
                    self.logger.debug("ThreadPoolExecutor.shutdown()不支持timeout参数，使用传统方式")
                    self.executor.shutdown(wait=False)

                    # 手动实现超时等待机制
                    import time
                    start_time = time.time()
                    check_interval = 0.1

                    while time.time() - start_time < timeout:
                        # 检查是否还有活跃的线程
                        try:
                            # 使用更安全的方式检查线程状态
                            import threading
                            active_threads = [t for t in threading.enumerate()
                                            if t.name.startswith("ThreadManager") and t.is_alive()]

                            if not active_threads:
                                break  # 没有活跃的ThreadManager线程，退出等待

                            time.sleep(check_interval)
                        except Exception:
                            # 如果检查失败，直接等待一段时间后退出
                            time.sleep(check_interval)

                    # 检查是否超时
                    elapsed_time = time.time() - start_time
                    if elapsed_time >= timeout:
                        self.logger.warning(f"线程池关闭等待超时 ({elapsed_time:.1f}秒)")
                    else:
                        self.logger.debug(f"线程池已关闭 (耗时: {elapsed_time:.1f}秒)")

            except Exception as e:
                self.logger.error(f"关闭线程池时发生错误: {e}")
        
        self.logger.info("线程管理器已停止")
    
    def update_thread_activity(self, thread_id: int = None):
        """更新线程活动时间，用于长时间运行的任务"""
        if thread_id is None:
            thread_id = threading.get_ident()

        with self.thread_lock:
            if thread_id in self.thread_info:
                self.thread_info[thread_id].last_activity = time.time()

    def submit_task(self, name: str, func: Callable, *args,
                   priority: TaskPriority = TaskPriority.NORMAL,
                   timeout: Optional[float] = None,
                   max_retries: int = 3,
                   callback: Optional[Callable] = None,
                   **kwargs) -> Optional[Future]:
        """提交任务到线程池
        
        Args:
            name: 任务名称
            func: 要执行的函数
            *args: 函数参数
            priority: 任务优先级
            timeout: 任务超时时间
            max_retries: 最大重试次数
            callback: 完成回调函数
            **kwargs: 函数关键字参数
            
        Returns:
            Future对象，如果提交失败返回None
        """
        if not self.running or not self.executor:
            self.logger.error("线程管理器未运行，无法提交任务")
            return None
        
        try:
            task = Task(
                name=name,
                func=func,
                args=args,
                kwargs=kwargs,
                priority=priority,
                timeout=timeout or self.task_timeout,
                max_retries=max_retries,
                callback=callback
            )
            
            # 提交任务到线程池
            future = self.executor.submit(self._execute_task, task)
            
            self.stats['tasks_submitted'] += 1
            self.logger.debug(f"任务已提交: {name} (优先级: {priority.name})")
            
            return future
            
        except Exception as e:
            self.logger.error(f"提交任务失败: {name}, 错误: {e}")
            return None
    
    def submit_periodic_task(self, name: str, func: Callable, interval: float,
                           *args, **kwargs) -> bool:
        """提交定期执行的任务
        
        Args:
            name: 任务名称
            func: 要执行的函数
            interval: 执行间隔（秒）
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            是否成功注册
        """
        if name in self.scheduled_tasks:
            self.logger.warning(f"定期任务已存在: {name}")
            return False
        
        self.scheduled_tasks[name] = {
            'func': func,
            'args': args,
            'kwargs': kwargs,
            'interval': interval,
            'last_run': 0
        }
        
        self.logger.info(f"定期任务已注册: {name} (间隔: {interval}秒)")
        return True
    
    def cancel_periodic_task(self, name: str) -> bool:
        """取消定期任务"""
        if name in self.scheduled_tasks:
            del self.scheduled_tasks[name]
            self.logger.info(f"定期任务已取消: {name}")
            return True
        return False
    
    def _execute_task(self, task: Task) -> Any:
        """执行任务"""
        thread_id = threading.get_ident()
        thread_name = threading.current_thread().name
        
        # 更新线程信息
        with self.thread_lock:
            if thread_id not in self.thread_info:
                self.thread_info[thread_id] = ThreadInfo(
                    thread_id=str(thread_id),
                    name=thread_name,
                    state=ThreadState.RUNNING,
                    start_time=time.time(),
                    last_activity=time.time()
                )
                self.stats['threads_created'] += 1
            
            thread_info = self.thread_info[thread_id]
            thread_info.state = ThreadState.RUNNING
            thread_info.current_task = task.name
            thread_info.last_activity = time.time()
            thread_info.task_count += 1
        
        start_time = time.time()
        result = None
        
        try:
            self.logger.debug(f"开始执行任务: {task.name} (线程: {thread_name})")
            
            # 执行任务
            result = task.func(*task.args, **task.kwargs)
            
            # 更新统计
            self.stats['tasks_completed'] += 1
            execution_time = time.time() - start_time
            
            self.logger.debug(f"任务执行完成: {task.name} (耗时: {execution_time:.2f}s)")
            
            # 执行回调
            if task.callback:
                try:
                    task.callback(True, result, task)
                except Exception as e:
                    self.logger.error(f"任务回调执行失败: {task.name}, 错误: {e}")
            
        except Exception as e:
            self.logger.error(f"任务执行失败: {task.name}, 错误: {e}")
            
            # 更新错误统计
            with self.thread_lock:
                if thread_id in self.thread_info:
                    self.thread_info[thread_id].error_count += 1
                    self.thread_info[thread_id].state = ThreadState.ERROR
            
            self.stats['tasks_failed'] += 1
            
            # 重试逻辑
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                self.stats['tasks_retried'] += 1
                
                self.logger.info(f"任务重试: {task.name} (第{task.retry_count}次)")
                
                # 延迟重试
                retry_delay = min(2 ** task.retry_count, 60)  # 指数退避，最大60秒
                time.sleep(retry_delay)
                
                return self._execute_task(task)
            
            # 执行失败回调
            if task.callback:
                try:
                    task.callback(False, e, task)
                except Exception as cb_e:
                    self.logger.error(f"任务失败回调执行失败: {task.name}, 错误: {cb_e}")
            
            raise e
        
        finally:
            # 更新线程状态
            with self.thread_lock:
                if thread_id in self.thread_info:
                    self.thread_info[thread_id].state = ThreadState.IDLE
                    self.thread_info[thread_id].current_task = None
                    self.thread_info[thread_id].last_activity = time.time()
        
        return result
    
    def _health_check_loop(self):
        """健康检查循环"""
        self.logger.info("线程健康检查已启动")
        
        while self.running:
            try:
                current_time = time.time()
                
                # 检查线程健康状态
                with self.thread_lock:
                    dead_threads = []
                    for thread_id, info in self.thread_info.items():
                        # 检查线程是否长时间无响应
                        inactive_time = current_time - info.last_activity

                        # 对于长时间运行的任务（如CO2控制循环），使用更宽松的超时策略
                        if info.current_task and ('co2_control' in info.current_task or 'sensor_data_collection' in info.current_task):
                            # 长时间任务使用更长的超时时间（4小时）
                            timeout_threshold = 14400  # 4小时
                        else:
                            # 普通任务使用默认超时时间
                            timeout_threshold = self.task_timeout

                        if inactive_time > timeout_threshold:
                            self.logger.warning(f"线程长时间无响应: {info.name} (ID: {thread_id}), 无活动时间: {inactive_time:.1f}秒, 当前任务: {info.current_task}")
                            info.state = ThreadState.ERROR
                        elif inactive_time > self.task_timeout and info.state == ThreadState.RUNNING:
                            # 对于超过普通超时但未超过长任务超时的情况，只记录信息不标记为错误
                            if inactive_time % 300 < 30:  # 每5分钟记录一次，避免日志过多
                                self.logger.info(f"线程执行长时间任务: {info.name} (ID: {thread_id}), 运行时间: {inactive_time:.1f}秒, 任务: {info.current_task}")

                        # 清理已死亡的线程信息
                        if info.state == ThreadState.STOPPED:
                            dead_threads.append(thread_id)

                    # 清理死亡线程
                    for thread_id in dead_threads:
                        del self.thread_info[thread_id]
                        self.stats['threads_destroyed'] += 1
                
                # 执行定期任务
                self._execute_scheduled_tasks(current_time)
                
                # 等待下次检查
                time.sleep(self.health_check_interval)
                
            except Exception as e:
                self.logger.error(f"健康检查循环异常: {e}")
                time.sleep(self.health_check_interval)
        
        self.logger.info("线程健康检查已停止")
    
    def _execute_scheduled_tasks(self, current_time: float):
        """执行定期任务"""
        for name, task_info in list(self.scheduled_tasks.items()):
            try:
                if current_time - task_info['last_run'] >= task_info['interval']:
                    # 提交定期任务
                    self.submit_task(
                        name=f"scheduled_{name}",
                        func=task_info['func'],
                        *task_info['args'],
                        priority=TaskPriority.LOW,
                        **task_info['kwargs']
                    )
                    task_info['last_run'] = current_time
                    
            except Exception as e:
                self.logger.error(f"执行定期任务失败: {name}, 错误: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.thread_lock:
            thread_stats = {
                'active_threads': len([t for t in self.thread_info.values() 
                                     if t.state == ThreadState.RUNNING]),
                'idle_threads': len([t for t in self.thread_info.values() 
                                   if t.state == ThreadState.IDLE]),
                'error_threads': len([t for t in self.thread_info.values() 
                                    if t.state == ThreadState.ERROR]),
                'total_threads': len(self.thread_info)
            }
        
        stats = self.stats.copy()
        stats.update(thread_stats)
        stats['scheduled_tasks'] = len(self.scheduled_tasks)
        stats['queue_size'] = self.task_queue.qsize() if hasattr(self.task_queue, 'qsize') else 0
        
        return stats
    
    def get_thread_info(self) -> List[Dict[str, Any]]:
        """获取线程详细信息"""
        with self.thread_lock:
            return [
                {
                    'thread_id': info.thread_id,
                    'name': info.name,
                    'state': info.state.value,
                    'start_time': info.start_time,
                    'last_activity': info.last_activity,
                    'task_count': info.task_count,
                    'error_count': info.error_count,
                    'current_task': info.current_task,
                    'uptime': time.time() - info.start_time
                }
                for info in self.thread_info.values()
            ]


# 全局线程管理器实例
_thread_manager = None

def get_thread_manager(config=None):
    """获取全局线程管理器实例"""
    global _thread_manager
    if _thread_manager is None:
        _thread_manager = ThreadManager(config)
    return _thread_manager

def start_thread_manager(config=None):
    """启动全局线程管理器"""
    manager = get_thread_manager(config)
    manager.start()
    return manager

def update_thread_activity():
    """更新当前线程的活动时间（全局函数）"""
    global _thread_manager
    if _thread_manager:
        _thread_manager.update_thread_activity()

def stop_thread_manager():
    """停止全局线程管理器"""
    global _thread_manager
    if _thread_manager:
        _thread_manager.stop()
        _thread_manager = None
