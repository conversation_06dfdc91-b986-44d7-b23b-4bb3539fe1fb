#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API测试接口 v2.0
升级版测试服务器，支持离线模式、设备绑定测试、压力测试等新功能
保持与v1版本的兼容性
"""

import os
import sys
import json
import time
import threading
import logging
import psutil
from datetime import datetime
from flask import Flask, request, jsonify, Response, send_from_directory
from flask_socketio import SocketIO, emit
from flask_cors import CORS
from functools import wraps
import random
import uuid

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入项目模块
from utils.logger import get_logger

# 初始化Flask应用
app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), 'static'))
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# 设置日志
logger = get_logger("test_server_v2", logging.INFO)

# 全局变量
system_instance = None
monitor_thread = None
stop_monitor = threading.Event()

# API密钥列表（继承v1配置）
API_KEYS = ["test_key_123", "dev_key_456", "v2_test_key"]

# 测试环境配置
TEST_CONFIG = {
    "offline_mode": True,           # 默认启用离线模式
    "mock_platform_reply": True,   # 模拟平台回复
    "data_retention_days": 7,      # 测试数据保留7天
    "test_data_prefix": "TEST-",   # 测试数据前缀
    "performance_monitoring": True  # 性能监控
}

# 简单的认证装饰器
def require_api_key(f):
    """API密钥验证装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        if api_key and api_key in API_KEYS:
            return f(*args, **kwargs)
        return jsonify({"status": "error", "message": "无效的API密钥"}), 401
    return decorated

def log_api_call(f):
    """API调用日志装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        start_time = time.time()
        endpoint = request.endpoint
        method = request.method
        
        logger.info(f"API调用开始: {method} {endpoint}")
        
        try:
            result = f(*args, **kwargs)
            duration = time.time() - start_time
            logger.info(f"API调用完成: {method} {endpoint}, 耗时: {duration:.3f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"API调用失败: {method} {endpoint}, 耗时: {duration:.3f}s, 错误: {e}")
            raise
    return decorated

# ==================== 基础接口 ====================

@app.route('/api/v2/health', methods=['GET'])
@require_api_key
@log_api_call
def health_check():
    """健康检查接口"""
    return jsonify({
        "status": "success",
        "version": "2.0",
        "message": "API测试接口 v2.0 运行正常",
        "timestamp": time.time(),
        "config": {
            "offline_mode": TEST_CONFIG["offline_mode"],
            "mock_platform_reply": TEST_CONFIG["mock_platform_reply"]
        }
    })

@app.route('/api/v2/config', methods=['GET', 'POST'])
@require_api_key
@log_api_call
def test_config():
    """测试配置管理接口"""
    if request.method == 'GET':
        return jsonify({
            "status": "success",
            "config": TEST_CONFIG,
            "timestamp": time.time()
        })
    
    elif request.method == 'POST':
        try:
            new_config = request.get_json()
            if new_config:
                TEST_CONFIG.update(new_config)
                logger.info(f"测试配置已更新: {new_config}")
                return jsonify({
                    "status": "success",
                    "message": "配置更新成功",
                    "config": TEST_CONFIG,
                    "timestamp": time.time()
                })
            else:
                return jsonify({"status": "error", "message": "无效的配置数据"}), 400
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return jsonify({"status": "error", "message": f"更新配置失败: {e}"}), 500

# ==================== 离线数据测试接口 ====================

@app.route('/api/v2/data/sensor/offline', methods=['POST'])
@require_api_key
@log_api_call
def test_sensor_data_offline():
    """离线传感器数据测试"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "缺少数据"}), 400
        
        # 添加测试标识
        test_data = {
            "device_id": TEST_CONFIG["test_data_prefix"] + "SENSOR",
            "data": data,
            "timestamp": time.time(),
            "test_mode": "offline"
        }
        
        # 模拟数据处理
        if system_instance and hasattr(system_instance, 'sensor_collector'):
            # 这里可以调用传感器收集器的测试方法
            pass
        
        logger.info(f"离线传感器数据测试: {test_data}")
        
        return jsonify({
            "status": "success",
            "message": "离线传感器数据测试完成",
            "test_data": test_data,
            "timestamp": time.time()
        })
        
    except Exception as e:
        logger.error(f"离线传感器数据测试失败: {e}")
        return jsonify({"status": "error", "message": f"测试失败: {e}"}), 500

@app.route('/api/v2/data/co2/offline', methods=['POST'])
@require_api_key
@log_api_call
def test_co2_data_offline():
    """离线CO2状态测试"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "缺少数据"}), 400
        
        # 添加测试标识
        test_data = {
            "device_id": TEST_CONFIG["test_data_prefix"] + "CO2",
            "data": data,
            "timestamp": time.time(),
            "test_mode": "offline"
        }
        
        logger.info(f"离线CO2状态测试: {test_data}")
        
        return jsonify({
            "status": "success",
            "message": "离线CO2状态测试完成",
            "test_data": test_data,
            "timestamp": time.time()
        })
        
    except Exception as e:
        logger.error(f"离线CO2状态测试失败: {e}")
        return jsonify({"status": "error", "message": f"测试失败: {e}"}), 500

@app.route('/api/v2/data/check/offline', methods=['POST'])
@require_api_key
@log_api_call
def test_device_check_offline():
    """离线设备自检测试"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "缺少数据"}), 400
        
        # 添加测试标识
        test_data = {
            "device_id": TEST_CONFIG["test_data_prefix"] + "CHECK",
            "data": data,
            "timestamp": time.time(),
            "test_mode": "offline"
        }
        
        logger.info(f"离线设备自检测试: {test_data}")
        
        return jsonify({
            "status": "success",
            "message": "离线设备自检测试完成",
            "test_data": test_data,
            "timestamp": time.time()
        })
        
    except Exception as e:
        logger.error(f"离线设备自检测试失败: {e}")
        return jsonify({"status": "error", "message": f"测试失败: {e}"}), 500

@app.route('/api/v2/data/mosquito/offline', methods=['POST'])
@require_api_key
@log_api_call
def test_mosquito_data_offline():
    """离线蚊子检测测试"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "缺少数据"}), 400
        
        # 添加测试标识
        test_data = {
            "device_id": TEST_CONFIG["test_data_prefix"] + "MOSQUITO",
            "data": data,
            "timestamp": time.time(),
            "test_mode": "offline"
        }
        
        logger.info(f"离线蚊子检测测试: {test_data}")
        
        return jsonify({
            "status": "success",
            "message": "离线蚊子检测测试完成",
            "test_data": test_data,
            "timestamp": time.time()
        })
        
    except Exception as e:
        logger.error(f"离线蚊子检测测试失败: {e}")
        return jsonify({"status": "error", "message": f"测试失败: {e}"}), 500

# ==================== 增强监控接口 ====================

@app.route('/api/v2/monitor/threads/detail', methods=['GET'])
@require_api_key
@log_api_call
def monitor_threads_detail():
    """详细线程状态监控"""
    try:
        import threading
        
        threads_info = []
        for thread in threading.enumerate():
            thread_info = {
                "name": thread.name,
                "ident": thread.ident,
                "is_alive": thread.is_alive(),
                "daemon": thread.daemon
            }
            threads_info.append(thread_info)
        
        return jsonify({
            "status": "success",
            "data": {
                "thread_count": threading.active_count(),
                "threads": threads_info
            },
            "timestamp": time.time()
        })
        
    except Exception as e:
        logger.error(f"线程监控失败: {e}")
        return jsonify({"status": "error", "message": f"监控失败: {e}"}), 500

def run_test_server_v2(host='localhost', port=8081, system=None):
    """启动测试服务器 v2.0"""
    global system_instance
    system_instance = system
    
    logger.info(f"启动API测试接口 v2.0: {host}:{port}")
    logger.info(f"离线模式: {TEST_CONFIG['offline_mode']}")
    logger.info(f"模拟平台回复: {TEST_CONFIG['mock_platform_reply']}")
    
    try:
        socketio.run(app, host=host, port=port, debug=False, allow_unsafe_werkzeug=True)
    except Exception as e:
        logger.error(f"启动测试服务器失败: {e}")
        raise

if __name__ == '__main__':
    run_test_server_v2()
