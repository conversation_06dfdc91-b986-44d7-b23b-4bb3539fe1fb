# MQTT配置
mqtt:
  # 设备绑定配置
  device_bind_url: "https://vsp.aixxc.com/api/v1/device/bind"  # 替换为实际的API服务器地址
  device_type: "mosquito"  # 设备类型
  hmac_key: "MQ2025_7f8e9d6c5b4a3f2e1d0c9b8a7f6e5d4c3b2a1f0e9d8c7b6a5f4e3d2c1b0a9f8e7d6c"  # 用于签名的密钥
  credential_retry_interval: 30  # 凭证获取初始重试间隔（秒）
  credential_max_retry_interval: 3600  # 凭证获取最大重试间隔（秒）
  credential_max_retry_attempts: 10  # 凭证获取最大尝试次数
  credential_expiry_days: 30  # 凭证过期天数，超过此天数将重新获取凭证
  # 连接模式配置
  enable_mqtt_connection: true  # 是否启用实际MQTT连接
  
  # 连接设置
  connection_timeout: 30        # 连接超时时间（秒）
  keep_alive: 60                # MQTT保活时间（秒）
  
  # 调试设置
  verbose_logging: false        # 是否启用详细日志
  log_queue_status: true        # 是否记录队列状态
  queue_check_interval: 300     # 队列状态记录间隔（秒）
  archive_interval: 600         # 数据归档间隔（秒）
  
  # 主题配置
  publish_topic: "/xxc/attr/sensor/{device_id}"
  subscribe_topic: "/xxc/once/sensor/{device_id}/reply"
  # 以下错误主题配置已不再使用，错误通过自检主题上报
  # error_topic: "/xxc/attr/error/{device_id}"
  # error_reply_topic: "/xxc/once/error/{device_id}/reply"
  # CO2控制器主题配置
  co2_status_topic: "/xxc/event/co2/{device_id}"
  co2_reply_topic: "/xxc/once/co2/{device_id}/reply"
  # 设备自检主题配置
  check_topic: "/xxc/attr/check/{device_id}"
  check_reply_topic: "/xxc/once/check/{device_id}/reply"
  # 设备开关机和重启主题配置
  devopen_topic: "/xxc/event/devopen/{device_id}"
  devopen_reply_topic: "/xxc/once/devopen/{device_id}/reply"
  devclose_topic: "/xxc/event/devclose/{device_id}"
  devclose_reply_topic: "/xxc/once/devclose/{device_id}/reply"
  devrestart_topic: "/xxc/event/devrestart/{device_id}"
  devrestart_reply_topic: "/xxc/once/devrestart/{device_id}/reply"
  # 组件控制主题配置
  swsensor_topic: "/xxc/event/swsensor/{device_id}"
  swsensor_reply_topic: "/xxc/once/swsensor/{device_id}/reply"
  # 蚊子检测主题配置
  mosquito_topic: "/xxc/attr/mosquito/{device_id}"
  mosquito_reply_topic: "/xxc/once/mosquito/{device_id}/reply"
  # 蚊子检测文件监控配置
  mosquito_detection:
    enabled: true
    shared_data_path: "/home/<USER>/shared_data"
    file_check_interval: 5  # 文件检查间隔（秒）
    max_retry_count: 3      # 最大重试次数
  # 时间间隔配置
  publish_interval: 600  # 数据上传间隔（秒）
  message_upload_interval: 1  # 消息上传间隔（秒），防止平台并发问题
  batch_size: 5  # 每次上传的数据批次大小
  check_batch_size: 20  # 自检数据批次大小，加快处理速度
  error_check_interval: 60  # 错误检查间隔（秒）
  confirmation_timeout: 5  # 消息确认超时时间（秒）
  # 连接管理配置
  reconnect_base_interval: 5  # 重连基础间隔（秒）
  reconnect_max_interval: 60  # 最大重连间隔（秒）
  reconnect_max_attempts: 0  # 最大重连尝试次数，0表示无限重试
  network_check_interval: 10  # 网络检测间隔（秒）
  # 内存队列配置
  sensor_queue_size: 100  # 传感器数据队列大小
  error_queue_size: 50    # 错误数据队列大小
  co2_queue_size: 20      # CO2状态队列大小
  check_queue_size: 50    # 设备自检队列大小
  mosquito_queue_size: 50 # 蚊子检测数据队列大小
  # 消息去重配置
  message_expiry: 3600    # 消息ID过期时间（秒）
  max_message_cache: 1000 # 最大消息ID缓存数量
   
# 串口配置
serial:
  port: "/dev/ttyTHS1"  # 所有传感器共用的串口
  baudrate: 4800
  timeout: 1
  
# 传感器配置
sensors:
  cth:  # 温湿度CO2传感器
    device_address: 0x01
    # 数据质量检验阈值
    temperature_min: 0  # 最低温度 (°C)
    temperature_max: 60   # 最高温度 (°C)
    humidity_min: 0       # 最低湿度 (%)
    humidity_max: 95     # 最高湿度 (%)
    co2_min: 0            # 最低CO2浓度 (ppm)
    co2_max: 50000         # 最高CO2浓度 (ppm)
  gps:  # GPS模块
    device_address: 0x02
  wind_speed:  # 风速传感器
    device_address: 0x04
    min: 0      # 最低风速 (m/s)
    max: 60     # 最高风速 (m/s)

# 数据采集配置
data_collection:
  interval: 60  # 数据采集间隔（秒）

# 数据存储配置
data_storage:
  data_dir: "data"
  sensors_dir: "sensors"
  devices_dir: "devices"
  sensor_data_file: "sensor_data.json"
  sensor_errors_file: "sensor_errors.json"
  sensor_data_archive_file: "sensor_data_archive.json"
  sensor_errors_archive_file: "sensor_errors_archive.json"
  co2_errors_file: "co2_errors.json"
  co2_errors_archive_file: "co2_errors_archive.json"
  credentials_file: "credentials.json"  # MQTT凭证保存文件
  # 设备自检数据目录（只保留目录配置）
  check_dir: "check"
  # 数据限制
  max_pending: 1000  # 待上传数据最大条数
  max_history: 10000  # 历史数据最大条数

# 重试机制配置
retry:
  base_interval: 5      # 基础重试间隔（秒）
  max_interval: 300     # 最大重试间隔（秒）
  temp_failure_count: 3 # 临时故障重试次数阈值
  perm_failure_count: 10 # 永久故障重试次数阈值

# CO2控制器配置
co2_controller:
  # 硬件配置
  pins:
    fans: [13, 15]
    pumps: [16, 21]
    heater: 19
    stepper: [22, 24, 26, 29]
  
  # 时间配置（分钟）
  timing:
    fan_switch_interval: 20
    adsorption_duration:
      round1: 110  # 1小时50分钟
      round2: 110  # 1小时50分钟
      round3: 110  # 1小时50分钟
      round4: 190  # 3小时10分钟
    heating_duration: 25
    pump_switch_interval: 2
    release_duration: 55
  
  # 异常处理配置
  retry_count: 3

# 设备自检配置
device_health:
  # 自检间隔（秒）
  check_interval: 1800  # 默认30分钟
  # 传感器数据过期时间（秒）
  sensor_data_expiry: 600  # 10分钟

  # 退避策略配置
  backoff:
    initial_interval: 60      # 初始退避间隔（秒）
    max_interval: 1800        # 最大退避间隔（秒）
    multiplier: 2             # 退避倍数

  # 状态检测配置
  state_detection:
    same_state_window: 300    # 相同状态时间窗口（秒）
    recovery_check_interval: 30  # 恢复检查间隔（秒）

  # 上报控制
  reporting:
    immediate_scenarios:      # 立即上报场景
      - "first_permanent_failure"
      - "recovery_to_normal"
      - "scheduled_check"
    backoff_scenarios:        # 退避上报场景
      - "repeated_failure"
      - "permanent_failure_retry"

  # 资源使用阈值
  cpu_threshold: 90  # CPU使用率阈值（百分比）
  memory_threshold: 10  # 可用内存阈值（百分比）
  disk_threshold: 10  # 可用磁盘空间阈值（百分比）
  gpu_threshold: 90  # GPU使用率阈值（百分比）
  # 电池相关
  battery_threshold: 20  # 电池低电量阈值（百分比）
  battery_min_voltage: 10.5  # 最低电压（伏特）
  battery_max_voltage: 12.0  # 最高电压（伏特）
  # ADS1115配置
  i2c_bus: 7  # I2C总线号
  ads_address: 0x48  # ADS1115地址
  battery_retry_count: 3  # 电池电压读取重试次数
  battery_retry_delay: 0.1  # 电池电压读取重试间隔（秒）

# 缓存配置
cache:
  # 传感器数据缓存配置
  max_sensor_cache_size: 1000  # 最大传感器数据缓存条数
  max_error_cache_size: 500    # 最大错误数据缓存条数
  cache_max_size: 50           # 批量刷新阈值
  flush_interval: 5            # 缓存刷新间隔（秒）

  # 内存监控配置
  memory_monitor:
    check_interval: 30         # 内存检查间隔（秒）
    warning_threshold: 80      # 内存告警阈值（%）
    critical_threshold: 90     # 内存严重告警阈值（%）
    max_memory_mb: 1024        # 最大内存限制（MB）

# 异步文件I/O配置
async_file_io:
  max_queue_size: 1000         # 最大队列大小
  worker_threads: 2            # 工作线程数
  batch_size: 20               # 批量写入大小
  flush_interval: 3            # 批量刷新间隔（秒）
  enable_compression: false    # 是否启用压缩（暂未实现）

# 线程管理配置
thread_manager:
  max_workers: 8               # 最大工作线程数
  core_workers: 4              # 核心工作线程数
  queue_size: 1000             # 任务队列大小
  health_check_interval: 30    # 健康检查间隔（秒）
  task_timeout: 7200           # 任务超时时间（秒），适应长时间运行的任务（如CO2控制循环）
  enable_monitoring: true      # 是否启用线程监控

