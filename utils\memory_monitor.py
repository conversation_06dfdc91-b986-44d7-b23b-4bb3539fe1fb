#!/usr/bin/env python3
"""
内存监控模块
监控系统内存使用情况，实现内存压力告警和缓存清理
"""

import time
import threading
import psutil
import gc
from utils.logger import get_logger
import logging

class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, config=None):
        self.logger = get_logger("memory_monitor", logging.INFO)
        self.config = config or {}
        
        # 配置参数
        self.check_interval = self.config.get('check_interval', 30)  # 检查间隔（秒）
        self.warning_threshold = self.config.get('warning_threshold', 80)  # 告警阈值（%）
        self.critical_threshold = self.config.get('critical_threshold', 90)  # 严重阈值（%）
        self.max_memory_mb = self.config.get('max_memory_mb', 1024)  # 最大内存限制（MB）
        
        # 运行状态
        self.running = False
        self.monitor_thread = None
        
        # 统计信息
        self.stats = {
            'current_memory_mb': 0,
            'max_memory_mb': 0,
            'warning_count': 0,
            'critical_count': 0,
            'gc_count': 0,
            'last_check_time': 0
        }
        
        # 回调函数列表
        self.warning_callbacks = []
        self.critical_callbacks = []
        self.cleanup_callbacks = []
        
        self.logger.info("内存监控器初始化完成")
    
    def add_warning_callback(self, callback):
        """添加内存告警回调函数"""
        self.warning_callbacks.append(callback)
    
    def add_critical_callback(self, callback):
        """添加内存严重告警回调函数"""
        self.critical_callbacks.append(callback)
    
    def add_cleanup_callback(self, callback):
        """添加内存清理回调函数"""
        self.cleanup_callbacks.append(callback)
    
    def get_memory_usage(self):
        """获取当前内存使用情况"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            # 获取系统内存使用率
            system_memory = psutil.virtual_memory()
            system_usage_percent = system_memory.percent
            
            return {
                'process_memory_mb': memory_mb,
                'system_memory_percent': system_usage_percent,
                'system_available_mb': system_memory.available / 1024 / 1024
            }
        except Exception as e:
            self.logger.error(f"获取内存使用情况失败: {e}")
            return None
    
    def check_memory_pressure(self):
        """检查内存压力"""
        memory_info = self.get_memory_usage()
        if not memory_info:
            return
        
        current_memory = memory_info['process_memory_mb']
        system_percent = memory_info['system_memory_percent']
        
        # 更新统计信息
        self.stats['current_memory_mb'] = current_memory
        self.stats['last_check_time'] = time.time()
        
        if current_memory > self.stats['max_memory_mb']:
            self.stats['max_memory_mb'] = current_memory
        
        # 检查进程内存使用
        memory_percent = (current_memory / self.max_memory_mb) * 100
        
        if memory_percent >= self.critical_threshold or system_percent >= self.critical_threshold:
            self.stats['critical_count'] += 1
            self.logger.error(f"内存使用严重告警: 进程={current_memory:.1f}MB({memory_percent:.1f}%), 系统={system_percent:.1f}%")
            self._trigger_critical_callbacks(memory_info)
            self._force_cleanup()
            
        elif memory_percent >= self.warning_threshold or system_percent >= self.warning_threshold:
            self.stats['warning_count'] += 1
            self.logger.warning(f"内存使用告警: 进程={current_memory:.1f}MB({memory_percent:.1f}%), 系统={system_percent:.1f}%")
            self._trigger_warning_callbacks(memory_info)
        
        else:
            self.logger.debug(f"内存使用正常: 进程={current_memory:.1f}MB({memory_percent:.1f}%), 系统={system_percent:.1f}%")
    
    def _trigger_warning_callbacks(self, memory_info):
        """触发告警回调"""
        for callback in self.warning_callbacks:
            try:
                callback(memory_info)
            except Exception as e:
                self.logger.error(f"执行告警回调失败: {e}")
    
    def _trigger_critical_callbacks(self, memory_info):
        """触发严重告警回调"""
        for callback in self.critical_callbacks:
            try:
                callback(memory_info)
            except Exception as e:
                self.logger.error(f"执行严重告警回调失败: {e}")
    
    def _force_cleanup(self):
        """强制内存清理"""
        try:
            # 触发清理回调
            for callback in self.cleanup_callbacks:
                try:
                    callback()
                except Exception as e:
                    self.logger.error(f"执行清理回调失败: {e}")
            
            # 强制垃圾回收
            collected = gc.collect()
            self.stats['gc_count'] += 1
            self.logger.info(f"强制垃圾回收完成，回收对象数: {collected}")
            
        except Exception as e:
            self.logger.error(f"强制内存清理失败: {e}")
    
    def _monitor_loop(self):
        """内存监控循环"""
        self.logger.info("内存监控线程已启动")
        
        while self.running:
            try:
                self.check_memory_pressure()
                time.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f"内存监控循环异常: {e}")
                time.sleep(self.check_interval)
        
        self.logger.info("内存监控线程已停止")
    
    def start(self):
        """启动内存监控"""
        if self.running:
            self.logger.warning("内存监控已在运行")
            return
        
        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("内存监控已启动")
    
    def stop(self):
        """停止内存监控"""
        if not self.running:
            return
        
        self.running = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("内存监控已停止")
    
    def get_stats(self):
        """获取监控统计信息"""
        return self.stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'current_memory_mb': 0,
            'max_memory_mb': 0,
            'warning_count': 0,
            'critical_count': 0,
            'gc_count': 0,
            'last_check_time': 0
        }
        self.logger.info("内存监控统计信息已重置")


# 全局内存监控实例
_memory_monitor = None

def get_memory_monitor(config=None):
    """获取全局内存监控实例"""
    global _memory_monitor
    if _memory_monitor is None:
        _memory_monitor = MemoryMonitor(config)
    return _memory_monitor

def start_memory_monitoring(config=None):
    """启动全局内存监控"""
    monitor = get_memory_monitor(config)
    monitor.start()
    return monitor

def stop_memory_monitoring():
    """停止全局内存监控"""
    global _memory_monitor
    if _memory_monitor:
        _memory_monitor.stop()
