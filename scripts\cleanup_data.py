#!/usr/bin/env python3
"""
数据清理脚本
清理损坏的JSON文件，重新初始化数据目录
"""

import os
import json
import shutil
from datetime import datetime

def backup_existing_data():
    """备份现有数据"""
    print("🔄 备份现有数据...")
    
    # 创建备份目录
    backup_dir = f"data/backup/{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    # 备份数据目录
    data_dirs = [
        "data/sensors",
        "data/check", 
        "data/devices",
        "data/mqtt"
    ]
    
    for data_dir in data_dirs:
        if os.path.exists(data_dir):
            backup_path = os.path.join(backup_dir, os.path.basename(data_dir))
            try:
                shutil.copytree(data_dir, backup_path)
                print(f"✅ 已备份: {data_dir} -> {backup_path}")
            except Exception as e:
                print(f"⚠️ 备份失败: {data_dir}, 错误: {e}")
    
    print(f"📁 备份完成，位置: {backup_dir}")
    return backup_dir

def clean_data_files():
    """清理损坏的数据文件"""
    print("🧹 清理损坏的数据文件...")
    
    # 需要清理的文件列表
    files_to_clean = [
        "data/sensors/current/sensor_data.json",
        "data/sensors/current/sensor_errors.json",
        "data/check/current/device_check.json",
        "data/devices/current/co2_status.json"
    ]
    
    for file_path in files_to_clean:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"🗑️ 已删除损坏文件: {file_path}")
            except Exception as e:
                print(f"❌ 删除失败: {file_path}, 错误: {e}")
        else:
            print(f"ℹ️ 文件不存在: {file_path}")

def initialize_data_files():
    """初始化数据文件"""
    print("🔧 初始化数据文件...")
    
    # 确保目录存在
    directories = [
        "data/sensors/current",
        "data/sensors/history", 
        "data/check/current",
        "data/check/history",
        "data/devices/current",
        "data/devices/history",
        "data/mqtt"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 目录已创建: {directory}")
    
    # 初始化JSON文件
    json_files = {
        "data/sensors/current/sensor_data.json": [],
        "data/sensors/current/sensor_errors.json": [],
        "data/check/current/device_check.json": [],
        "data/devices/current/co2_status.json": []
    }
    
    for file_path, initial_data in json_files.items():
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(initial_data, f, indent=2, ensure_ascii=False)
            print(f"✅ 已初始化: {file_path}")
        except Exception as e:
            print(f"❌ 初始化失败: {file_path}, 错误: {e}")

def verify_data_integrity():
    """验证数据完整性"""
    print("🔍 验证数据完整性...")
    
    json_files = [
        "data/sensors/current/sensor_data.json",
        "data/sensors/current/sensor_errors.json", 
        "data/check/current/device_check.json",
        "data/devices/current/co2_status.json"
    ]
    
    all_valid = True
    
    for file_path in json_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"✅ JSON格式正确: {file_path} (包含 {len(data)} 条记录)")
        except Exception as e:
            print(f"❌ JSON格式错误: {file_path}, 错误: {e}")
            all_valid = False
    
    return all_valid

def main():
    """主函数"""
    print("=" * 50)
    print("🧹 数据清理脚本")
    print("=" * 50)
    
    try:
        # 1. 备份现有数据
        backup_dir = backup_existing_data()
        
        # 2. 清理损坏文件
        clean_data_files()
        
        # 3. 初始化数据文件
        initialize_data_files()
        
        # 4. 验证数据完整性
        if verify_data_integrity():
            print("\n🎉 数据清理完成！")
            print("✅ 所有JSON文件格式正确")
            print("✅ 数据目录结构完整")
            print(f"📁 备份位置: {backup_dir}")
            print("\n现在可以重新运行测试了！")
        else:
            print("\n❌ 数据清理失败，请检查错误信息")
            return False
            
    except Exception as e:
        print(f"\n❌ 清理过程中发生错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
