# 设备绑定 API 接口文档

## 概述

设备绑定系统提供了安全的设备注册和MQTT凭证分发功能，支持多种类型的IoT设备接入。

## 接口列表

### 1. 设备绑定接口

**接口地址：** `POST /api/v1/device/bind`

**接口描述：** 设备向系统请求绑定，获取设备ID和MQTT连接配置

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| device_unique_id | string | 是 | 设备唯一标识 | "DEV2025001" |
| device_type | string | 是 | 设备类型 | "mosquito" |
| mac_address | string | 否 | MAC地址 | "AA:BB:CC:DD:EE:FF" |
| serial_number | string | 否 | 序列号 | "SN202500001" |
| timestamp | string | 是 | Unix时间戳(秒) | "1752222285" |
| nonce | string | 是 | 随机数 | "nonce_328000" |
| signature | string | 是 | 请求签名 | "9ea595e6fd73..." |

#### 请求示例

```json
{
    "device_unique_id": "DEV2025001",
    "device_type": "mosquito",
    "mac_address": "AA:BB:CC:DD:EE:FF",
    "serial_number": "SN202500001",
    "timestamp": "1752222285",
    "nonce": "nonce_328000",
    "signature": "9ea595e6fd73029366824140453b783209ae50f9f4617116acb97a5307fde770"
}
```

#### 响应参数

**成功响应 (200):**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | int | 响应码，0表示成功 |
| message | string | 响应消息 |
| data.device_id | int64 | 分配的设备ID |
| data.mqtt_config | object | MQTT连接配置 |
| data.mqtt_config.client_id | string | MQTT客户端ID |
| data.mqtt_config.username | string | MQTT用户名 |
| data.mqtt_config.password | string | MQTT密码 |
| data.mqtt_config.broker | string | MQTT服务器地址 |
| data.mqtt_config.port | int | MQTT端口 |
| data.mqtt_config.protocol | string | 连接协议 |

#### 成功响应示例

```json
{
    "code": 0,
    "message": "success",
    "data": {
        "device_id": 1001,
        "mqtt_config": {
            "client_id": "GID-SD-X1@@@1001",
            "username": "DeviceCredential|LTAI5tEiWHjWvp7QfMuEJUV5|post-cn-qzm4c7k9d04",
            "password": "Y2FsY3VsYXRlZF9wYXNzd29yZF9oYXNo",
            "broker": "post-cn-qzm4c7k9d04.mqtt.aliyuncs.com",
            "port": 1883,
        }
    }
}
```

#### 错误响应

**参数错误 (400):**
```json
{
    "code": 20002,
    "message": "设备唯一标识不能为空"
}
```

**设备已绑定 (422):**
```json
{
    "code": 422,
    "message": "设备已绑定，设备ID: 1001"
}
```

**签名验证失败 (400):**
```json
{
    "code": 20002,
    "message": "请求签名验证失败"
}
```

### 2. 设备绑定日志查询接口

**接口地址：** `GET /api/v1/device/bind/logs`

**接口描述：** 查询设备绑定过程的详细日志

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| device_unique_id | string | 否 | 设备唯一标识 | "DEV2025001" |
| device_type | string | 否 | 设备类型 | "mosquito_monitor" |
| step | int | 否 | 绑定步骤(1-7) | 3 |
| status | int | 否 | 绑定状态(1-4) | 2 |
| start_time | string | 否 | 开始时间 | "2025-01-11T00:00:00Z" |
| end_time | string | 否 | 结束时间 | "2025-01-11T23:59:59Z" |
| page | int | 否 | 页码，默认1 | 1 |
| page_size | int | 否 | 每页数量，默认10，最大100 | 10 |

#### 响应示例

```json
{
    "code": 0,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "device_unique_id": "DEV2025001",
                "device_type": "mosquito_monitor",
                "step": "接收设备请求",
                "status": "成功",
                "request_data": "{\"device_unique_id\":\"DEV2025001\",...}",
                "response_data": "",
                "message": "接收设备请求",
                "created_at": "2025-01-11T15:20:45Z"
            }
        ],
        "total": 7
    }
}
```

## 业务流程

### 设备绑定流程图

```mermaid
flowchart TD
    A[设备发起绑定请求] --> B[接收设备请求]
    B --> C{参数验证}
    C -->|验证失败| D[返回参数错误]
    C -->|验证成功| E{签名验证}
    E -->|验证失败| F[返回签名错误]
    E -->|验证成功| G{检查设备唯一性}
    G -->|设备已存在| H[返回设备已绑定]
    G -->|设备不存在| I[创建设备记录]
    I -->|创建失败| J[返回创建失败]
    I -->|创建成功| K[注册MQTT凭证]
    K -->|注册失败| L[返回MQTT注册失败]
    K -->|注册成功| M[生成响应数据]
    M --> N[返回绑定成功]

    %% 样式定义
    classDef successNode fill:#d4edda,stroke:#155724,color:#155724
    classDef errorNode fill:#f8d7da,stroke:#721c24,color:#721c24
    classDef processNode fill:#cce5ff,stroke:#004085,color:#004085
    classDef decisionNode fill:#fff3cd,stroke:#856404,color:#856404

    %% 应用样式
    class N successNode
    class D,F,H,J,L errorNode
    class B,I,K,M processNode
    class C,E,G decisionNode
```

### 系统架构交互流程图

```mermaid
sequenceDiagram
    participant Device as IoT设备
    participant Handler as Handler层
    participant Logic as Logic层
    participant DB as 数据库
    participant MQTT as 阿里云MQTT
    participant Log as 日志系统

    Device->>Handler: POST /api/v1/device/bind
    Note over Device,Handler: 包含签名的设备信息

    Handler->>Log: 记录接收请求步骤
    Handler->>Handler: 参数验证
    alt 参数验证失败
        Handler->>Log: 记录验证失败
        Handler->>Device: 返回参数错误
    else 参数验证成功
        Handler->>Log: 记录验证成功
        Handler->>Handler: 签名验证
        alt 签名验证失败
            Handler->>Log: 记录签名失败
            Handler->>Device: 返回签名错误
        else 签名验证成功
            Handler->>Log: 记录签名成功
            Handler->>Logic: 调用绑定逻辑

            Logic->>Log: 记录检查设备唯一性
            Logic->>DB: 查询设备是否存在
            DB->>Logic: 返回查询结果

            alt 设备已存在
                Logic->>Log: 记录设备已绑定
                Logic->>Handler: 返回设备已绑定错误
                Handler->>Device: 返回设备已绑定
            else 设备不存在
                Logic->>Log: 记录创建设备步骤
                Logic->>DB: 创建设备记录
                DB->>Logic: 返回设备信息
                Logic->>Log: 记录创建成功

                Logic->>Log: 记录MQTT注册步骤
                Logic->>MQTT: 注册设备凭证
                MQTT->>Logic: 返回MQTT凭证
                Logic->>Log: 记录MQTT注册成功

                Logic->>DB: 更新设备MQTT信息
                Logic->>Log: 记录生成响应步骤
                Logic->>Handler: 返回绑定结果
                Handler->>Device: 返回成功响应
            end
        end
    end
```

### 设备绑定状态转换图

```mermaid
stateDiagram-v2
    [*] --> 接收请求: 设备发起绑定
    接收请求 --> 参数验证: 解析请求参数
    参数验证 --> 签名验证: 参数格式正确
    参数验证 --> 参数错误: 参数格式错误
    签名验证 --> 设备检查: 签名验证通过
    签名验证 --> 签名错误: 签名验证失败
    设备检查 --> 创建设备: 设备不存在
    设备检查 --> 设备已绑定: 设备已存在
    创建设备 --> MQTT注册: 设备创建成功
    创建设备 --> 创建失败: 设备创建失败
    MQTT注册 --> 绑定成功: MQTT凭证获取成功
    MQTT注册 --> MQTT失败: MQTT凭证获取失败

    参数错误 --> [*]
    签名错误 --> [*]
    设备已绑定 --> [*]
    创建失败 --> [*]
    MQTT失败 --> [*]
    绑定成功 --> [*]

    note right of 绑定成功: 返回设备ID和MQTT配置
    note right of 参数错误: 返回400错误
    note right of 签名错误: 返回400错误
    note right of 设备已绑定: 返回422错误
```

### 数据流图

```mermaid
graph LR
    A[设备请求数据] --> B[参数验证器]
    B --> C[签名验证器]
    C --> D[设备管理器]
    D --> E[数据库]
    D --> F[MQTT服务]
    F --> G[阿里云MQTT]
    E --> H[设备记录]
    G --> I[MQTT凭证]
    H --> J[响应构建器]
    I --> J
    J --> K[设备绑定响应]

    %% 数据标注
    A -.->|device_unique_id<br/>device_type<br/>timestamp<br/>nonce<br/>signature| B
    B -.->|验证结果| C
    C -.->|验证通过| D
    D -.->|设备信息| E
    D -.->|客户端ID| F
    F -.->|凭证请求| G
    E -.->|设备ID<br/>设备编码| H
    G -.->|AccessKey<br/>Secret<br/>ClientID| I
    H -.->|设备信息| J
    I -.->|MQTT配置| J
    J -.->|device_id<br/>mqtt_config| K
```

### 详细业务流程说明

1. **接收设备请求** - 解析HTTP请求参数
2. **参数验证** - 验证必填字段和格式
3. **签名验证** - 验证请求签名防止伪造
4. **检查设备唯一性** - 确保设备未重复绑定
5. **创建设备** - 在系统中创建设备记录
6. **注册MQTT凭证** - 向阿里云申请设备MQTT凭证
7. **生成响应** - 返回设备ID和MQTT配置

### 绑定步骤常量

| 步骤 | 常量值 | 描述 |
|------|--------|------|
| BindStepReceiveRequest | 1 | 接收设备请求 |
| BindStepValidateParams | 2 | 参数验证 |
| BindStepVerifySignature | 3 | 签名验证 |
| BindStepCheckDeviceUnique | 4 | 检查设备唯一性 |
| BindStepCreateDevice | 5 | 创建设备 |
| BindStepRegisterMQTT | 6 | 注册MQTT凭证 |
| BindStepGenerateResponse | 7 | 生成响应 |

### 绑定状态常量

| 状态 | 常量值 | 描述 |
|------|--------|------|
| BindStatusPending | 1 | 进行中 |
| BindStatusSuccess | 2 | 成功 |
| BindStatusFailed | 3 | 失败 |
| BindStatusTimeout | 4 | 超时 |

## 支持的设备类型

- `mosquitor` - 蚊媒监测器

## 安全机制

1. **时间戳验证** - 请求必须在5分钟内有效，防止重放攻击
2. **签名验证** - 使用HMAC-SHA256算法验证请求完整性
3. **设备类型密钥** - 不同设备类型使用不同的签名密钥
4. **随机数** - 防止重复请求攻击

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| 0 | 成功 |
| 20002 | 参数错误 |
| 422 | 设备已绑定 |
| 500 | 服务器内部错误 |
