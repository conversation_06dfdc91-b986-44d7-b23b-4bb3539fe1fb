import serial
import struct
import math
import time
from utils.config_loader import load_config

class GPSBDSModule:
    def __init__(self, port=None, baudrate=None, timeout=None):
        # 如果未提供参数，则从配置文件加载
        if port is None or baudrate is None or timeout is None:
            config = load_config()
            serial_config = config['serial']
            port = port or serial_config['port']
            baudrate = baudrate or serial_config['baudrate']
            timeout = timeout or serial_config['timeout']
            
        self.ser = serial.Serial(port, baudrate, timeout=timeout)
        if not self.ser.is_open:
            self.ser.open()
        time.sleep(0.1)

    def calculate_crc(self, data):
        crc = 0xFFFF
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x0001:
                    crc >>= 1
                    crc ^= 0xA001
                else:
                    crc >>= 1
        return crc

    def read_registers(self, device_address, start_addr, count):
        request_frame = bytearray([
            device_address,
            0x03,
            (start_addr >> 8) & 0xFF,
            start_addr & 0xFF,
            (count >> 8) & 0xFF,
            count & 0xFF
        ])
        crc = self.calculate_crc(request_frame)
        request_frame.extend([crc & 0xFF, (crc >> 8) & 0xFF])
        self.ser.flushInput()
        self.ser.write(request_frame)

        response_len = 5 + 2 * count
        response = self.ser.read(response_len)
        if len(response) != response_len:
            print("响应长度错误")
            return None

        received_crc = struct.unpack('<H', response[-2:])[0]
        calculated_crc = self.calculate_crc(response[:-2])
        if received_crc != calculated_crc:
            print("CRC校验失败")
            return None

        return response[3:-2]

    def read_float(self, data):
        return struct.unpack('>f', data)[0]

    def read_int16(self, data):
        return struct.unpack('>h', data)[0]

    def wgs84_to_bd09(self, lng, lat):
        # WGS84 -> GCJ02
        x_pi = math.pi * 3000.0 / 180.0
        z = math.sqrt(lng * lng + lat * lat) + 0.00002 * math.sin(lat * x_pi)
        theta = math.atan2(lat, lng) + 0.000003 * math.cos(lng * x_pi)
        gcj_lng = z * math.cos(theta) + 0.0065
        gcj_lat = z * math.sin(theta) + 0.006

        # GCJ02 -> BD09
        z = math.sqrt(gcj_lng * gcj_lng + gcj_lat * gcj_lat) + 0.00002 * math.sin(gcj_lat * x_pi)
        theta = math.atan2(gcj_lat, gcj_lng) + 0.000003 * math.cos(gcj_lng * x_pi)
        bd_lng = z * math.cos(theta) + 0.0065
        bd_lat = z * math.sin(theta) + 0.006

        return bd_lng, bd_lat

    def read_position(self, device_address=None):
        # 如果未提供设备地址，则从配置文件加载
        if device_address is None:
            config = load_config()
            device_address = config['sensors']['gps']['device_address']
            
        # 读取经度方向 + 经度
        lng_data = self.read_registers(device_address, 0x0001, 3)
        if not lng_data:
            return None
        longitude = self.read_float(lng_data[2:6])

        # 读取纬度方向 + 纬度
        lat_data = self.read_registers(device_address, 0x0004, 3)
        if not lat_data:
            return None
        latitude = self.read_float(lat_data[2:6])

        # 坐标转换
        bd_lng, bd_lat = self.wgs84_to_bd09(longitude, latitude)

        return {
            "bd_longitude": bd_lng,
            "bd_latitude": bd_lat
        }

    def close(self):
        self.ser.close()

# 示例使用
if __name__ == "__main__":
    gps = GPSBDSModule()
    try:
        data = gps.read_position()
        if data:
            print("经度(BD09):", data["bd_longitude"])
            print("纬度(BD09):", data["bd_latitude"])
        else:
            print("读取失败")
    finally:
        gps.close()